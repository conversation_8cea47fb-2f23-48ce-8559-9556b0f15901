name: supabase

on:
  pull_request:
  push:
    branches:
      - main
      - dev

jobs:
  check-schema:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: website
    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v1
      - run: supabase init
      - run: supabase db start
      - name: Verify generated types match Postgres schema
        run: |
          supabase gen types typescript --local > schema.gen.ts
          if ! git diff --ignore-space-at-eol --exit-code --quiet schema.gen.ts; then
            echo "Detected uncommitted changes after build. See status below:"
            git diff
            exit 1
          fi
  migrate:
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: website
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN  }}
      SUPABASE_DB_PASSWORD: ${{ github.ref_name == 'main' && secrets.SUPABASE_DB_PASSWORD || secrets.SUPABASE_DEV_DB_PASSWORD }}
      PROJECT_ID: ${{ github.ref_name == 'main' && secrets.SUPABASE_PROJECT_ID || secrets.SUPABASE_DEV_PROJECT_ID }}

    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v1
      - run: supabase link --project-ref $PROJECT_ID
      - run: supabase db push --include-all
