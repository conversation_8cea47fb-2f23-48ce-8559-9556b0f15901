# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Start development server:**
```bash
bun dev  # Runs on port 3033 with turbo mode
```

**Package management:**
```bash
bun install
bun add <package>
bun remove <package>
```

**Build and deploy:**
```bash
bun run build
bun start
```

**Linting and testing:**
```bash
bun run lint
bun run test  # Uses Jest
```

**Database operations:**
```bash
bun gen                  # Generate TypeScript types from Supabase schema
bun run create:migration # Create new Supabase migration
bun run migrate         # Run migrations and regenerate types
```

**Development tools:**
```bash
bun run ngrok           # Expose local server via ngrok
```

## Project Architecture

### Tech Stack
- **Runtime:** Bun (package manager and runtime)
- **Framework:** Next.js 15 (App Router) with React 19
- **Styling:** Tailwind CSS with custom design system
- **Database:** Supabase with PostgreSQL
- **Authentication:** Privy for wallet authentication
- **Blockchain:** Abstract (L2), zkSync, with wagmi/viem
- **State Management:** TanStack Query for server state
- **Analytics:** Mixpanel, LaunchDarkly feature flags
- **UI Components:** Radix UI primitives with custom styling

### Core Game Types
The platform supports two main games:
- **Death Race:** Grid-based gambling game with progressive multipliers
- **Laser Party:** Multi-tile selection variant with complex grid logic

### Key Directories

**`/app`** - Next.js App Router structure
- `/(death-race)/` - Main game pages and admin tools
- `/api/` - API routes for games, leaderboards, and blockchain operations
- `/laser/` - Laser Party game pages

**`/components`** - React components organized by feature
- `/death-race/` - Death Race game components
- `/laser-party/` - Laser Party game components  
- `/admin/` - Admin dashboard components
- `/common/` - Shared UI components
- `/ui/` - Base design system components

**`/lib`** - Core business logic and utilities
- `/blockchain/` - Smart contract ABIs and blockchain interfaces
- `/client/` - Client-side utilities (analytics, queries, wallet)
- `/server/` - Server-side utilities (auth, database, analytics)
- `/types/` - TypeScript type definitions
- `/utils/` - Game logic, provably fair algorithms, Abstract blockchain integration
- `/hooks/` - Custom React hooks including feature flags

**`/supabase/migrations`** - Database schema migrations with comprehensive game state management

### Database Schema
The database uses Supabase with complex triggers and functions for:
- Game state management across multiple game types
- Real-time leaderboards with daily/weekly cycles
- Referral system with custom bonus percentages
- Points/rewards system integration
- Comprehensive user statistics tracking

### Game State Architecture
Games flow through states: `pending_onchain` → `creation_failed`/`active` → `lost`/`cashout_pending` → `won`
- Provably fair verification using commitment hashes and game seeds
- Blockchain integration for on-chain game creation and payouts
- Server-side game logic with client-side state synchronization
- Error handling for failed transactions and manual intervention

### Authentication & Security
- Wallet-based authentication via Privy
- Server-side API key validation
- Content Security Policy headers
- Environment variable validation on startup

### Feature Flag System
Uses LaunchDarkly for:
- Game availability toggling (`useAvailableGames`)
- A/B testing capabilities
- Gradual feature rollouts

## Environment Requirements
- Node.js >= 20.0.0
- Bun runtime and package manager
- Required environment variables (validated in `/lib/server/constants.ts`):
  - `APP_ID`
  - `ABSTRACT_RPC_URL` 
  - `ABSCAN_API_KEY`
  - `NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS`
  - `SERVER_SIGNER_PRIVATE_KEY`

## Testing Strategy
- Jest configuration for unit testing
- Focus on game logic verification and provably fair algorithms
- Database function testing for complex statistical calculations