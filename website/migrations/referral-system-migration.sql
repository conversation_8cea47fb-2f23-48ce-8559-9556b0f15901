-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_address TEXT NOT NULL UNIQUE,
    referral_code TEXT NOT NULL UNIQUE,
    referred_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    total_referral_bonuses BIGINT DEFAULT 0, -- In lamports
    total_referral_bonuses_paid BIGINT DEFAULT 0, -- In lamports
    CONSTRAINT fk_referred_by FOREIGN KEY (referred_by) REFERENCES users(id)
);

-- Create referral bonuses table
CREATE TABLE referral_bonuses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID NOT NULL REFERENCES users(id),
    referred_id UUID NOT NULL REFERENCES users(id),
    game_id UUID NOT NULL REFERENCES games(id),
    bonus_amount BIGINT NOT NULL, -- In lamports
    status TEXT NOT NULL CHECK (status IN ('pending', 'paid')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    paid_at TIMESTAMPTZ,
    CONSTRAINT fk_game FOREIGN KEY (game_id) REFERENCES games(id)
);

-- Create indexes for performance
CREATE INDEX idx_users_wallet ON users(wallet_address);
CREATE INDEX idx_users_referral_code ON users(referral_code);
CREATE INDEX idx_users_referred_by ON users(referred_by);
CREATE INDEX idx_bonuses_referrer ON referral_bonuses(referrer_id);
CREATE INDEX idx_bonuses_referred ON referral_bonuses(referred_id);
CREATE INDEX idx_bonuses_status ON referral_bonuses(status);

-- Add RLS policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_bonuses ENABLE ROW LEVEL SECURITY;

-- Users can read their own data and the referral codes of all users
CREATE POLICY "Users can read their own data" ON users
    FOR SELECT USING (auth.uid()::text = wallet_address);

CREATE POLICY "Anyone can read referral codes" ON users
    FOR SELECT USING (true);

-- Users can only read their own referral bonuses
CREATE POLICY "Users can read their own referral bonuses" ON referral_bonuses
    FOR SELECT USING (
        auth.uid()::text IN (
            SELECT wallet_address FROM users WHERE id = referrer_id
        )
    );

-- Only the service role can insert/update referral bonuses
CREATE POLICY "Service role can manage referral bonuses" ON referral_bonuses
    FOR ALL USING (auth.role() = 'service_role'); 