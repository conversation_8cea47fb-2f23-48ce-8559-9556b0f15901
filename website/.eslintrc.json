{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "prettier"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-empty-interface": "off", "react/react-in-jsx-scope": "off", "no-empty-pattern": "off", "react-hooks/exhaustive-deps": "error", "@typescript-eslint/no-explicit-any": "off", "react/prop-types": "off"}}