import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)'],
        mono: ['var(--font-geist-mono)'],
        fancy: ['var(--font-silkscreen)'],
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        gc: {
          '50': 'hsl(var(--gc-50))',
          '100': 'hsl(var(--gc-100))',
          '200': 'hsl(var(--gc-200))',
          '300': 'hsl(var(--gc-300))',
          '400': 'hsl(var(--gc-400))',
          '500': 'hsl(var(--gc-500))',
          '600': 'hsl(var(--gc-600))',
          '700': 'hsl(var(--gc-700))',
          '800': 'hsl(var(--gc-800))',
          '900': 'hsl(var(--gc-900))',
          '950': 'hsl(var(--gc-950))',
        },
      },
      keyframes: {
        shake: {
          '0%, 100%': {
            transform: 'translateX(0)',
          },
          '25%': {
            transform: 'translateX(-2px)',
          },
          '75%': {
            transform: 'translateX(2px)',
          },
        },
        fadeIn: {
          '0%': {
            opacity: '0',
          },
          '100%': {
            opacity: '1',
          },
        },
        rowsAppear: {
          '0%': {
            opacity: '0',
            transform: 'translateY(2rem)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideDown: {
          '0%': {
            opacity: '0',
            transform: 'translateY(-2rem)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(2rem)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideRight: {
          '0%': {
            opacity: '0',
            transform: 'translateX(-2rem)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        slideLeft: {
          '0%': {
            opacity: '0',
            transform: 'translateX(2rem)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        'laugh': {
          '10%, 30%, 50%': {
            transform: 'translateY(6px)',
          },
          '20%, 40%': {
            transform: 'translateY(2px)',
          },
          '60%, 80%': {
            transform: 'translateY(0px)',
          },
          '70%, 90%': {
            transform: 'translateY(-3px)',
          },
        },
        'mouth-laugh': {
          '10%, 30%, 50%': {
            transform: 'scale(0.8)',
            top: '45%',
          },
          '20%, 40%': {
            transform: 'scale(0.9)',
            top: '45%',
          },
          '60%, 80%': {
            transform: 'scale(1)',
            top: '50%',
          },
          '70%': {
            transform: 'scale(1.05)',
            top: '50%',
          },
          '90%': {
            transform: 'scale(1.03)',
            top: '50%',
          },
        },
        'head-shake': {
          '0%': { transform: 'translateY(0)' },
          '20%': { transform: 'translateY(-8px)' },
          '50%': { transform: 'translateY(0)' },
        },
        'spin': {
          '0%, 5%': { transform: 'rotate(0deg)' },
          '60%, 100%': { transform: 'rotate(720deg)' },
        },
        'face-circle': {
          '0%': { transform: 'rotate(0deg) translateY(8px) rotate(0deg)' },
          '60%, 100%':   { transform: 'rotate(-360deg) translateY(8px) rotate(360deg)' },
        },
        'mouth-flat': { 
          '0%': { transform: 'scaleX(1)' },
          '10%, 30%': { transform: 'scaleX(0.55)' },
          '40%, 100%': { transform: 'scaleX(1)' },
        },
        'mouth-stagger': {
          '0%, 2%': { opacity: '0' },
          '1%': { opacity: '1' },
          '50%, 100%': { opacity: '0' },
        },
      },
        animation: {
        shake: 'shake 0.15s ease-in-out infinite',
        'fade-in': 'fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'rows-appear': 'rowsAppear 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-down': 'slideDown 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-up': 'slideUp 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-right': 'slideRight 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'slide-left': 'slideLeft 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'laugh': 'laugh 1.4s linear infinite',
        'mouth-laugh': 'mouth-laugh 1.4s linear infinite',
        'head-shake': 'head-shake 3s cubic-bezier(0.42, 0, 0.58, 1) infinite',
        'spin': 'spin 3s ease-in-out infinite',
        'face-circle': 'face-circle 3s cubic-bezier(0.4, 0, 0.2, 1) infinite forwards',
        'mouth-flat': 'mouth-flat 3s ease-in-out infinite',
        'mouth-stagger': 'mouth-stagger 4s linear infinite backwards',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;