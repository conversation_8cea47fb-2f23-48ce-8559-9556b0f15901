import 'server-only';
import { Address, Hex } from 'viem';
import { ABSTRACT_VOTING_CONTRACT_ADDRESS as VOTING_CONTRACT_ADDRESS } from '@/lib/constants';

if (!process.env.APP_ID) {
  throw new Error('APP_ID environment variable is not set.');
}
if (!process.env.ABSTRACT_RPC_URL) {
  throw new Error('ABSTRACT_RPC_URL environment variable is not set.');
}
if (!process.env.ABSCAN_API_KEY) {
  throw new Error('ABSCAN_API_KEY environment variable is not set.');
}
if (!process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS) {
  throw new Error('NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS environment variable is not set.');
}
if (!process.env.SERVER_SIGNER_PRIVATE_KEY) {
  throw new Error('SERVER_SIGNER_PRIVATE_KEY environment variable is not set.');
}
if (!process.env.SERVER_SIGNER_PRIVATE_KEY_2) {
  throw new Error('SERVER_SIGNER_PRIVATE_KEY_2 environment variable is not set.');
}

export const APP_ID = process.env.APP_ID; // Get App ID from environment
export const ABSTRACT_RPC_URL = process.env.ABSTRACT_RPC_URL; // Get RPC URL from environment
export const ABSCAN_API_KEY = process.env.ABSCAN_API_KEY; // Get API Key from environment
export const ABSTRACT_CONTRACT_ADDRESS = process.env
  .NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as Address; // Get Contract Address from environment
export const ABSTRACT_OLD_CONTRACT_ADDRESS = process.env
  .NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS as Address; // Get Contract Address from environment
export const SERVER_SIGNER_PRIVATE_KEY = process.env.SERVER_SIGNER_PRIVATE_KEY as Hex; // Get Server Signer Private Key from environment
export const SERVER_SIGNER_PRIVATE_KEY_2 = process.env.SERVER_SIGNER_PRIVATE_KEY_2 as Hex; // Get Server Signer Private Key 2 from environment

// --- abstract client constants ---
export const ABSTRACT_POLLING_INTERVAL = 200; // 200ms
export const ABSTRACT_GAS_LIMIT = BigInt(800000); // using 800k noticed l2 gas limit is around 500k
export const ABSTRACT_TX_TIMEOUT = 20000; // 20 seconds
export const ABSTRACT_GAS_BUFFER = BigInt(150); // 150% more gas

// Abstract Voting contract
export const ABSTRACT_VOTING_CONTRACT_ADDRESS = VOTING_CONTRACT_ADDRESS;
