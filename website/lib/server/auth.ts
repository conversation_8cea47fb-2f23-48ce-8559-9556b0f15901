import 'server-only';
import { PrivyClient } from '@privy-io/server-auth';
import { UnauthorizedError } from './errors';
import { cookies } from 'next/headers';

if (!process.env.NEXT_PUBLIC_PRIVY_APP_ID || !process.env.PRIVY_APP_SECRET) {
  throw new Error('PRIVY_APP_ID and PRIVY_API_SECRET must be set');
}

export const privyClient = new PrivyClient(
  process.env.NEXT_PUBLIC_PRIVY_APP_ID, // Your Privy App ID
  process.env.PRIVY_APP_SECRET // Your Privy App Secret
);

// This is a copy of the CrossAppAccountWithMetadata interface since its not exported from @privy-io/server-auth
interface CrossAppAccountCopy {
  /** The user's embedded wallet address(es) from the provider app */
  embeddedWallets: {
    address: string;
  }[];
  smartWallets: {
    address: string;
  }[];
}

/**
 * Verify the token and return the wallet address
 * @returns wallet address in lowercase
 */
export const verifyToken = async (): Promise<{
  walletAddress: string;
  error: null | UnauthorizedError;
}> => {
  // --- get cookie
  const cookieStore = await cookies();
  const idToken = cookieStore.get('privy-id-token')?.value;
  if (!idToken) {
    return { walletAddress: '', error: new UnauthorizedError('missing jwt') };
  }

  // --- signature and payload validations
  const user = await privyClient.getUserFromIdToken(idToken);
  if (!user) {
    return { walletAddress: '', error: new UnauthorizedError('invalid jwt') };
  }
  if (!user.linkedAccounts || user.linkedAccounts.length < 1) {
    return { walletAddress: '', error: new UnauthorizedError('missing linked accounts') };
  }
  const linkedAccount = user.linkedAccounts[0] as CrossAppAccountCopy;
  if (!linkedAccount.smartWallets || linkedAccount.smartWallets.length < 1) {
    return { walletAddress: '', error: new UnauthorizedError('missing smart wallets') };
  }

  // --- return wallet address
  return {
    walletAddress: linkedAccount.smartWallets[0].address.toLowerCase(),
    error: null,
  };
};
