import 'server-only';
import mixpanel from 'mixpanel';

const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN!;

class ServerAnalytics {
  private mp = mixpanel.init(MIXPANEL_TOKEN);

  private trackEvent(event: string, userId: string, properties: Record<string, any> = {}) {
    this.mp.track(event, { ...properties, distinct_id: userId });
  }

  // Server-side events only
  gameStarted(userId: string, props?: Record<string, any>) {
    this.trackEvent('Game Started', userId, props);
  }
  gameCashedOut(userId: string, props?: Record<string, any>) {
    this.trackEvent('Game Cashed Out', userId, props);
  }
  gameBusted(userId: string, props?: Record<string, any>) {
    this.trackEvent('Game Busted', userId, props);
  }
  savedUsername(userId: string, props?: Record<string, any>) {
    this.trackEvent('Username Saved', userId, props);
  }
  createdSessionKey(userId: string, props?: Record<string, any>) {
    this.trackEvent('Session Key Created', userId, props);
  }
  accountCreated(userId: string, props?: Record<string, any>) {
    this.trackEvent('Account Created', userId, props);
  }
  errorOccurred(userId: string, props?: Record<string, any>) {
    this.trackEvent('Error', userId, props);
  }

  setUserProfile(userId: string, username: string, walletAddress: string) {
    this.mp.people.set(userId, {
      $name: username,
      username,
      wallet_address: walletAddress,
    });
  }

  // Separate method specifically for setting referral relationship (call once when user is created)
  setUserReferralInfo(userId: string, referrerId: string) {
    this.mp.people.set(userId, {
      referred_by: referrerId,
    });
  }
}

export const analytics = new ServerAnalytics();

export const setUserProfile = (userId: string, username: string, walletAddress: string) =>
  analytics.setUserProfile(userId, username, walletAddress);

export const setUserReferralInfo = (userId: string, referrerId: string) =>
  analytics.setUserReferralInfo(userId, referrerId);
