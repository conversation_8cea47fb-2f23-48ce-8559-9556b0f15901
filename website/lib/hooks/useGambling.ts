import { useAccount, useBalance, usePublicClient, useWalletClient } from 'wagmi';
import { CONTRACT_ADDRESS } from '../client/constants';
import {
  DEATH_RACE_MAX_BET_PERCENTAGE,
  DEATH_RACE_MAX_PROFIT_PERCENTAGE,
  GAME_TYPES,
  GameType,
} from '../constants';
import { useGetUser, useGameBalance } from '../client/queries';
import { abstractClientConfig } from '../utils/abstract/config';
import { Hex } from 'viem';
import DeathRaceGameABI from '@/lib/blockchain/abi/DeathFun.json';
import { DisplayRow } from '@/components/death-race/types';
import { decimalToFraction } from '@/lib/utils';
import { useMemo } from 'react';
import { ALGO_VERSION } from '../constants';

export const useGambling = (gameType: (typeof GAME_TYPES)[number]) => {
  // ------------------------------------------------ HOOKS
  // Get game-specific balance
  const {
    data: gameBalanceData,
    isPending: isGameBalanceLoading,
    refetch: refetchGameBalance,
  } = useGameBalance(gameType);
  const { effectiveBalance, hasMaxBalance } = gameBalanceData ?? {};

  const {
    data: contractBalance,
    isPending: isPotLoading,
    refetch,
  } = useBalance({
    address: CONTRACT_ADDRESS,
    chainId: Number(abstractClientConfig.chainId),
    query: {
      enabled: !hasMaxBalance,
    },
  });

  const potBalance = effectiveBalance ?? contractBalance?.value;

  const { data: userData } = useGetUser();
  const { walletAddress } = userData ?? {};
  const {
    data: userBalance,
    isPending: isUserBalanceLoading,
    refetch: refetchUserBalance,
  } = useBalance({
    address: walletAddress as `0x${string}`,
    chainId: Number(abstractClientConfig.chainId),
  });
  const publicClient = usePublicClient({ chainId: Number(abstractClientConfig.chainId) });
  const { data: walletClient } = useWalletClient({
    account: walletAddress as `0x${string}`,
    chainId: Number(abstractClientConfig.chainId),
  });
  const { address } = useAccount();

  // ------------------------------------------------ HANDLERS
  const sendCreateGameTransaction = async (params: {
    preliminaryGameId: string;
    gameSeedHash: Hex;
    serverSignature: Hex;
    betAmountWei: bigint;
    rows: DisplayRow[];
    deadline: number;
  }) => {
    if (!publicClient || !address || !walletClient || !walletAddress)
      throw new Error('Public client or wallet address not found');

    // --- create transaction
    const { request } = await publicClient.simulateContract({
      address: process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as Hex,
      abi: DeathRaceGameABI.abi,
      functionName: 'createGame',
      args: [
        params.preliminaryGameId,
        params.gameSeedHash,
        ALGO_VERSION,
        params.rows.map((row) => row.tiles),
        params.deadline,
        params.serverSignature,
      ],
      value: params.betAmountWei,
      account: walletAddress as `0x${string}`,
    });

    // --- send transaction
    const txHash = await walletClient.writeContract(request);

    return txHash;
  };

  const { numerator: numeratorDeathRace, denominator: denominatorDeathRace } = decimalToFraction(
    DEATH_RACE_MAX_BET_PERCENTAGE
  );
  const maxBetAmount = ((potBalance || BigInt(0)) * numeratorDeathRace) / denominatorDeathRace;
  const { numerator: numeratorDeathRaceProfit, denominator: denominatorDeathRaceProfit } =
    decimalToFraction(DEATH_RACE_MAX_PROFIT_PERCENTAGE);
  const maxPayoutAmount =
    ((potBalance || BigInt(0)) * numeratorDeathRaceProfit) / denominatorDeathRaceProfit;

  return {
    potBalance: potBalance || BigInt(0),
    userBalance: userBalance?.value || BigInt(0),
    isPotLoading: isPotLoading || isGameBalanceLoading,
    isUserBalanceLoading,
    maxBetAmount,
    maxPayoutAmount,
    refetchPotBalance: refetch,
    refetchGameBalance,
    refetchUserBalance: refetchUserBalance,
    sendCreateGameTransaction,
    hasMaxBalance: gameBalanceData?.hasMaxBalance || false,
  };
};
