import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { ActiveDeathRaceGame, ActiveGame, CurrentDeathRaceGame } from '@/lib/types/game';
import {
  getActiveGameOptions,
  getRowConfigOptions,
  useGetActiveGame,
  useGetRowConfig,
  useGetUser,
  getUserOptions,
} from '../client/queries';
import { useGambling } from './useGambling';
import { GameRow } from '@/components/death-race/types';
import { abstractClientConfig as config } from '../utils/abstract/config';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  DEATH_RACE_TOTAL_ROWS,
  DEATH_RACE_MIN_TILES,
  DEATH_RACE_MAX_TILES,
  DEATH_RACE_GAME_TYPE,
} from '../constants';
import { DisplayRow } from '@/components/death-race/types';
import { calculateRowMultipliers } from '../utils/game';
import { decimalToFraction } from '@/lib/utils';
import { useSounds } from '@/lib/hooks/useSounds';
import { useAbstractSession } from './useAbstractSession';
import { LaserPartyGameState } from '../types/laserParty';

interface UseGameReturn {
  currentGame: ActiveDeathRaceGame['currentGame'] | undefined;
  previousGame: ActiveDeathRaceGame['previousGame'] | undefined;
  isLoading: boolean;
  error: string | null;
  payoutData: { isPending: boolean; amount?: number; error?: string };
  processingTileIndex: number | null;
  rowConfig: DisplayRow[];
  isFreshRowConfig: boolean;
  isSelectingTile: boolean;
  selectTile: (tileIndex: number) => Promise<void>;
  cashOut: () => Promise<void>;
  setCashoutToastId: (toastId: string | number) => void;
  shuffleRows: (customRows?: GameRow[]) => void;
}

export function useGame(): UseGameReturn {
  // ------------------------------------------------ STATE
  const cashoutToastIdRef = useRef<string | number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [payoutData, setPayoutData] = useState<{
    isPending: boolean;
    amount?: number;
    error?: string;
  }>({ isPending: false });
  const [processingTileIndex, setProcessingTileIndex] = useState<number | null>(null);

  // ------------------------------------------------ HOOKS
  const { data: rowConfigData } = useGetRowConfig();
  const { rows: rowConfig, fresh: isFreshRowConfig } = rowConfigData ?? {};
  const { data: activeGame } = useGetActiveGame<ActiveDeathRaceGame>(DEATH_RACE_GAME_TYPE);
  const { currentGame, previousGame } = activeGame ?? {};
  const { data: userData } = useGetUser();
  const { walletAddress, sessionConfig } = userData ?? {};
  const { maxPayoutAmount } = useGambling(DEATH_RACE_GAME_TYPE);
  const queryClient = useQueryClient();
  const { playCashOut } = useSounds();
  const { handleCreateSessionAndUpdateUser, isValidSession } = useAbstractSession();

  // ------------------------------------------------ MUTATIONS
  const { mutate: callSelectTile, isPending: isSelectingTile } = useMutation({
    mutationKey: ['selectTile'],
    mutationFn: async ({
      tileIndex,
      currentVersion,
    }: {
      tileIndex: number;
      currentVersion: number;
    }) => {
      const response = await fetch(`/api/games/${currentGame?.id}/select-tile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          game_type: DEATH_RACE_GAME_TYPE,
          tileIndex,
          version: currentVersion,
        }),
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to select tile', { cause: (await response.json()).error });
      }
      const data = await response.json();
      const { currentRowIndex, currentRow, status, finalMultiplier, version } = data;

      // If the game is over (won or lost), invalidate the query to get fresh data with game seed
      if (status === 'won' || status === 'lost') {
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
        });
        queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
        return data;
      }

      // Otherwise update the cache as before
      queryClient.setQueryData(
        getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
        (oldData: ActiveGame | undefined) => {
          if (!oldData) return oldData;
          const previousCurrentRowIndex = oldData.currentGame?.currentRowIndex;
          const newSelectedTiles = Array.isArray(oldData.currentGame.selectedTiles)
            ? [...oldData.currentGame.selectedTiles]
            : [];
          if (previousCurrentRowIndex !== null) {
            oldData.currentGame.rows[previousCurrentRowIndex] = currentRow as DisplayRow;
            newSelectedTiles[previousCurrentRowIndex] = tileIndex;
          }
          return {
            ...oldData,
            currentGame: {
              ...oldData.currentGame,
              currentRowIndex,
              rows: oldData.currentGame.rows,
              status,
              finalMultiplier,
              selectedTiles: newSelectedTiles,
              version,
            },
          };
        }
      );
      return data;
    },
    onError: (err) => {
      if (err.cause === 'Game already updated') {
        toast.error('Game already updated');
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
        });
        return;
      }
      if (err.cause === 'Cannot select tile after cashout has been initiated') {
        toast.error('Cannot select tile after cashout has been initiated');
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
        });
        return;
      }
      // Error already toasted inside the block or is a signature reset
      if (!(err instanceof Error && err.message === 'Signature expired or reused.')) {
        const message = err instanceof Error ? err.message : 'Failed to select tile';
        setError(message);
        // Avoid double-toasting if API call failed
        if (!message.includes('Failed to select tile')) {
          toast.error('Operation failed', {
            description: message,
          });
        }
      }
    },
    onSettled: () => {
      setIsLoading(false);
      setProcessingTileIndex(null);
    },
  });

  const {
    mutate: callCashOut,
    isPending: isCashingOut,
    error: cashOutError,
  } = useMutation({
    mutationKey: ['cashOut'],
    mutationFn: async (gameId: string) => {
      const response = await fetch(`/api/games/${gameId}/cash-out`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error((await response.json()).error || 'Failed to cash out');
      }
      return response.json();
    },
    onSuccess: (data) => {
      setPayoutData({ isPending: false, amount: data.payoutAmount, error: undefined });
      playCashOut();
      if (cashoutToastIdRef.current) {
        // Update the toast to show success and auto-dismiss after 2 seconds
        toast.success('Cashout successful!', {
          id: cashoutToastIdRef.current,
          duration: 2000,
        });
        cashoutToastIdRef.current = null;
      }
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
    },
    onError: (err) => {
      setPayoutData({ isPending: false, amount: undefined, error: err.message });
      console.error('[cashOut] Error:', err);
      toast.error(`Cash out failed!`, {
        id: cashoutToastIdRef.current ?? undefined,
        description: err.message,
      });
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  // ------------------------------------------------ HANDLERS
  const selectTile = useCallback(
    async (tileIndex: number) => {
      if (!currentGame || !walletAddress || isLoading) {
        toast.error('Cannot select tile: Missing game, connection, or signature.');
        return;
      }

      if (currentGame.status !== 'active') {
        setError('Game is already over');
        toast.error('Game is already over');
        return;
      }

      if (currentGame.currentRowIndex < 0) {
        setError('No active row');
        toast.error('No active row');
        return;
      }

      if (!currentGame.rows) {
        setError('No rows found');
        toast.error('No rows found');
        return;
      }

      const currentRow = currentGame.rows[currentGame.currentRowIndex];

      if (!currentRow || tileIndex < 0 || tileIndex >= currentRow.tiles) {
        setError('Invalid tile selection');
        toast.error('Invalid tile selection');
        return;
      }

      // Check if the tile in the specific row has already been chosen
      if (currentRow.chosenTileIndex !== undefined && currentRow.chosenTileIndex !== null) {
        setError('Row already played');
        toast.error('This row has already been played');
        return;
      }

      // --- Max profit enforcement ---
      // Calculate the potential payout if this tile is selected (i.e., if the player survives this row)
      const betAmount = currentGame.betAmount;
      const nextRowIndex = currentGame.currentRowIndex;
      const multipliers = calculateRowMultipliers(currentGame.rows);
      const nextMultiplier = multipliers[nextRowIndex];
      const { numerator, denominator } = decimalToFraction(nextMultiplier);
      const potentialPayout = (BigInt(betAmount) * numerator) / denominator;
      if (maxPayoutAmount && potentialPayout > maxPayoutAmount) {
        toast.error('Wow, you hit the profit limit! You must cash out now.');
        setIsLoading(false);
        setProcessingTileIndex(null);
        return;
      }

      setProcessingTileIndex(tileIndex);
      setIsLoading(true);
      setError(null);

      callSelectTile({ tileIndex, currentVersion: currentGame.version });
    },
    [currentGame, walletAddress, isLoading, callSelectTile]
  );

  const cashOut = useCallback(async (): Promise<void> => {
    if (!['active', 'cashout_pending', 'cashout_failed'].includes(currentGame?.status ?? '')) {
      toast.error('Cannot cash out: Game is not active.');
      console.warn(
        '[Cash Out] Aborted: Client game status is not active (',
        currentGame?.status,
        ')'
      );
      return;
    }

    if (!currentGame || !walletAddress || !config) {
      console.error('[Cash Out] Missing required data:', {
        currentGame,
        walletAddress,
        config,
      });
      toast.error('Cannot cash out: Missing game, wallet connection, or signature.');
      return;
    }

    setIsLoading(true);
    setError(null);

    if (sessionConfig && !(await isValidSession(sessionConfig))) {
      try {
        await handleCreateSessionAndUpdateUser();
      } catch (error) {
        console.error('Error renewing session:', error);
        toast.error('Error renewing session: ' + error);
        setIsLoading(false);
        setError('Error renewing session: ' + error);
        return;
      }
    }

    cashoutToastIdRef.current = toast.loading('Processing cash out...');
    setPayoutData({ isPending: true, amount: undefined, error: undefined });
    callCashOut(currentGame.id);
  }, [
    currentGame,
    walletAddress,
    config,
    sessionConfig,
    isValidSession,
    handleCreateSessionAndUpdateUser,
  ]);

  const setCashoutToastId = useCallback((toastId: string | number) => {
    cashoutToastIdRef.current = toastId;
  }, []);

  const shuffleRows = (customRows?: GameRow[]) => {
    // Only allow shuffling if there's no active game or if the current game is over
    if (currentGame?.id && currentGame?.status === 'active') return;

    let newRows: GameRow[];
    if (customRows && Array.isArray(customRows)) {
      // Use the custom row configuration if provided
      newRows = customRows;
    } else {
      // Generate random rows if no custom configuration provided
      newRows = Array(DEATH_RACE_TOTAL_ROWS)
        .fill(null)
        .map(() => ({
          tiles:
            Math.floor(Math.random() * (DEATH_RACE_MAX_TILES - DEATH_RACE_MIN_TILES + 1)) +
            DEATH_RACE_MIN_TILES, // Use constants
          deathTileIndex: null,
          chosenTileIndex: undefined,
        }));
    }

    const multipliers = calculateRowMultipliers(newRows);
    const rowsWithMultipliers = newRows.map((row, index) => ({
      ...row,
      multiplier: multipliers[index],
    }));

    queryClient.setQueryData(getRowConfigOptions().queryKey, {
      rows: rowsWithMultipliers,
      fresh: true,
    });
  };

  return {
    currentGame: currentGame,
    previousGame: previousGame,
    isLoading,
    error,
    payoutData,
    processingTileIndex,
    rowConfig,
    isFreshRowConfig,
    isSelectingTile,
    selectTile,
    cashOut,
    setCashoutToastId,
    shuffleRows,
  };
}
