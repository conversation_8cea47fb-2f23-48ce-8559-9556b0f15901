import { useEffect, useRef } from 'react';
import { useLocalStorage } from 'usehooks-ts';

function useAudio(src: string, volume: number) {
  const ref = useRef<HTMLAudioElement | null>(null);
  useEffect(() => {
    const audio = new Audio(src);
    audio.preload = 'auto';
    audio.volume = volume;
    ref.current = audio;
  }, [src, volume]);
  return ref;
}

function playSound(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  playingRef: React.MutableRefObject<boolean>,
  isMuted: boolean
) {
  if (isMuted) return;
  if (playingRef.current) return;
  const audio = audioRef.current;
  if (!audio) return;
  playingRef.current = true;
  audio.currentTime = 0;
  audio.play().finally(() => {
    playingRef.current = false;
  });
}

export function useSounds() {
  const [isMuted] = useLocalStorage<boolean>('deathrace-muted', false);
  // --- Audio refs ---
  const gameOverAudioRef = useAudio('/sounds/death-race/death-tile.mp3', 0.3);
  const bowserAudioRef = useAudio('/sounds/death-race/bowser.mp3', 0.2);
  const goodTileAudioRef = useAudio('/sounds/death-race/good-tile.mp3', 0.2);
  const gameStartAudioRef = useAudio('/sounds/death-race/game-start.mp3', 0.4);
  const cashOutAudioRef = useAudio('/sounds/death-race/coins1.mp3', 0.2);
  const shuffleAudioRef = useAudio('/sounds/death-race/shuffle7.mp3', 0.3);

  // --- Playing refs ---
  const deathPlayingRef = useRef(false);
  const goodTilePlayingRef = useRef(false);
  const gameStartPlayingRef = useRef(false);
  const cashOutPlayingRef = useRef(false);
  const shufflePlayingRef = useRef(false);

  function playDeathTile() {
    playSound(gameOverAudioRef, deathPlayingRef, isMuted);
    // If you want to play bowser as well, uncomment:
    // playSound(bowserAudioRef, deathPlayingRef);
  }
  function playWinTile() {
    playSound(goodTileAudioRef, goodTilePlayingRef, isMuted);
  }
  function playGameStart() {
    playSound(gameStartAudioRef, gameStartPlayingRef, isMuted);
  }
  function playCashOut() {
    playSound(cashOutAudioRef, cashOutPlayingRef, isMuted);
  }
  function playShuffle() {
    playSound(shuffleAudioRef, shufflePlayingRef, isMuted);
  }
  function reset() {
    /* no-op, kept for API compatibility */
  }

  return { playDeathTile, playWinTile, playGameStart, playCashOut, playShuffle, reset };
}
