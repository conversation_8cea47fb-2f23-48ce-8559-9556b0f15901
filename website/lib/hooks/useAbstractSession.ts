import { useAccount, usePublicClient } from 'wagmi';
import { useCreateSession } from '@abstract-foundation/agw-react';
import { toFunctionSelector } from 'viem';
import { LimitType, LimitZero, SessionConfig } from '@abstract-foundation/agw-client/sessions';
import { parseEther } from 'viem';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import superjson from 'superjson';
import { toast } from 'sonner';
import { useState } from 'react';
import { getUserOptions, useGetUser } from '../client/queries';
import { abstractClientConfig } from '../utils/abstract/config';
import { getSessionStatus } from '@abstract-foundation/agw-client/actions';

export const sessionPolicy: SessionConfig = {
  signer: process.env.NEXT_PUBLIC_SERVER_WALLET_ADDRESS as `0x${string}`, // Pass the server wallet address as the signer
  expiresAt: BigInt(Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60), // 1 month
  feeLimit: {
    limitType: LimitType.Lifetime,
    limit: parseEther('1'), // 1 ETH lifetime gas limit
    period: BigInt(0),
  },
  callPolicies: [
    {
      target: process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as `0x${string}`,
      selector: toFunctionSelector('createGame(string,bytes32,string,string,uint256,bytes)'), // Allowed function (createGame)
      valueLimit: {
        limitType: LimitType.Unlimited,
        limit: parseEther('100'),
        period: BigInt(0),
      },
      maxValuePerUse: parseEther('100'),
      constraints: [],
    },
    {
      target: process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as `0x${string}`, //
      selector: toFunctionSelector('cashOut(uint256,uint256,string,string,uint256,bytes)'), // Allowed function (cashOut)
      valueLimit: LimitZero,
      maxValuePerUse: parseEther('1'),
      constraints: [],
    },
    {
      target: process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as `0x${string}`, //
      selector: toFunctionSelector('markGameAsLost(uint256,string,string,uint256,bytes)'), // Allowed function (markGameAsLost)
      valueLimit: LimitZero,
      maxValuePerUse: parseEther('1'),
      constraints: [],
    },
  ],
  transferPolicies: [],
};

export const useAbstractSession = () => {
  const { address } = useAccount();
  const { createSessionAsync, isPending, isError, error: sessionError } = useCreateSession();
  const queryClient = useQueryClient();
  const [toastId, setToastId] = useState<string | number>('');
  const publicClient = usePublicClient({
    chainId: abstractClientConfig.chain.id,
  });
  const { data: user } = useGetUser();
  const { walletAddress } = user || {};
  const [isValidSc, setIsValidSc] = useState(false);

  const {
    isPending: isCreatingSessionAndUpdatingUser,
    mutateAsync: createSessionAndUpdateUserAsync,
  } = useMutation({
    mutationKey: ['createSession'],
    mutationFn: async () => {
      const localToastId = toast.loading('Creating session...');
      setToastId(localToastId);
      // --- create abstract session
      const result = await createSessionAsync({ session: sessionPolicy });
      if (isError) {
        throw sessionError;
      }
      if (!result.session) {
        throw new Error('Failed to create session');
      }
      const resp = await fetch('/api/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: superjson.stringify({ sessionConfig: result.session }),
        credentials: 'include',
      });
      if (!resp.ok) {
        throw new Error('Failed to update user');
      }
      return resp.json();
    },
    onSuccess: () => {
      toast.success('Session created', { id: toastId });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
    },
    onError: (error) => {
      toast.error('Failed to create session', { id: toastId });
      console.error('Error creating session', error);
    },
  });

  const handleCreateSessionAndUpdateUser = async () => {
    if (isCreatingSessionAndUpdatingUser || !address) {
      return;
    }
    return createSessionAndUpdateUserAsync();
  };

  const isValidSession = async (sc: SessionConfig) => {
    if (!publicClient) return false;
    if (isValidSc) return true;
    const sessionStatus = await getSessionStatus(publicClient, walletAddress as `0x${string}`, sc);
    if (sessionStatus > 1) {
      setIsValidSc(false);
      return false;
    }
    setIsValidSc(true);
    return true;
  };

  return {
    handleCreateSessionAndUpdateUser,
    isValidSession,
    publicClient,
  };
};
