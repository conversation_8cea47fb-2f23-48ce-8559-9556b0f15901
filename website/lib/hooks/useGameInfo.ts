'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

import { GAME_META_BY_TYPE, DEATH_RACE_GAME_TYPE } from '@/lib/constants';

export function useGameInfo() {
  const pathname = usePathname();

  return useMemo(() => {
    const gameMeta = Object.values(GAME_META_BY_TYPE).find(
      (game) => game.route === pathname
    ) ?? GAME_META_BY_TYPE[DEATH_RACE_GAME_TYPE];

    const gameType = gameMeta?.gameType ?? DEATH_RACE_GAME_TYPE;
    const statsLink = `/stats?gameType=${gameType}`;

    return { 
      gameType,
      gameMeta,
      statsLink 
    };
  }, [pathname]);
}
