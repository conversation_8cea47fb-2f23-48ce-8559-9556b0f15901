'use client';

import { useEffect } from 'react';
import { useFirstTimeUser } from './useFirstTimeUser';
import { useGambling } from './useGambling';
import { useGetUser } from '../client/queries';
import { GameType } from '../constants';
import { useGlobalStore } from '../store';
import { useBridge } from './flags/useBridge';

export function useNoFunds(gameType: GameType) {
  // ------------------------------------------------ STATE
  const faqOpen = useGlobalStore((state) => state.faq);
  const toggleModals = useGlobalStore((state) => state.toggleModals);

  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { walletAddress } = userData ?? {};
  const { isFirstTimeUser } = useFirstTimeUser(gameType);
  const { userBalance, isUserBalanceLoading } = useGambling(gameType);
  const bridgeEnabled = useBridge();

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    if (
      !walletAddress ||
      isFirstTimeUser === null ||
      isUserBalanceLoading ||
      !bridgeEnabled ||
      faqOpen
    ) {
      return;
    }

    if (userBalance === BigInt(0)) {
      toggleModals({ noFunds: true });
    }
  }, [
    walletAddress,
    isFirstTimeUser,
    userBalance,
    isUserBalanceLoading,
    bridgeEnabled,
    toggleModals,
  ]);
}
