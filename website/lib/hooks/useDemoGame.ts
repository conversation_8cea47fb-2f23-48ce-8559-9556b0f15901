import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import {
  DEATH_RACE_TOTAL_ROWS,
  DEATH_RACE_MIN_TILES,
  DEATH_RACE_MAX_TILES,
  DEFAULT_ROW_CONFIG,
  ALGO_VERSION,
  DEATH_RACE_GAME_TYPE,
} from '@/lib/constants';
import { calculateRowMultipliers, generateRandomRow } from '@/lib/utils/game';
import { getDeathTileIndex, generateGameSeed } from '@/lib/utils/provably-fair';
import type { DisplayRow, GameRow } from '@/components/death-race/types';
import { parseEther } from 'viem';
import { analytics } from '@/lib/client/analytics';
import { useSounds } from '@/lib/hooks/useSounds';

function getInitialRowsWithDeathTiles(seed: string, baseRows?: GameRow[]): DisplayRow[] {
  const rowsToUse: GameRow[] = baseRows || DEFAULT_ROW_CONFIG;
  const multipliers = calculateRowMultipliers(rowsToUse);
  return rowsToUse.map((row, i) => ({
    ...row,
    deathTileIndex: getDeathTileIndex(seed, i, row.tiles),
    multiplier: multipliers[i],
  }));
}

export function useDemoGame() {
  const [gameSeed, setGameSeed] = useState(() => generateGameSeed());
  const [rows, setRows] = useState<DisplayRow[]>(() => getInitialRowsWithDeathTiles(gameSeed));
  const [isFreshRowConfig, setIsFreshRowConfig] = useState(false);
  const [currentRowIndex, setCurrentRowIndex] = useState(0);
  const [selectedTiles, setSelectedTiles] = useState<number[]>([]);
  const [isSelectingTile, setIsSelectingTile] = useState(false);
  const [isGameOver, setIsGameOver] = useState(false);
  const [hasWon, setHasWon] = useState(false);
  const [finalMultiplier, setFinalMultiplier] = useState(1);
  const [processingTileIndex, setProcessingTileIndex] = useState<number | null>(null);
  const [payoutData, setPayoutData] = useState<{ isPending: boolean; error?: string }>({
    isPending: false,
  });
  const { playShuffle } = useSounds();

  // Mimic the shape of useGame
  const currentGame = {
    id: 'demo',
    status: isGameOver ? (hasWon ? 'won' : 'lost') : 'active',
    currentRowIndex,
    finalMultiplier,
    rows,
    selectedTiles,
    commitmentHash: null,
    gameSeed,
    payoutError: null,
    payoutAmount: null,
    payoutTxSignature: null,
    createdAt: '',
    updatedAt: '',
    betAmount: parseEther('1'),
    algoVersion: ALGO_VERSION,
  };

  const selectTile = useCallback(
    async (tileIndex: number) => {
      if (isGameOver || processingTileIndex !== null) return;
      setIsSelectingTile(true);
      setProcessingTileIndex(tileIndex);
      setTimeout(() => {
        const row = rows[currentRowIndex];
        const isDeath = row.deathTileIndex === tileIndex;
        const newSelectedTiles = [...selectedTiles, tileIndex];
        setSelectedTiles(newSelectedTiles);
        if (isDeath) {
          setIsGameOver(true);
          setHasWon(false);
          setFinalMultiplier(0);
        } else if (currentRowIndex === rows.length - 1) {
          setIsGameOver(true);
          setHasWon(true);
          setFinalMultiplier(rows[rows.length - 1].multiplier);
        } else {
          setCurrentRowIndex((i) => i + 1);
        }
        setProcessingTileIndex(null);
        setIsSelectingTile(false);
      }, 400);
    },
    [isGameOver, processingTileIndex, rows, currentRowIndex, selectedTiles]
  );

  const cashOut = useCallback(async () => {
    if (isGameOver) return;
    setIsGameOver(true);
    setHasWon(true);
    setFinalMultiplier(currentRowIndex > 0 ? rows[currentRowIndex - 1].multiplier : 1);
    toast.success('Cashed out!');
  }, [isGameOver, currentRowIndex, rows]);

  const shuffleRows = useCallback((customRows?: GameRow[]) => {
    playShuffle();
    const newSeed = generateGameSeed();
    const baseRows: GameRow[] =
      customRows && customRows.length
        ? customRows
        : Array(DEATH_RACE_TOTAL_ROWS)
            .fill(null)
            .map(() => generateRandomRow(DEATH_RACE_MIN_TILES, DEATH_RACE_MAX_TILES));
    const multipliers = calculateRowMultipliers(baseRows);
    const rowsWithDeath = baseRows.map((row, i) => ({
      ...row,
      deathTileIndex: getDeathTileIndex(newSeed, i, row.tiles),
      multiplier: multipliers[i],
    }));
    setRows(rowsWithDeath);
    setCurrentRowIndex(0);
    setSelectedTiles([]);
    setIsGameOver(false);
    setHasWon(false);
    setFinalMultiplier(1);
  }, []);

  const setCashoutToastId = () => {};
  const setRefetchInterval = () => {};
  const loadNextGame = async () => false;

  // Unified function to start/reset the demo game using the current rows config
  const startDemo = useCallback(() => {
    analytics.demoPlayed({ gameType: DEATH_RACE_GAME_TYPE });
    const newSeed = generateGameSeed();
    setGameSeed(newSeed);
    setRows(getInitialRowsWithDeathTiles(newSeed, rows));
    setCurrentRowIndex(0);
    setSelectedTiles([]);
    setIsGameOver(false);
    setHasWon(false);
    setFinalMultiplier(1);
    setProcessingTileIndex(null);
  }, [rows]);

  return {
    currentGame,
    previousGame: undefined,
    isLoading: false,
    error: null,
    payoutData,
    hasUpcomingGames: false,
    processingTileIndex,
    rowConfig: rows,
    isFreshRowConfig,
    isSelectingTile,
    loadNextGame,
    selectTile,
    cashOut,
    setCashoutToastId,
    setRefetchInterval,
    shuffleRows,
    startDemo,
  };
}

export type DemoGameHookReturn = ReturnType<typeof useDemoGame>;
