import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

export const useShowUI = (delay: number = 500, block: boolean = false) => {
  const [showUI, setShowUI] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    if (block) {
      return;
    }

    setShowUI(false);

    const timer = setTimeout(() => {
      setShowUI(true);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [block, delay, pathname]);

  return showUI;
};
