import { usePrivy } from '@privy-io/react-auth';
import { useQueryClient } from '@tanstack/react-query';
import { useLDClient } from 'launchdarkly-react-client-sdk';

export const useLogout = () => {
  const { logout } = usePrivy();
  const queryClient = useQueryClient();
  const client = useLDClient();

  return async () => {
    await logout();
    queryClient.resetQueries();
    client?.identify({ anonymous: true, kind: 'user' });
  };
};
