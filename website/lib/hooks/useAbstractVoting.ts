import { useAccount, usePublicClient, useWalletClient, useReadContract } from 'wagmi';
import { abstractClientConfig } from '../utils/abstract/config';
import { Hex } from 'viem';
import AbstractVotingABI from '@/lib/blockchain/abi/AbstractVoting.json';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ABSTRACT_VOTING_CONTRACT_ADDRESS, getVotingIdForGameType } from '../constants';

export const useAbstractVoting = (gameType?: string | null) => {
  const { address } = useAccount();
  const publicClient = usePublicClient({ chainId: Number(abstractClientConfig.chainId) });
  const { data: walletClient } = useWalletClient({
    chainId: Number(abstractClientConfig.chainId),
  });
  const queryClient = useQueryClient();

  // Read the current vote cost
  const { data: voteCost, error: voteCostError } = useReadContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'voteCost',
    chainId: Number(abstractClientConfig.chainId),
    query: {
      enabled: !!ABSTRACT_VOTING_CONTRACT_ADDRESS,
    },
  });

  // Read user's remaining votes
  const { data: userVotesRemaining, error: votesRemainingError } = useReadContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'userVotesRemaining',
    args: [address],
    chainId: Number(abstractClientConfig.chainId),
    query: {
      enabled: !!address && !!ABSTRACT_VOTING_CONTRACT_ADDRESS,
    },
  });

  // Read current epoch
  const { data: currentEpoch } = useReadContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'currentEpoch',
    chainId: Number(abstractClientConfig.chainId),
    query: {
      enabled: !!ABSTRACT_VOTING_CONTRACT_ADDRESS,
    },
  });

  // Check if user has already voted for Death Race in current epoch
  const { data: userVotes, refetch: refetchUserVotes } = useReadContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'getUserVotes',
    args: [address, currentEpoch?.toString()],
    chainId: Number(abstractClientConfig.chainId),
    query: {
      enabled: !!address && !!ABSTRACT_VOTING_CONTRACT_ADDRESS && currentEpoch !== undefined,
    },
  });

  // Check if user has already voted for the specific game
  const votingId = getVotingIdForGameType(gameType);
  const hasAbstractVoted = userVotes ? (userVotes as bigint[]).includes(BigInt(votingId)) : false;

  // Vote for app mutation
  const voteForAppMutation = useMutation({
    mutationFn: async () => {
      if (!publicClient || !address || !walletClient) {
        throw new Error('Wallet not connected');
      }

      if (!ABSTRACT_VOTING_CONTRACT_ADDRESS) {
        throw new Error('Abstract Voting contract address not configured');
      }

      if (voteCost === undefined || voteCost === null) {
        throw new Error('Vote cost not available');
      }

      // Simulate the transaction first
      const { request } = await publicClient.simulateContract({
        address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
        abi: AbstractVotingABI,
        functionName: 'voteForApp',
        args: [BigInt(votingId)],
        value: voteCost as bigint,
        account: address,
      });

      // Send the transaction
      const txHash = await walletClient.writeContract(request);

      return { txHash, appId: votingId };
    },
    onSuccess: (data) => {
      toast.success(`Thanks for voting! Now verify to get your points.`);
      // Invalidate all voting-related queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['userVotesRemaining'] });
      queryClient.invalidateQueries({
        predicate: (query) =>
          query.queryKey.some((key) => typeof key === 'string' && key.includes('getUserVotes')),
      });
      // Also manually refetch user votes
      refetchUserVotes();
    },
    onError: (error) => {
      // Don't show error toast for user rejections/dismissals
      if (
        error.message.includes('user rejected') ||
        error.message.includes('User rejected') ||
        error.message.includes('rejected the request') ||
        error.message.includes('User denied') ||
        error.message.includes('user denied')
      ) {
        // Silently handle user rejections
        return;
      }

      toast.error(`Failed to vote: ${error.message}`);
    },
  });

  return {
    voteForApp: voteForAppMutation.mutate,
    isVoting: voteForAppMutation.isPending,
    canVote:
      !!address &&
      voteCost !== undefined &&
      voteCost !== null &&
      !!ABSTRACT_VOTING_CONTRACT_ADDRESS &&
      (userVotesRemaining as number) > 0 &&
      !hasAbstractVoted,
    hasAbstractVoted,
    refreshVotingStatus: refetchUserVotes,
  };
};
