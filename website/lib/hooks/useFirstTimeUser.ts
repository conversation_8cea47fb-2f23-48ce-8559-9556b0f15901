import { useState, useEffect } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { useGetActiveGame, useGetUser } from '../client/queries';
import { GAME_TYPES } from '../constants';

export function useFirstTimeUser(gameType: (typeof GAME_TYPES)[number]) {
  const { data: userData } = useGetUser();
  const { walletAddress } = userData ?? {};
  const { data: activeGame } = useGetActiveGame(gameType);
  const { currentGame } = activeGame ?? {};
  const [isFirstTimeUser, setIsFirstTimeUser] = useState<boolean | null>(null);
  const [hasShownFAQ, setHasShownFAQ] = useLocalStorage<Record<string, boolean>>('faq_shown', {});

  useEffect(() => {
    if (!walletAddress || !userData) {
      setIsFirstTimeUser(null);
      return;
    }

    try {
      if (hasShownFAQ[walletAddress]) {
        setIsFirstTimeUser(false);
        return;
      }

      const isFirstTime =
        !currentGame && new Date(userData.createdAt).getTime() > Date.now() - 3600000;

      setIsFirstTimeUser(isFirstTime);
    } catch (error) {
      console.error('Error checking first time user status:', error);
      setIsFirstTimeUser(null);
    }
  }, [walletAddress, userData, hasShownFAQ, currentGame]);

  const markFAQAsShown = () => {
    if (walletAddress) {
      setHasShownFAQ((prev) => ({
        ...prev,
        [walletAddress]: true,
      }));
      setIsFirstTimeUser(false);
    }
  };

  return { isFirstTimeUser, markFAQAsShown };
}
