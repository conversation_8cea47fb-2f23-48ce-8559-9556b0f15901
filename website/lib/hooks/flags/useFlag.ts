import { DEATH_RACE_GAME_TYPE } from '@/lib/constants';
import { useFlags, useLDClient } from 'launchdarkly-react-client-sdk';
import { useEffect, useState } from 'react';

type AvailableBoolFlags = 'bridge';

const boolFallbackValues: Record<AvailableBoolFlags, boolean> = {
  bridge: false,
};

/**
 * Use a boolean flag from LaunchDarkly
 * @param flag
 * @returns The value of the boolean flag or a fallback value if the flag is not available
 */
export const useBoolFlag = (
  flag: AvailableBoolFlags
): {
  value: boolean;
  isLoading: boolean;
} => {
  const client = useLDClient();
  const flags = useFlags();
  const [isLoadingFlags, setIsLoadingFlags] = useState(true);

  useEffect(() => {
    client?.waitUntilReady().then(() => {
      setIsLoadingFlags(false);
    });
  }, [client]);

  return {
    value: flags[flag] !== undefined && !isLoadingFlags ? flags[flag] : boolFallbackValues[flag],
    isLoading: isLoadingFlags,
  };
};

type AvailableJsonFlags = 'available-games';

const jsonFallbackValues: Record<AvailableJsonFlags, any> = {
  'available-games': {
    games: [DEATH_RACE_GAME_TYPE],
  },
};

/**
 * Use a JSON flag from LaunchDarkly
 * @param flag
 * @returns The value of the JSON flag or a fallback value if the flag is not available
 */
export const useJsonFlag = (
  flag: AvailableJsonFlags
): {
  value: any;
  isLoading: boolean;
} | null => {
  const client = useLDClient();
  const flags = useFlags();
  const [isLoadingFlags, setIsLoadingFlags] = useState(true);

  useEffect(() => {
    client?.waitUntilReady().then(() => {
      setIsLoadingFlags(false);
    });
  }, [client]);

  return {
    value: flags[flag] !== undefined && !isLoadingFlags ? flags[flag] : jsonFallbackValues[flag],
    isLoading: isLoadingFlags,
  };
};
