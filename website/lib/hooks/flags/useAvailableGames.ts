import { GAME_META_BY_TYPE } from '@/lib/constants';
import { useJsonFlag } from './useFlag';

export const useAvailableGames = (): (keyof typeof GAME_META_BY_TYPE)[] | null => {
  const { value: availableGames, isLoading } = useJsonFlag('available-games') || {
    value: { games: [] },
    isLoading: true,
  };
  if (isLoading) {
    return null;
  }

  const filteredGames = availableGames.games.filter((game: string) => game in GAME_META_BY_TYPE);
  return filteredGames as (keyof typeof GAME_META_BY_TYPE)[];
};
