import { useBalance } from 'wagmi';
import { abstractClientConfig } from '@/lib/utils/abstract/config';
import { useGetUser } from '@/lib/client/queries';

export function useUserBalance() {
  const { data: user } = useGetUser();
  const { walletAddress } = user ?? {};
  const { data: userBalance, isPending: isUserBalanceLoading } = useBalance({
    address: walletAddress as `0x${string}`,
    chainId: Number(abstractClientConfig.chainId),
  });

  return { userBalance, isUserBalanceLoading };
}
