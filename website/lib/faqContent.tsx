import { FAQContent } from '@/components/FAQ';
import { DEATH_RACE_HOUSE_EDGE, DOCS_LINK, SUPPORT_LINK, REFERRAL_PERCENTAGE } from './constants';
import { UserDataResponse } from './client/queries';

export const getDeathRaceFAQContent = (userData?: UserDataResponse): FAQContent => {
  // Use custom referral percentage if set, otherwise use global percentage
  const effectiveReferralPercentage = userData?.customReferralPercentage ?? REFERRAL_PERCENTAGE;

  return {
    howToPlay: [
      'Place your bet',
      'Choose one tile per row',
      'Each row has one death tile that busts you to 0',
      'Your payout multiplier increases for each row you survive',
      'Cash out anytime',
    ],
    accordionItems: [
      {
        value: 'shuffling',
        title: 'Shuffling',
        content: (
          <ul className='list-decimal list-inside space-y-1'>
            <li>Hit the shuffle button to change the rows layout</li>
            <li>Fewer tiles means higher risk but also higher potential rewards</li>
          </ul>
        ),
      },
      {
        value: 'provably-fair',
        title: 'Provably Fair',
        content:
          'Each game includes a hash that can be used to verify death tiles were chosen before the game started',
      },
      {
        value: 'referral-program',
        title: 'Referral Program',
        content: `Share your referral link and earn ${(effectiveReferralPercentage / DEATH_RACE_HOUSE_EDGE) * 100}% of profits on all referrals!`,
      },
      {
        value: 'multiplier-calculation',
        title: 'Multiplier Calculation',
        content: (
          <div className='flex flex-col gap-2'>
            <div>
              <p>Base multiplier per row:</p>
              <p className='text-gc-400 font-mono pl-4'>1 / (1 - death_probability)</p>
            </div>
            <div>
              <p>Example w/ 5 tiles:</p>
              <p className='text-gc-400 font-mono pl-4'>1 / (1 - 0.2) = 1.25x</p>
            </div>
            <div>
              <p>Each row multiplies with previous rows:</p>
              <p className='text-gc-400 font-mono pl-4'>Row 2 Total = Row 1 Base × Row 2 Base </p>
            </div>
            <div>
              <p>A {(DEATH_RACE_HOUSE_EDGE * 100).toFixed(1)}% house edge is then applied:</p>
              <p className='text-gc-400 font-mono pl-4'>
                Row 2 Total × {(1 - DEATH_RACE_HOUSE_EDGE).toFixed(4)} = Total Multiplier
              </p>
            </div>
          </div>
        ),
      },
      {
        value: 'support',
        title: 'Support',
        content: (
          <div className='flex flex-col gap-2'>
            <p>Ran into an issue? Reach out or create a ticket here:</p>
            <a
              href={SUPPORT_LINK}
              target='_blank'
              rel='noopener noreferrer'
              className='text-gc-400 underline hover:text-white transition-colors w-fit'
            >
              Telegram Support
            </a>
          </div>
        ),
      },
    ],
  };
};

export const getLazerPartyFAQContent = (userData?: UserDataResponse): FAQContent => {
  // Use custom referral percentage if set, otherwise use global percentage
  const effectiveReferralPercentage = userData?.customReferralPercentage ?? REFERRAL_PERCENTAGE;

  return {
    howToPlay: [
      'Place your bet',
      'Choose one cell in the grid',
      'Each round, a laser destroys an entire column or row',
      'If you get lasered, you bust to 0',
      'Your payout multiplier increases for each round you survive',
      'Cash out anytime',
    ],
    accordionItems: [
      {
        value: 'provably-fair',
        title: 'Provably Fair',
        content: (
          <>
            Each game includes a cryptographic hash of the lasered columns & rows order, which can
            be verified once the game is over to prove that it was all decided before the game
            started. Click the checkmark button on the stats page to verify it! You can read more
            about this in our{' '}
            <a
              href={DOCS_LINK}
              target='_blank'
              rel='noopener noreferrer'
              className='text-gc-400 underline'
            >
              docs
            </a>
            .
          </>
        ),
      },
      {
        value: 'referral-program',
        title: 'Referral Program',
        content: `Share your referral link and earn ${(effectiveReferralPercentage / DEATH_RACE_HOUSE_EDGE) * 100}% of profits on all referrals!`,
      },
      {
        value: 'multiplier-calculation',
        title: 'Multiplier Calculation',
        content: (
          <div className='flex flex-col gap-2'>
            <div>
              <p>Base multiplier per row:</p>
              <p className='text-gc-400 font-mono pl-4'>1 / (1 - death_probability)</p>
            </div>
            <div>
              <p>Example w/ 10 columns:</p>
              <p className='text-gc-400 font-mono pl-4'>1 / (1 - 0.1) = 1.10x</p>
            </div>
            <div>
              <p>Each row multiplies with previous rows:</p>
              <p className='text-gc-400 font-mono pl-4'>Row 2 Total = Row 1 Base × Row 2 Base</p>
            </div>
            <div>
              <p>A {(DEATH_RACE_HOUSE_EDGE * 100).toFixed(1)}% house edge is then applied:</p>
              <p className='text-gc-400 font-mono pl-4'>
                Row 2 Total × {(1 - DEATH_RACE_HOUSE_EDGE).toFixed(4)} = Total Multiplier
              </p>
            </div>
          </div>
        ),
      },
      {
        value: 'support',
        title: 'Support',
        content: (
          <div className='flex flex-col gap-2'>
            <p>Ran into an issue? Reach out or create a ticket here:</p>
            <a
              href={SUPPORT_LINK}
              target='_blank'
              rel='noopener noreferrer'
              className='text-gc-400 underline hover:text-white transition-colors w-fit'
            >
              Telegram Support
            </a>
          </div>
        ),
      },
    ],
  };
};

// Backward compatibility exports - these will use default referral percentage
export const deathRaceFAQContent: FAQContent = getDeathRaceFAQContent();
export const lazerPartyFAQContent: FAQContent = getLazerPartyFAQContent();
