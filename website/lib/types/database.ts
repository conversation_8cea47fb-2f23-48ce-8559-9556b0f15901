export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_stats_overall: {
        Row: {
          game_type: string
          lost_games: number
          sum_row_reached: number
          sum_win_multipliers: number
          total_games: number
          total_payouts: number
          total_volume: number
          treasury_delta: number
          unique_users: number
          updated_at: string | null
        }
        Insert: {
          game_type: string
          lost_games?: number
          sum_row_reached?: number
          sum_win_multipliers?: number
          total_games?: number
          total_payouts?: number
          total_volume?: number
          treasury_delta?: number
          unique_users?: number
          updated_at?: string | null
        }
        Update: {
          game_type?: string
          lost_games?: number
          sum_row_reached?: number
          sum_win_multipliers?: number
          total_games?: number
          total_payouts?: number
          total_volume?: number
          treasury_delta?: number
          unique_users?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      challenge_submissions: {
        Row: {
          challenge_id: number
          created_at: string | null
          expires_at: string | null
          status: string
          user_id: string
        }
        Insert: {
          challenge_id: number
          created_at?: string | null
          expires_at?: string | null
          status: string
          user_id: string
        }
        Update: {
          challenge_id?: number
          created_at?: string | null
          expires_at?: string | null
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_challenge"
            columns: ["challenge_id"]
            isOneToOne: false
            referencedRelation: "challenges"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_user"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      challenges: {
        Row: {
          created_at: string | null
          description: string | null
          game_type: string | null
          icon: string | null
          id: number
          link: string
          points: number
          published: boolean
          title: string
          type: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          game_type?: string | null
          icon?: string | null
          id?: number
          link: string
          points: number
          published?: boolean
          title: string
          type: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          game_type?: string | null
          icon?: string | null
          id?: number
          link?: string
          points?: number
          published?: boolean
          title?: string
          type?: string
        }
        Relationships: []
      }
      game_max_balances: {
        Row: {
          active: boolean
          created_at: string
          game_type: string
          max_balance: string
          updated_at: string
        }
        Insert: {
          active?: boolean
          created_at?: string
          game_type: string
          max_balance: string
          updated_at?: string
        }
        Update: {
          active?: boolean
          created_at?: string
          game_type?: string
          max_balance?: string
          updated_at?: string
        }
        Relationships: []
      }
      games: {
        Row: {
          algo_version: string
          app: string | null
          balance_expires_at: string | null
          bet_amount: string
          commitment_hash: string | null
          created_at: string
          current_row_index: number
          error_reason: string | null
          final_multiplier: number | null
          game_seed: string | null
          game_type: string
          id: string
          net: number | null
          onchain_game_id: string | null
          payout_attempts: number | null
          payout_error: string | null
          payout_sent_at: string | null
          payout_tx_signature: string | null
          pot_balance: string | null
          refund_amount: number | null
          refund_tx_signature: string | null
          release_version: string
          requires_manual_action: boolean | null
          rows: Json
          selected_tiles: Json | null
          status: string
          transaction_signature: string | null
          updated_at: string
          version: number
          wallet_address: string | null
        }
        Insert: {
          algo_version?: string
          app?: string | null
          balance_expires_at?: string | null
          bet_amount: string
          commitment_hash?: string | null
          created_at?: string
          current_row_index?: number
          error_reason?: string | null
          final_multiplier?: number | null
          game_seed?: string | null
          game_type?: string
          id?: string
          net?: number | null
          onchain_game_id?: string | null
          payout_attempts?: number | null
          payout_error?: string | null
          payout_sent_at?: string | null
          payout_tx_signature?: string | null
          pot_balance?: string | null
          refund_amount?: number | null
          refund_tx_signature?: string | null
          release_version?: string
          requires_manual_action?: boolean | null
          rows: Json
          selected_tiles?: Json | null
          status: string
          transaction_signature?: string | null
          updated_at?: string
          version?: number
          wallet_address?: string | null
        }
        Update: {
          algo_version?: string
          app?: string | null
          balance_expires_at?: string | null
          bet_amount?: string
          commitment_hash?: string | null
          created_at?: string
          current_row_index?: number
          error_reason?: string | null
          final_multiplier?: number | null
          game_seed?: string | null
          game_type?: string
          id?: string
          net?: number | null
          onchain_game_id?: string | null
          payout_attempts?: number | null
          payout_error?: string | null
          payout_sent_at?: string | null
          payout_tx_signature?: string | null
          pot_balance?: string | null
          refund_amount?: number | null
          refund_tx_signature?: string | null
          release_version?: string
          requires_manual_action?: boolean | null
          rows?: Json
          selected_tiles?: Json | null
          status?: string
          transaction_signature?: string | null
          updated_at?: string
          version?: number
          wallet_address?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "games_wallet_address_fkey"
            columns: ["wallet_address"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["wallet_address"]
          },
        ]
      }
      leaderboard_records: {
        Row: {
          game_type: string
          games_lost: number | null
          games_won: number | null
          profit_loss_daily: number | null
          total_bet_amount: number | null
          total_games: number | null
          total_profit_loss: number | null
          user_id: string | null
        }
        Insert: {
          game_type: string
          games_lost?: number | null
          games_won?: number | null
          profit_loss_daily?: number | null
          total_bet_amount?: number | null
          total_games?: number | null
          total_profit_loss?: number | null
          user_id?: string | null
        }
        Update: {
          game_type?: string
          games_lost?: number | null
          games_won?: number | null
          profit_loss_daily?: number | null
          total_bet_amount?: number | null
          total_games?: number | null
          total_profit_loss?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "leaderboard_records_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      locks: {
        Row: {
          created_at: string
          game_id: string | null
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          game_id?: string | null
          type: string
          user_id: string
        }
        Update: {
          created_at?: string
          game_id?: string | null
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "locks_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locks_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      points_ledger: {
        Row: {
          context: Json | null
          created_at: string
          id: string
          points: number
          source: string
          user_id: string
        }
        Insert: {
          context?: Json | null
          created_at?: string
          id?: string
          points: number
          source: string
          user_id: string
        }
        Update: {
          context?: Json | null
          created_at?: string
          id?: string
          points?: number
          source?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "points_ledger_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      referral_bonuses: {
        Row: {
          app: string | null
          bonus_amount: string
          created_at: string
          game_id: string
          id: string
          referred_id: string
          referrer_id: string
          status: string
          transaction_signature: string | null
          updated_at: string | null
          version: number
        }
        Insert: {
          app?: string | null
          bonus_amount: string
          created_at?: string
          game_id: string
          id?: string
          referred_id: string
          referrer_id: string
          status: string
          transaction_signature?: string | null
          updated_at?: string | null
          version?: number
        }
        Update: {
          app?: string | null
          bonus_amount?: string
          created_at?: string
          game_id?: string
          id?: string
          referred_id?: string
          referrer_id?: string
          status?: string
          transaction_signature?: string | null
          updated_at?: string | null
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "referral_bonuses_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_bonuses_referred_id_fkey"
            columns: ["referred_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_bonuses_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          app: string | null
          created_at: string
          custom_referral_percentage: number | null
          games_lost: number | null
          games_won: number | null
          id: string
          last_game_date: string | null
          last_login_at: string | null
          last_updated: string | null
          multiplier: number
          points: number
          profit_loss_30d: number | null
          profit_loss_7d: number | null
          profit_loss_daily: number | null
          referral_code: string
          referred_by: string | null
          role: string
          session_config: Json | null
          streak: number
          testnet_claim_status: boolean
          total_games: number | null
          total_profit_loss: number | null
          total_referral_bonuses: number | null
          total_referral_bonuses_paid: number | null
          username: string | null
          wallet_address: string
        }
        Insert: {
          app?: string | null
          created_at?: string
          custom_referral_percentage?: number | null
          games_lost?: number | null
          games_won?: number | null
          id?: string
          last_game_date?: string | null
          last_login_at?: string | null
          last_updated?: string | null
          multiplier?: number
          points?: number
          profit_loss_30d?: number | null
          profit_loss_7d?: number | null
          profit_loss_daily?: number | null
          referral_code: string
          referred_by?: string | null
          role?: string
          session_config?: Json | null
          streak?: number
          testnet_claim_status?: boolean
          total_games?: number | null
          total_profit_loss?: number | null
          total_referral_bonuses?: number | null
          total_referral_bonuses_paid?: number | null
          username?: string | null
          wallet_address: string
        }
        Update: {
          app?: string | null
          created_at?: string
          custom_referral_percentage?: number | null
          games_lost?: number | null
          games_won?: number | null
          id?: string
          last_game_date?: string | null
          last_login_at?: string | null
          last_updated?: string | null
          multiplier?: number
          points?: number
          profit_loss_30d?: number | null
          profit_loss_7d?: number | null
          profit_loss_daily?: number | null
          referral_code?: string
          referred_by?: string | null
          role?: string
          session_config?: Json | null
          streak?: number
          testnet_claim_status?: boolean
          total_games?: number | null
          total_profit_loss?: number | null
          total_referral_bonuses?: number | null
          total_referral_bonuses_paid?: number | null
          username?: string | null
          wallet_address?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_referred_by"
            columns: ["referred_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_referred_by_fkey"
            columns: ["referred_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_points_to_user_and_log: {
        Args: {
          p_user_id: string
          p_points_to_add: number
          p_source: string
          p_context: Json
        }
        Returns: number
      }
      adjust_referral_bonuses_status: {
        Args: { p_ids: string[]; p_status: string; p_tx_hash?: string }
        Returns: number
      }
      calculate_points_multiplier: {
        Args: { streak_days: number }
        Returns: number
      }
      distribute_and_reset_daily_leaderboard: {
        Args:
          | {
              p_user_ids: string[]
              points_to_add: number[]
              p_game_type: string
            }
          | {
              wallet_addresses: string[]
              points_to_add: number[]
              p_game_type: string
            }
        Returns: undefined
      }
      generate_referral_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_admin_stats: {
        Args: { game_type?: string; start_date?: string; end_date?: string }
        Returns: Json
      }
      get_bulk_user_stats: {
        Args: { p_user_ids: string[]; p_game_type?: string }
        Returns: Json
      }
      get_game_with_lock: {
        Args: { game_id: string }
        Returns: {
          algo_version: string
          app: string | null
          balance_expires_at: string | null
          bet_amount: string
          commitment_hash: string | null
          created_at: string
          current_row_index: number
          error_reason: string | null
          final_multiplier: number | null
          game_seed: string | null
          game_type: string
          id: string
          net: number | null
          onchain_game_id: string | null
          payout_attempts: number | null
          payout_error: string | null
          payout_sent_at: string | null
          payout_tx_signature: string | null
          pot_balance: string | null
          refund_amount: number | null
          refund_tx_signature: string | null
          release_version: string
          requires_manual_action: boolean | null
          rows: Json
          selected_tiles: Json | null
          status: string
          transaction_signature: string | null
          updated_at: string
          version: number
          wallet_address: string | null
        }[]
      }
      get_lock: {
        Args: {
          p_user_id: string
          p_game_id: string
          p_type: string
          p_status: string
          p_threshold?: unknown
        }
        Returns: undefined
      }
      get_user_lock: {
        Args: { p_user_id: string; p_type: string; p_threshold?: unknown }
        Returns: undefined
      }
      get_user_stats: {
        Args:
          | { p_user_id: string; p_game_type?: string }
          | { wallet: string; game_type?: string }
        Returns: Json
      }
      get_weekly_game_stats: {
        Args: { days_ago: number }
        Returns: {
          wallet_address: string
          transactions: number
          total_eth_bet: number
        }[]
      }
      increment_user_points_with_multiplier: {
        Args:
          | {
              p_user_id: string
              p_base_points: number
              p_source: string
              p_context: Json
            }
          | { p_wallet_address: string; p_base_points: number }
          | {
              p_wallet_address: string
              p_base_points: number
              p_source: string
              p_context: Json
            }
        Returns: number
      }
      is_in_current_day: {
        Args: { ts: string }
        Returns: boolean
      }
      process_challenge: {
        Args: { challenge_id: number; user_id: string }
        Returns: undefined
      }
      update_user_streak: {
        Args: { p_wallet_address: string }
        Returns: {
          new_streak: number
          new_multiplier: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

