import { DisplayRow, LPDisplayRow } from '@/components/death-race/types';
import { GameStatus } from './game';

export interface SelectTileResponse {
  isDeathTile: boolean;
  currentRowIndex: number;
  finalMultiplier: number | null;
  status: GameStatus;
  currentRow: DisplayRow | LPDisplayRow | null;
  nextRow: DisplayRow | LPDisplayRow | null;
}

export interface LeaderboardEntry {
  username?: string;
  profit_loss: number;
  rank: number;
  is_current_user: boolean;
  points?: number;
}

export interface AdminStats {
  totalGames: number;
  totalVolume: string;
  totalPayouts: string;
  totalUsers: number;
  treasuryDelta: string;
  bustPercentage: number;
  averageWinMultiplier: number;
  averageRowReached: number;
}
