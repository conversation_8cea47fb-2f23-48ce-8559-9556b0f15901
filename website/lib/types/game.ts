import { DisplayRow, GameRow, LPDisplayRow } from '@/components/death-race/types';
import type { CamelCasedPropertiesDeep } from 'type-fest';
import { Database } from './database';

// Define GameStatus based on the database definition
export type GameStatus =
  | 'active'
  | 'won'
  | 'lost'
  | 'creation_failed'
  | 'pending_onchain'
  | 'cashout_pending'
  | 'cashout_failed';

/**
 * Represents the game data structure as stored in the database.
 * Uses snake_case matching the database schema.
 */
export interface Game {
  id: string;
  wallet_address: string;
  bet_amount: number; // Represents smallest unit (Lamports/Wei) as BIGINT in DB
  status: GameStatus;
  rows: GameRow[]; // Use the imported GameRow type
  current_row_index: number;
  selected_tiles: number[];
  final_multiplier: number | null;
  created_at: string; // ISO timestamp string
  updated_at: string; // ISO timestamp string
  transaction_signature: string | null; // Solana
  payout_tx_signature: string | null;
  payout_attempts: number;
  payout_error: string | null;
  game_seed: string | null;
  commitment_hash: string | null;
  is_verified?: boolean; // Added from NormalizedGame, assuming it exists in DB
  error_reason: string | null;
  requires_manual_action: boolean;
  refund_tx_signature: string | null;
  refund_amount: number | null;
  refund_notified?: boolean; // Added from NormalizedGame, assuming it exists in DB
  app: string; // Added from database.ts
  onchain_game_id: string | null; // Abstract
  // Ensure all fields from Database['public']['Tables']['games']['Row'] are here
}

/**
 * Represents the normalized, internal state of a game used within useGame.ts.
 * Uses camelCase and appropriate internal types (e.g., bigint for amounts).
 */
export interface NormalizedGame {
  id: string;
  status: GameStatus;
  walletAddress: string;
  betAmount: bigint; // Smallest unit (Lamports/Wei)
  currentRowIndex: number;
  finalMultiplier: number | null;
  rows: DisplayRow[]; // Use DisplayRow which includes cumulative multiplier
  selectedTiles: number[];
  commitmentHash: string | null;
  gameSeed: string | null;
  payoutError: string | null;
  payoutAmount: bigint | null; // Smallest unit (Lamports/Wei), derived if possible
  payoutTxSignature: string | null;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  // Add other fields normalized from the raw data
  refundAmount?: number | null; // Keep as number for now, matches DB type
  refundNotified?: boolean;
  onchainGameId?: string | null;
  isVerified?: boolean;
  // Include any other relevant fields from the database.Game or API responses
  // that are needed by useGame, ensuring they are camelCased.
  payoutAttempts?: number;
  errorReason?: string | null;
  requiresManualAction?: boolean;
  refundTxSignature?: string | null;
  app?: string;
}

/**
 * Represents the game state specifically prepared for client-side consumption.
 * This might involve hiding sensitive information (like future death tiles)
 * or adding pre-formatted fields.
 */
export interface ClientGame extends Omit<NormalizedGame, 'rows'> {
  rows: DisplayRow[]; // Ensure rows are DisplayRow, which include multipliers etc.
  // Note: deathTileIndex within DisplayRow might be nullified for active/future rows
  // by the prepareGameForClient function.
  hasUpcomingGames: boolean; // Add field to indicate pending games
}

export type CurrentGameServer = Database['public']['Tables']['games']['Row'] & {
  rows: DisplayRow[];
  selected_tiles: number[] | number[][];
};

export type CurrentGame = CamelCasedPropertiesDeep<Database['public']['Tables']['games']['Row']> & {
  rows: DisplayRow[];
  algoVersion: string;
};

export interface ActiveGame {
  currentGame: CurrentGame;
  previousGame: CurrentGame;
}

export interface GameWithOptionalUsername extends CurrentGame {
  username?: string;
}

// ------------------------------------------------ DEATH RACE
export type CurrentDeathRaceGame = CurrentGame & {
  selectedTiles: number[];
};

export type ActiveDeathRaceGame = ActiveGame & {
  currentGame: CurrentDeathRaceGame;
  previousGame: CurrentDeathRaceGame;
};

// ------------------------------------------------ LASER PARTY
export type CurrentLaserPartyGame = CurrentGame & {
  selectedTiles: number[][];
  rows: LPDisplayRow[];
};

export type ActiveLaserPartyGame = ActiveGame & {
  currentGame: CurrentLaserPartyGame;
  previousGame: CurrentLaserPartyGame;
};

export type CurrentLaserPartyGameServer = CurrentGameServer & {
  rows: LPDisplayRow[];
};
