import { LPDisplayRow } from '@/components/death-race/types';
import { CurrentGame } from '@/lib/types/game';
export interface CellSelection {
  row: number;
  col: number;
}

export type GamePhase = 'pregame' | 'selecting' | 'countdown' | 'laser' | 'shrinking' | 'over';

export type TargetDimension = 'row' | 'col';

export interface LaserTarget {
  dimension: TargetDimension;
  index: number;
}

export interface RoundOutcome {
  laserTarget: LaserTarget | null;
  didPlayerDie: boolean;
  pick: CellSelection;
}

// Laser Party specific extensions to the standard CurrentGame type
export type LaserPartyGameState = CurrentGame & {
  rows: LPDisplayRow[];
};

// For backward compatibility during migration
export interface GameState extends LaserPartyGameState {}

export type LocalAnimationPhase = 'idle' | 'countdown' | 'laser' | 'shrinking' | 'dying' | 'dead';
