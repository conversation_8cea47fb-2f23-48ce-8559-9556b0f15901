import { DisplayRow, LPDisplayRow } from '@/components/death-race/types';
import { parseEther } from 'viem';
import React from 'react';
import { Icon, IconSpriteType } from '@/components/ui/icons/Icon';

// ------------------------------------------------------------ ENVIRONMENT ------------------------------------------------------------

export const IS_LOCAL_ENV = process.env.NODE_ENV === 'development';
export const IS_DF_PREVIEW_ENV = process.env.NEXT_PUBLIC_DF_ENV === 'preview';
export const IS_DF_DEV_ENV = process.env.NEXT_PUBLIC_DF_ENV === 'dev';

// ------------------------------------------------------------ DEFAULT GAME CONSTANTS ------------------------------------------------------------
export const DEFAULT_GAME_NAME = 'Death Fun';
export const DEFAULT_GAME_DESCRIPTION = "Don't die.";
export const X_USERNAME = 'deathfungame';

// Laser Party SEO Constants
export const LASER_PARTY_GAME_NAME = 'Laser Party | Death.fun';
export const LASER_PARTY_GAME_DESCRIPTION = "Don't get lasered.";

// LINKS
export const SUPPORT_LINK = 'https://t.me/DeathFun_SupportBot';
export const DOCS_LINK = 'https://death-fun.notion.site/';

// ALGO_VERSION
export const ALGO_VERSION = 'v1';

// Demo Mode
export const ENABLE_DEMO_MODE = true;

// Death Race Game Constants
export const DEATH_RACE_TOTAL_ROWS = 25;
export const DEATH_RACE_MIN_TILES = 2;
export const DEATH_RACE_MAX_TILES = 7;

export const DEATH_RACE_HOUSE_EDGE = 0.05; // 5% house edge
export const DEATH_RACE_MAX_PROFIT_PERCENTAGE = 0.05; // How much can players win as a percentage of the total pot?
export const DEATH_FUN_MIN_BET_ETH = parseEther('0.001');
export const DEATH_RACE_MAX_BET_PERCENTAGE = 0.01; // How much can players bet as a percentage of the total pot?

// Points program: how many points per 1 ETH
export const POINTS_ETH_RATIO = 150;

// Abstract Portal Voting
export const ABSTRACT_VOTING_IDS = {
  death_race: 154, // Death Race app ID in Abstract portal
  laser_party: 167, // Laser Party app ID in Abstract portal
} as const;

// Legacy constant for backward compatibility
export const ABSTRACT_VOTING_ID = ABSTRACT_VOTING_IDS.death_race;

export const ABSTRACT_VOTING_CONTRACT_ADDRESS =
  '******************************************' as const; // Abstract voting contract address

// Helper function to get voting ID based on game type
export const getVotingIdForGameType = (gameType?: string | null): number => {
  if (gameType === 'laser_party') {
    return ABSTRACT_VOTING_IDS.laser_party;
  }
  return ABSTRACT_VOTING_IDS.death_race; // Default to death race
};

export const TESTNET_CLAIM_AMOUNT = parseEther('0.01');
export const TESTNET_CLAIM_THRESHOLD = parseEther('0.01');

// Referral System Constants
export const REFERRAL_PERCENTAGE = 0.0025; // 0.25% of bet amount goes to referrer
export const MINIMUM_CLAIM_AMOUNT = parseEther('0.001');

// Signature Constants
export const SIGNATURE_EXPIRY = 60 * 60 * 14 * 1000; // 10 seconds in milliseconds for testing

// Default row configuration
export const DEFAULT_ROW_CONFIG = [
  { tiles: 7 },
  { tiles: 6 },
  { tiles: 5 },
  { tiles: 4 },
  { tiles: 3 },
  { tiles: 4 },
  { tiles: 5 },
  { tiles: 6 },
  { tiles: 7 },
  { tiles: 6 },
  { tiles: 5 },
  { tiles: 4 },
  { tiles: 3 },
  { tiles: 4 },
  { tiles: 5 },
  { tiles: 6 },
  { tiles: 7 },
  { tiles: 6 },
  { tiles: 5 },
  { tiles: 4 },
  { tiles: 3 },
  { tiles: 4 },
  { tiles: 5 },
  { tiles: 6 },
  { tiles: 7 },
] as DisplayRow[];

export const DEFAULT_LASER_PARTY_ROW_CONFIG = [
  { tiles: 10, dimension: 'col' },
  { tiles: 10, dimension: 'row' },
  { tiles: 9, dimension: 'col' },
  { tiles: 9, dimension: 'row' },
  { tiles: 8, dimension: 'col' },
  { tiles: 8, dimension: 'row' },
  { tiles: 7, dimension: 'col' },
  { tiles: 7, dimension: 'row' },
  { tiles: 6, dimension: 'col' },
  { tiles: 6, dimension: 'row' },
  { tiles: 5, dimension: 'col' },
  { tiles: 5, dimension: 'row' },
  { tiles: 4, dimension: 'col' },
  { tiles: 4, dimension: 'row' },
  { tiles: 3, dimension: 'col' },
  { tiles: 3, dimension: 'row' },
  { tiles: 2, dimension: 'col' },
  { tiles: 2, dimension: 'row' },
  { tiles: 1, dimension: 'col' },
  { tiles: 1, dimension: 'row' },
] as LPDisplayRow[];

export const IS_MAINNET = process.env.NEXT_PUBLIC_ABSTRACT_CHAIN_ID === '2741';

// Daily Leaderboard Constants
export const DAILY_LEADERBOARD_TOTAL_POINTS = 5000;
export const DAILY_LEADERBOARD_NUM_WINNERS = 50;
// Daily leaderboard reset time (Eastern Time, 24-hour format 'HH:mm')
export const DAILY_LEADERBOARD_RESET_TIME_ET = '12:00'; // 12:00 PM ET

// Daily Leaderboard Feature Flag
// false = disabled for everyone
// 'admin' = enabled only for admins
// true = enabled for everyone
export const DAILY_LEADERBOARD_ENABLED: false | 'admin' | true = true;

// Game Types
export const DEATH_RACE_GAME_TYPE = 'death_race';
export const LASER_PARTY_GAME_TYPE = 'laser_party';
export const GAME_TYPES = [DEATH_RACE_GAME_TYPE, LASER_PARTY_GAME_TYPE] as const;

// Streak and Multiplier System Constants
export const MAX_STREAK_DAYS = 30;
export const BASE_MULTIPLIER = 1.0;
export const MULTIPLIER_INCREMENT_PER_DAY = 0.02; // 0.02x per day
export const MAX_MULTIPLIER = BASE_MULTIPLIER + MAX_STREAK_DAYS * MULTIPLIER_INCREMENT_PER_DAY; // 1.6x at 30 days
// Streak reset timezone (Eastern Time)
export const STREAK_RESET_TIMEZONE_ET = 'America/New_York';

// ------------------------------------------------ LASER PARTY

export const LP_HOUSE_EDGE = 0.95;

export const LP_GRID_SIZE = 10;
export const LP_MIN_GRID_SIZE = 2;
export const LP_MAX_GRID_SIZE = 10;
export const LASER_DURATION = 0.175; // seconds
export const LASER_HOLD = 0.25; // seconds (extra hold after laser fires)
export const LP_SHRINK_PAUSE = 0.5; // seconds
export const LP_MIN_BET = 10; // Minimum bet amount
export const LP_MAX_TURNS = 18; // Max turns for Mode C progress bar
export const LASER_SCAN_STEP_DURATION = 0.035; // seconds per scan step (2x speed)

// ------------------------------------------------------------ COLOR PALETTES ------------------------------------------------------------
export const COLOR_PALETTES = {
  deathRace: {
    '--gc-50': '78 92% 95%',
    '--gc-100': '80 89% 89%',
    '--gc-200': '81 88% 80%',
    '--gc-300': '82 85% 67%',
    '--gc-400': '83 78% 55%',
    '--gc-500': '84 81% 44%',
    '--gc-600': '85 85% 35%',
    '--gc-700': '86 78% 27%',
    '--gc-800': '88 72% 22%',
    '--gc-900': '89 61% 20%',
    '--gc-950': '88 80% 10%',
  },
  laserParty: {
    '--gc-50': '78 92% 95%',
    '--gc-100': '80 89% 89%',
    '--gc-200': '81 88% 80%',
    '--gc-300': '82 85% 67%',
    '--gc-400': '83 78% 55%',
    '--gc-500': '84 81% 44%',
    '--gc-600': '85 85% 35%',
    '--gc-700': '86 78% 27%',
    '--gc-800': '88 72% 22%',
    '--gc-900': '89 61% 20%',
    '--gc-950': '88 80% 10%',
  },
} as const;

export type ColorPalette = keyof typeof COLOR_PALETTES;

const GAME_COLOR_MAP: Record<GameType, keyof typeof COLOR_PALETTES> = {
  death_race: 'deathRace',
  laser_party: 'laserParty',
};

export const getGameColor = (gameType: GameType, token: keyof typeof COLOR_PALETTES.deathRace) => {
  const paletteKey = GAME_COLOR_MAP[gameType];
  return COLOR_PALETTES[paletteKey][token];
};

// SideNavButton specific colors
export const SIDE_NAV_COLORS = {
  death_race: {
    text: 'lime-400',
    bg: 'lime-900',
  },
  laser_party: {
    text: 'red-500',
    bg: 'red-900',
  },
} as const;

export const getSideNavColor = (gameType: GameType) => {
  return SIDE_NAV_COLORS[gameType];
};

// ------------------------------------------------------------ GAMES ------------------------------------------------------------
export type GameType = (typeof GAME_TYPES)[number];

export type GameMeta = {
  gameType: GameType;
  displayName: string;
  shortName: string;
  logoId: IconSpriteType;
  route: string;
  theme: typeof COLOR_PALETTES.deathRace | typeof COLOR_PALETTES.laserParty;
  new?: boolean; // Optional flag to mark games as new
};

export const GAME_META_BY_TYPE: Record<GameType, GameMeta> = {
  death_race: {
    gameType: 'death_race',
    displayName: 'Death.Fun',
    shortName: 'Death Fun',
    logoId: 'logo',
    route: '/',
    theme: COLOR_PALETTES.deathRace,
  },
  laser_party: {
    gameType: 'laser_party',
    displayName: 'Laser Party',
    shortName: 'Laser Party',
    logoId: 'laser',
    route: '/laser',
    theme: COLOR_PALETTES.laserParty,
    new: true, // Mark as new
  },
} as const;

export const GAMES_META: Array<GameMeta> = Object.values(GAME_META_BY_TYPE);
