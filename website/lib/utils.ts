import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { millify } from 'millify';
import { GameMeta, GAMES_META, LP_HOUSE_EDGE } from './constants';
import { formatEther } from 'viem';
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatNumber(num: number, decimalCount: number = 2): string {
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';

  // Special case, don't want 'k' used until 4 digits
  if (absNum < 10000) {
    // Check if it's a whole number
    const isWholeNumber = num % 1 === 0;
    return (
      sign +
      absNum.toLocaleString(undefined, {
        minimumFractionDigits: isWholeNumber ? 0 : decimalCount,
        maximumFractionDigits: decimalCount,
      })
    );
  }

  return millify(num, {
    precision: decimalCount, // Always show 4 total digits (3 after decimal + 1 before)
  });
}

export function formatCurrency(num: number): string {
  return num.toLocaleString(undefined, {
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
  });
}

export function formatSOL(num: number): string {
  return `${formatCurrency(num)} SOL`;
}

/**
 * Format a hash or seed value to show only the first and last 6 characters
 * with an ellipsis in between
 */
export function formatHashOrSeed(value: string | null): string {
  if (!value) return '';
  if (value.length <= 12) return value;
  return `${value.substring(0, 6)}...${value.substring(value.length - 6)}`;
}

export function formatWalletAddress(address: string): string {
  if (!address) return '';
  if (address.length <= 8) return address;
  return `${address.slice(0, 4)}..${address.slice(-4)}`;
}

/**
 * Converts a decimal (e.g. 0.01) to a fraction for BigInt math.
 * @param decimal The decimal value to convert (e.g. 0.01 for 1%)
 * @param precision The denominator to use (default: 1,000,000)
 * @returns { numerator: bigint, denominator: bigint }
 */
export function decimalToFraction(
  decimal: number,
  precision = 1_000_000
): { numerator: bigint; denominator: bigint } {
  const denominator = BigInt(precision);
  const numerator = BigInt(Math.round(decimal * precision));
  return { numerator, denominator };
}

// --------------------------------------------- ROLES
export function isSuperAdmin(role?: string): boolean {
  return role === 'super-admin';
}

export function isAdmin(role?: string): boolean {
  return role === 'admin' || role === 'super-admin';
}

// ------------------------------------------------------------------------------------------ LASER PARTY

// --------------------------------------------- GAMES
export function getGameMeta(pathname: string, gameType: string | null): GameMeta {
  const gameByRoute = GAMES_META.find((game) => game.route === pathname);
  const gameByType = GAMES_META.find((game) => game.gameType === gameType);

  return gameByRoute || gameByType || GAMES_META[0];
}
