'use client';

import React, { createContext, useContext, useState, useEffect, Suspense } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Route } from 'next';
import { GAMES_META } from '@/lib/constants';

// SearchParams Context to provide search params safely across the app
interface SearchParamsContextType {
  searchParams: URLSearchParams;
  getParam: (key: string) => string | null;
}

const SearchParamsContext = createContext<SearchParamsContextType>({
  searchParams: new URLSearchParams(),
  getParam: () => null,
});

export const useSearchParamsContext = () => useContext(SearchParamsContext);

// Component that safely wraps useSearchParams with proper Suspense boundary
function SearchParamsHandler({ children }: { children: React.ReactNode }) {
  const searchParams = useSearchParams();

  const getParam = (key: string) => searchParams.get(key);

  return (
    <SearchParamsContext.Provider value={{ searchParams, getParam }}>
      {children}
    </SearchParamsContext.Provider>
  );
}

// Wrapper component with Suspense boundary
export function SearchParamsProvider({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<div></div>}>
      <SearchParamsHandler>{children}</SearchParamsHandler>
    </Suspense>
  );
}

export type ViewType = 'game' | 'leaderboard';

interface ViewContextType {
  activeView: ViewType;
  setActiveView: (view: ViewType) => void;
  isHomePage: boolean;
}

const ViewContext = createContext<ViewContextType>({
  activeView: 'game',
  setActiveView: () => {},
  isHomePage: false,
});

export const useView = () => useContext(ViewContext);

export function ViewProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { searchParams, getParam } = useSearchParamsContext();

  // Check if we're on the home page
  const isHomePage = pathname === '/';

  // Check if we're on any game route and get the current game
  const currentGame = GAMES_META.find((game) => game.route === pathname);
  const isGameRoute = !!currentGame;

  // Initialize state simply, effect will correct based on URL
  const [activeView, setActiveViewState] = useState<ViewType>('game');

  // Update the URL when the active view changes
  const setActiveView = (view: ViewType) => {
    setActiveViewState(view);

    // Use current game route if on a game page, fallback to Death Race
    const targetRoute = currentGame?.route || '/';
    const targetUrl = `${targetRoute}?view=${view}` as Route;

    if (!isGameRoute) {
      // If not on a game route, navigate to Death Race
      router.push(`/?view=${view}` as Route);
    } else {
      // Only push/replace if the URL param needs changing to avoid loops
      if (searchParams.get('view') !== view) {
        router.push(targetUrl, { scroll: false });
      }
    }
  };

  // Keep activeView state in sync with URL when on any game page
  useEffect(() => {
    if (isGameRoute) {
      // Read the current param directly inside the effect
      const currentViewParam = searchParams.get('view') as ViewType | null;
      const targetView = currentViewParam === 'leaderboard' ? 'leaderboard' : 'game';
      // Update state only if it differs from the URL-derived target
      setActiveViewState((prev) => (prev === targetView ? prev : targetView));
    }
    // We don't need to sync based on URL params if not on a game page
  }, [isGameRoute, searchParams]); // Depend on the searchParams object and game route status

  return (
    <ViewContext.Provider value={{ activeView, setActiveView, isHomePage }}>
      {children}
    </ViewContext.Provider>
  );
}
