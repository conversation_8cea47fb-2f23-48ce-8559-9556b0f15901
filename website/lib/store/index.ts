import { create, useStore } from 'zustand';
import { createModalsSlice, ModalsSlice } from './slices/modalsSlice';
import { devtools } from 'zustand/middleware';
import { createContext, useContext } from 'react';

// --- create store
type GlobalStore = ModalsSlice;

export const createGlobalStore = () =>
  create<GlobalStore>()(
    devtools(
      (...args) => ({
        ...createModalsSlice(...args),
      }),
      {
        name: typeof window !== 'undefined' ? window.location.host : 'death.fun',
      }
    )
  );

// ------------------------------------------------ These are required for nextjs app router
// --- create context
export type GlobalStoreApi = ReturnType<typeof createGlobalStore>;
export const GlobalStoreContext = createContext<GlobalStoreApi | undefined>(undefined);

// --- useGlobalStore hook
export const useGlobalStore = <T>(selector: (store: GlobalStore) => T): T => {
  const globalStoreContext = useContext(GlobalStoreContext);

  if (!globalStoreContext) {
    throw new Error(`useGlobalStore must be used within GlobalStoreProvider`);
  }

  return useStore(globalStoreContext, selector);
};
