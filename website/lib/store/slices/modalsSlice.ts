import { StateCreator } from 'zustand';

export type ModalType = 'noFunds' | 'bridge' | 'faq';

export interface ModalsSlice {
  noFunds: boolean;
  bridge: boolean;
  faq: boolean;
  toggleModals: (modalTypes: { [key in ModalType]?: boolean }) => void;
}

export const createModalsSlice: StateCreator<
  ModalsSlice,
  [['zustand/devtools', never]],
  [],
  ModalsSlice
> = (set) => ({
  noFunds: false,
  bridge: false,
  faq: false,
  toggleModals: (modalTypes) =>
    set(modalTypes, undefined, { type: 'toggleModals', payload: modalTypes }),
});
