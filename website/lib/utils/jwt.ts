import { importSPK<PERSON>, jwtVerify } from 'jose';

/**
 * Verifies a Privy JWT using the public key from the environment.
 * Throws if invalid. Returns the payload if valid.
 */
export async function verifyPrivyJWT(token: string): Promise<any> {
  const rawKey = process.env.NEXT_PUBLIC_PRIVY_PUBLIC_KEY;
  if (!rawKey) throw new Error('NEXT_PUBLIC_PRIVY_PUBLIC_KEY env variable is not set');
  // Ensure PEM format
  const pem = rawKey.includes('BEGIN PUBLIC KEY')
    ? rawKey
    : `-----BEGIN PUBLIC KEY-----\n${rawKey}\n-----END PUBLIC KEY-----`;
  const key = await importSPKI(pem, 'ES256');
  const { payload } = await jwtVerify(token, key, { algorithms: ['ES256'] });

  // Extract wallet address from linked_accounts if present
  let walletAddress: string | null = null;
  try {
    if (typeof payload.linked_accounts === 'string') {
      const linkedAccounts = JSON.parse(payload.linked_accounts);
      for (const acct of linkedAccounts) {
        if (
          acct.smart_wallets &&
          Array.isArray(acct.smart_wallets) &&
          acct.smart_wallets.length > 0
        ) {
          walletAddress = acct.smart_wallets[0].address;
          break;
        }
      }
    }
  } catch (e) {
    // Ignore parse errors, walletAddress stays null
  }

  return { ...payload, walletAddress };
}
