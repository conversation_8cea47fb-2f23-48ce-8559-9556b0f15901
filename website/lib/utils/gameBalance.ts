import 'server-only';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { GameType } from '@/lib/constants';

/**
 * Gets the effective balance for a game type using a single database query
 * - If game has max balance set: returns that max balance
 * - If game doesn't have max balance: gets contract balance from provider and returns contract_balance - sum(all_max_balances)
 */
export async function getEffectiveBalance(
  gameType: GameType,
  provider: { getContractBalance: () => Promise<{ balance: bigint; error: Error | null }> }
): Promise<{
  effectiveBalance: bigint;
  contractBalance: bigint;
  hasMaxBalance: boolean;
  error: Error | null;
}> {
  try {
    // --- get all active game max balances
    const { data: allBalances, error: queryError } = await supabaseServerClient
      .from('game_max_balances')
      .select('game_type, max_balance')
      .eq('active', true);
    if (queryError) {
      return {
        effectiveBalance: BigInt(0),
        contractBalance: BigInt(0),
        hasMaxBalance: false,
        error: new Error(queryError.message),
      };
    }

    // -- check if the specified game type has a max balance
    const gameMaxBalance = allBalances?.find((balance) => balance.game_type === gameType);
    if (gameMaxBalance) {
      const maxBalance = BigInt(gameMaxBalance.max_balance);
      return {
        effectiveBalance: maxBalance,
        contractBalance: BigInt(0),
        hasMaxBalance: true,
        error: null,
      };
    }

    // --- get contract balance
    const { balance: contractBalance, error: balanceError } = await provider.getContractBalance();
    if (balanceError) {
      return {
        effectiveBalance: BigInt(0),
        contractBalance: BigInt(0),
        hasMaxBalance: false,
        error: balanceError,
      };
    }

    // --- calculate effective balance
    const totalOtherMaxBalances = (allBalances || []).reduce((sum, balance) => {
      return sum + BigInt(balance.max_balance);
    }, BigInt(0));
    const effectiveBalance = contractBalance - totalOtherMaxBalances;

    return {
      effectiveBalance: effectiveBalance > BigInt(0) ? effectiveBalance : BigInt(0),
      contractBalance,
      hasMaxBalance: false,
      error: null,
    };
  } catch (err) {
    return {
      effectiveBalance: BigInt(0),
      contractBalance: BigInt(0),
      hasMaxBalance: false,
      error: err as Error,
    };
  }
}
