import { IBlockchainProvider } from '@/lib/utils/interfaces';
import { AbstractProviderDeprecated } from '@/lib/utils/abstract';
import { ABSTRACT_RPC_URL } from '@/lib/server/constants';

/**
 * Creates and returns a blockchain provider instance based on current environment variables.
 * This should ONLY be called in a server-side context (API routes, Server Components data fetching).
 */
export const getBlockchainProvider = (): IBlockchainProvider => {

  let provider: IBlockchainProvider;

  try {
    const abstractRpcUrl = ABSTRACT_RPC_URL;
    provider = new AbstractProviderDeprecated(abstractRpcUrl);
    return provider;
  } catch (error) {
    console.error('[Server Provider Factory] Failed to initialize blockchain provider:', error);
    throw new Error(
      `Failed to initialize server-side blockchain provider: ${error instanceof Error ? error.message : String(error)}`
    );
  }
};
