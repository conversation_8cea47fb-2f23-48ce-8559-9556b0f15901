import 'server-only';

/**
 * Checks if the given text is flagged as inappropriate by OpenAI Moderation API.
 * Uses the latest omni-moderation model with calibrated probability scores.
 * Returns false if the API is unavailable or returns an error (graceful degradation).
 */
export async function isHateSpeech(text: string): Promise<boolean> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.warn('OPENAI_API_KEY env variable is not set, skipping moderation check');
    return false;
  }

  try {
    const response = await fetch('https://api.openai.com/v1/moderations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        input: text,
        model: 'omni-moderation-latest', // Explicitly use the latest model
      }),
    });

    if (!response.ok) {
      console.warn(
        `OpenAI Moderation API returned ${response.status}: ${response.statusText}, skipping moderation check`
      );
      return false;
    }

    const data = await response.json();
    const result = data.results?.[0];

    if (!result) {
      console.warn(
        'OpenAI Moderation API returned unexpected response format, skipping moderation check'
      );
      return false;
    }

    const isFlagged = result.flagged;

    return isFlagged;
  } catch (error) {
    console.warn('OpenAI Moderation API error, skipping moderation check:', error);
    return false;
  }
}
