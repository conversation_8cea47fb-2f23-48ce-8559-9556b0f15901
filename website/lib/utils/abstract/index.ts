// Abstract L2-specific utility functions will reside here.

import {
  IAccountService,
  ITransactionService,
  IWalletProvider,
  IBlockchainConfig,
} from '@/lib/utils/interfaces'; // Use full path
import { abstractClientConfig } from './config';
import { AbstractAccountService } from './AbstractAccountService';
import { AbstractTransactionService } from './AbstractTransactionService';
import { <PERSON>h, Hex, TransactionReceipt } from 'viem';
// Wallet provider logic is handled client-side in BlockchainProvider.tsx

export class AbstractProviderDeprecated {
  public config: IBlockchainConfig;
  // Wallet functionality is accessed via client-side hooks (useBlockchain)
  // This provider class mainly bundles server-side usable services.
  public wallet: IWalletProvider;
  private accountService: IAccountService;
  private transactionService: ITransactionService;

  constructor(rpcUrl: string) {
    this.config = {
      ...abstractClientConfig,
      rpcUrl: rpcUrl, // Add the full RPC URL used by services
    };

    // Instantiate server-side services
    this.accountService = new AbstractAccountService(); // Uses env vars internally now
    this.transactionService = new AbstractTransactionService(); // Uses env vars internally now

    // Provide a dummy/placeholder wallet provider for the interface contract
    // Client will use the real one from BlockchainProvider context
    this.wallet = {
      isConnected: false,
      address: null,
      connect: async () => {
        console.warn('Connect called on server-side AbstractProvider');
      },
      disconnect: async () => {
        console.warn('Disconnect called on server-side AbstractProvider');
      },
      signMessage: async () => {
        throw new Error('SignMessage called on server-side AbstractProvider');
      },
    };
  }

  // --- Delegate methods --- //

  getBalance(address: string): Promise<bigint> {
    return this.accountService.getBalance(address);
  }

  formatCurrency(
    amount: bigint,
    options?: { showSymbol?: boolean; significantDigits?: number }
  ): string {
    return this.accountService.formatCurrency(amount, options);
  }

  prepareBetTransactionParams(
    senderAddress: string,
    betAmountWei: bigint
  ): Promise<{ to: string; value: bigint; data?: string; [key: string]: any }> {
    return this.transactionService.prepareBetTransactionParams(senderAddress, betAmountWei);
  }

  processIncomingTransaction(
    payload: any
  ): Promise<{ sender: string; amount: bigint; txHash: string; uniqueId: string }> {
    return this.transactionService.processIncomingTransaction(payload);
  }

  initiatePayout(recipientAddress: string, amountWei: bigint): Promise<{ txHash: string }> {
    return this.transactionService.initiatePayout(recipientAddress, amountWei);
  }

  getTxStatus(txHash: string): Promise<'pending' | 'confirmed' | 'failed' | 'unknown'> {
    // Cast needed as viem Hash type is `0x${string}`
    return this.transactionService.getTxStatus(txHash as `0x${string}`);
  }

  verifySignature(params: {
    address: string;
    message: string;
    signature: string;
  }): Promise<boolean> {
    return this.transactionService.verifySignature(params);
  }

  // --- Implement New Interface Methods by Delegating ---

  async getOnChainGameId(preliminaryGameId: string): Promise<bigint> {
    return this.transactionService.getOnChainGameId(preliminaryGameId);
  }

  async markGameLostOnChain(
    onChainGameId: bigint,
    selectedTiles: number[],
    gameSeed: `0x${string}`
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    // Ensure the internal service has the method before calling
    if (!('markGameLostOnChain' in this.transactionService)) {
      throw new Error(
        'markGameLostOnChain method not available on the internal transaction service.'
      );
    }
    // Type assertion needed as ITransactionService doesn't guarantee these methods
    const service = this.transactionService as AbstractTransactionService;
    return service.markGameLostOnChain(onChainGameId, selectedTiles, gameSeed);
  }

  async cashOutOnChain(
    onChainGameId: bigint,
    payoutAmountWei: bigint,
    selectedTiles: number[],
    gameSeed: Hex
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!('cashOutOnChain' in this.transactionService)) {
      throw new Error('cashOutOnChain method not available on the internal transaction service.');
    }
    const service = this.transactionService as AbstractTransactionService;
    return service.cashOutOnChain(onChainGameId, payoutAmountWei, selectedTiles, gameSeed);
  }

  async withdrawFunds(
    recipientAddress: Hex,
    amountWei: bigint
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!('withdrawFunds' in this.transactionService)) {
      throw new Error('withdrawFunds method not available on the internal transaction service.');
    }
    const service = this.transactionService as AbstractTransactionService;
    return service.withdrawFunds(recipientAddress, amountWei);
  }

  async waitForTransactionReceipt(
    txHash: Hash,
    confirmations?: number
  ): Promise<{ receipt: TransactionReceipt | null; error: Error | null }> {
    if (!('waitForTransactionReceipt' in this.transactionService)) {
      throw new Error(
        'waitForTransactionReceipt method not available on the internal transaction service.'
      );
    }
    const service = this.transactionService as AbstractTransactionService;
    return service.waitForTransactionReceipt(txHash, confirmations);
  }
}
