import {
  createPublicClient,
  createWalletClient,
  http,
  PublicClient,
  Hash,
  Transport,
  Chain,
  TransactionReceipt,
  Address,
  WriteContractParameters,
  Account,
  Abi,
  EstimateContractGasParameters,
  PrivateKeyAccount,
  createNonceManager,
  encodeAbiParameters,
  keccak256,
  WalletClient,
  NonceManager,
  ContractFunctionExecutionError,
} from 'viem';
import { ChainEIP712 } from 'viem/chains';
import DeathFun<PERSON>bi from '@/lib/blockchain/abi/DeathFun.json';
import OldDeathFunAbi from '@/lib/blockchain/abi/DeathRaceGame.json';
import AbstractVotingABI from '@/lib/blockchain/abi/AbstractVoting.json';
import {
  ABSTRACT_GAS_BUFFER,
  ABSTRACT_POLLING_INTERVAL,
  ABSTRACT_RPC_URL,
  ABSTRACT_TX_TIMEOUT,
  ABSTRACT_CONTRACT_ADDRESS,
  SERVER_SIGNER_PRIVATE_KEY,
  SERVER_SIGNER_PRIVATE_KEY_2,
  ABSTRACT_OLD_CONTRACT_ADDRESS,
} from '@/lib/server/constants';
import { abstractClientConfig } from './config';
import {
  createSessionClient,
  SessionConfig,
  SessionClient,
  SessionStatus,
} from '@abstract-foundation/agw-client/sessions';
import { PrivyClient } from '@privy-io/server-auth';
import { privateKeyToAccount } from 'viem/accounts';
import { createViemAccount } from '@privy-io/server-auth/viem';
import { jsonRpc } from 'viem/nonce';
import {
  ABSTRACT_VOTING_ID,
  ABSTRACT_VOTING_CONTRACT_ADDRESS,
  ALGO_VERSION,
  IS_MAINNET,
} from '@/lib/constants';
import { getSessionStatus } from '@abstract-foundation/agw-client/actions';
import { eip712WalletActions } from 'viem/zksync';
import { InsufficientBalanceError } from '@/lib/server/errors';

const privy = new PrivyClient(process.env.NEXT_PUBLIC_PRIVY_APP_ID!, process.env.PRIVY_APP_SECRET!);

type AdminType = 'deployer' | 'referral';
export class AbstractProvider {
  private transport: Transport;
  private publicClient: PublicClient<Transport, Chain>;
  private admins: {
    [key in AdminType]: {
      walletAccount: PrivateKeyAccount;
      nonceManager: NonceManager;
      walletClient: WalletClient<Transport, Chain>;
    };
  };
  private sessionClient: SessionClient | null = null;

  constructor() {
    this.transport = http(ABSTRACT_RPC_URL);
    const deployerNonceManager = createNonceManager({ source: jsonRpc() });
    const deployerWalletAccount = privateKeyToAccount(SERVER_SIGNER_PRIVATE_KEY, {
      nonceManager: deployerNonceManager,
    });
    const deployerWalletClient = createWalletClient({
      account: deployerWalletAccount,
      chain: abstractClientConfig.chain,
      transport: this.transport,
    }).extend(eip712WalletActions());
    const referralNonceManager = createNonceManager({ source: jsonRpc() });
    const referralWalletAccount = privateKeyToAccount(SERVER_SIGNER_PRIVATE_KEY_2, {
      nonceManager: referralNonceManager,
    });

    const referralWalletClient = createWalletClient({
      account: referralWalletAccount,
      chain: abstractClientConfig.chain,
      transport: this.transport,
    }).extend(eip712WalletActions());

    this.admins = {
      deployer: {
        nonceManager: deployerNonceManager,
        walletAccount: deployerWalletAccount,
        walletClient: deployerWalletClient,
      },
      referral: {
        nonceManager: referralNonceManager,
        walletAccount: referralWalletAccount,
        walletClient: referralWalletClient,
      },
    };
    this.publicClient = createPublicClient({
      chain: abstractClientConfig.chain,
      transport: this.transport,
    });
  }

  async init(
    walletAddress: Address,
    sessionConfig: SessionConfig
  ): Promise<{ sessionClient: SessionClient | null; error: Error | null }> {
    if (this.sessionClient) {
      return { sessionClient: this.sessionClient, error: null };
    }

    const serverWalletAccount = await createViemAccount({
      walletId: process.env.PRIVY_WALLET_ID!,
      address: process.env.NEXT_PUBLIC_SERVER_WALLET_ADDRESS as Address,
      // @ts-ignore
      privy: privy,
    });

    const sessionStatus2Message: Record<SessionStatus, string> = {
      0: 'not initialized',
      1: 'active',
      2: 'expired',
      3: 'revoked',
    };
    const sessionStatus = await getSessionStatus(
      this.publicClient,
      walletAddress as `0x${string}`,
      sessionConfig
    );
    if (sessionStatus > 1) {
      return {
        sessionClient: null,
        error: new Error(`Session is not active: ${sessionStatus2Message[sessionStatus]}`),
      };
    }

    this.sessionClient = createSessionClient({
      account: walletAddress as `0x${string}`,
      chain: abstractClientConfig.chain,
      signer: serverWalletAccount,
      session: sessionConfig,
      transport: this.transport,
    });

    return { sessionClient: this.sessionClient, error: null };
  }

  // Session-specific methods
  async createGame({
    walletAddress,
    sessionConfig,
    preliminaryGameId,
    betAmount,
    commitmentHash,
    rowConfig,
    releaseVersion,
  }: {
    walletAddress: string;
    sessionConfig: SessionConfig;
    preliminaryGameId: string;
    betAmount: bigint;
    commitmentHash: string;
    rowConfig: number[];
    releaseVersion: string;
  }): Promise<{ txHash: Hash | null; error: Error | null }> {
    try {
      if (releaseVersion === 'v2') {
        const { txHash: migrateGameCounterTxHash, error: migrateGameCounterError } =
          await this.migrateGameCounter();
        if (
          migrateGameCounterError &&
          migrateGameCounterError.message !== 'v2 game counter is already set'
        ) {
          return {
            txHash: null,
            error: migrateGameCounterError,
          };
        }
        if (migrateGameCounterTxHash) {
          console.log(
            `[Abstract Service] Migrated game counter to v2: ${migrateGameCounterTxHash}`
          );
        }
      }

      const deadline = Math.floor(Date.now() / 1000) + 900; // 15 minutes from now
      const typesToSign = [
        { type: 'string', name: 'domain' },
        { type: 'string', name: 'preliminaryGameId' },
        { type: 'bytes32', name: 'gameSeedHash' },
        { type: 'string', name: 'algoVersion' },
        { type: 'string', name: 'gameConfig' },
        { type: 'address', name: 'player' },
        { type: 'uint256', name: 'betAmountWei' },
        { type: 'uint256', name: 'deadline' },
      ];
      const paramsToSign = [
        releaseVersion === 'v1' ? 'DeathRaceGame:createGame' : 'DeathFun:createGame',
        preliminaryGameId,
        commitmentHash,
        ALGO_VERSION,
        JSON.stringify({ rowConfig }),
        walletAddress as Address,
        betAmount,
        deadline,
      ];
      const abiEncoded = encodeAbiParameters(typesToSign, paramsToSign);
      const hashToSign = keccak256(abiEncoded);
      const serverSignature = await this.admins.deployer.walletAccount.signMessage({
        message: { raw: hashToSign },
      });

      const { txHash, error } = await this.executeTransaction(
        walletAddress as Address,
        sessionConfig,
        {
          account: walletAddress as Address,
          address:
            releaseVersion === 'v1' ? ABSTRACT_OLD_CONTRACT_ADDRESS : ABSTRACT_CONTRACT_ADDRESS,
          chain: abstractClientConfig.chain,
          abi: releaseVersion === 'v1' ? (OldDeathFunAbi.abi as Abi) : (DeathFunAbi.abi as Abi),
          functionName: 'createGame',
          args: [
            preliminaryGameId,
            commitmentHash,
            ALGO_VERSION,
            JSON.stringify({ rowConfig }),
            deadline,
            serverSignature,
          ],
          value: betAmount,
        }
      );
      if (
        error &&
        error instanceof ContractFunctionExecutionError &&
        (error.message.includes('function_selector = 0xe7931438') ||
          error.message.includes('Insufficient balance'))
      ) {
        return {
          txHash: null,
          error: new InsufficientBalanceError(error.message),
        };
      }
      return { txHash, error };
    } catch (error: any) {
      return {
        txHash: null,
        error: new Error(`Failed to create game: ${error.message || error}`),
      };
    }
  }

  async cashOut({
    walletAddress,
    sessionConfig,
    onChainGameId,
    payoutAmountWei,
    selectedTiles,
    gameSeed,
    releaseVersion,
    isServer,
  }: {
    walletAddress: Address;
    sessionConfig: SessionConfig | null;
    onChainGameId: bigint;
    payoutAmountWei: bigint;
    selectedTiles: number[] | number[][];
    gameSeed: string;
    releaseVersion: string;
    isServer: boolean;
  }): Promise<{ txHash: Hash | null; error: Error | null }> {
    switch (releaseVersion) {
      case 'v1':
        return this.executeServerTransaction(
          {
            account: this.admins.deployer.walletAccount,
            address: ABSTRACT_OLD_CONTRACT_ADDRESS,
            chain: abstractClientConfig.chain,
            abi: OldDeathFunAbi.abi as Abi,
            functionName: 'cashOut',
            args: [onChainGameId, payoutAmountWei, JSON.stringify({ selectedTiles }), gameSeed],
          },
          'deployer'
        );
      case 'v2':
        if (!sessionConfig && !isServer) {
          return {
            txHash: null,
            error: new Error('Session config is required for v2'),
          };
        }
        try {
          const deadline = Math.floor(Date.now() / 1000) + 900; // 15 minutes from now
          const typesToSign = [
            { type: 'string', name: 'domain' },
            { type: 'uint256', name: 'onChainGameId' },
            { type: 'uint256', name: 'payoutAmountWei' },
            { type: 'string', name: 'gameState' },
            { type: 'string', name: 'gameSeed' },
            { type: 'uint256', name: 'deadline' },
          ];
          const paramsToSign = [
            'DeathFun:cashOut',
            onChainGameId,
            payoutAmountWei,
            JSON.stringify({ selectedTiles }),
            gameSeed,
            deadline,
          ];
          const abiEncoded = encodeAbiParameters(typesToSign, paramsToSign);
          const hashToSign = keccak256(abiEncoded);
          const serverSignature = await this.admins.deployer.walletAccount.signMessage({
            message: { raw: hashToSign },
          });

          if (isServer) {
            return await this.executeServerTransaction(
              {
                account: this.admins.deployer.walletAccount,
                address: ABSTRACT_CONTRACT_ADDRESS,
                chain: abstractClientConfig.chain,
                abi: DeathFunAbi.abi as Abi,
                functionName: 'cashOut',
                args: [
                  onChainGameId,
                  payoutAmountWei,
                  JSON.stringify({ selectedTiles }),
                  gameSeed,
                  deadline,
                  serverSignature,
                ],
              },
              'deployer'
            );
          }

          return await this.executeTransaction(walletAddress, sessionConfig!, {
            account: walletAddress,
            address: ABSTRACT_CONTRACT_ADDRESS,
            chain: abstractClientConfig.chain,
            abi: DeathFunAbi.abi as Abi,
            functionName: 'cashOut',
            args: [
              onChainGameId,
              payoutAmountWei,
              JSON.stringify({ selectedTiles }),
              gameSeed,
              deadline,
              serverSignature,
            ],
          });
        } catch (error: any) {
          return {
            txHash: null,
            error: new Error(`Failed to cash out: ${error.message || error}`),
          };
        }
      default:
        return {
          txHash: null,
          error: new Error(`Invalid release version: ${releaseVersion}`),
        };
    }
  }

  async markGameAsLost({
    walletAddress,
    sessionConfig,
    onChainGameId,
    selectedTiles,
    gameSeed,
    releaseVersion,
    isServer,
  }: {
    walletAddress: string;
    sessionConfig: SessionConfig | null;
    onChainGameId: bigint;
    selectedTiles: number[] | number[][];
    gameSeed: string;
    releaseVersion: string;
    isServer: boolean;
  }): Promise<{ txHash: Hash | null; error: Error | null }> {
    switch (releaseVersion) {
      case 'v1':
        return this.executeServerTransaction(
          {
            account: this.admins.deployer.walletAccount,
            address: ABSTRACT_OLD_CONTRACT_ADDRESS,
            chain: abstractClientConfig.chain,
            abi: OldDeathFunAbi.abi as Abi,
            functionName: 'markGameAsLost',
            args: [onChainGameId, JSON.stringify({ selectedTiles }), gameSeed],
          },
          'deployer'
        );
      case 'v2':
        if (!sessionConfig && !isServer) {
          return {
            txHash: null,
            error: new Error('Session config is required for v2'),
          };
        }
        try {
          const deadline = Math.floor(Date.now() / 1000) + 900; // 15 minutes from now
          const typesToSign = [
            { type: 'string', name: 'domain' },
            { type: 'uint256', name: 'onChainGameId' },
            { type: 'string', name: 'gameState' },
            { type: 'string', name: 'gameSeed' },
            { type: 'uint256', name: 'deadline' },
          ];
          const paramsToSign = [
            'DeathFun:markGameAsLost',
            onChainGameId,
            JSON.stringify({ selectedTiles }),
            gameSeed,
            deadline,
          ];
          const abiEncoded = encodeAbiParameters(typesToSign, paramsToSign);
          const hashToSign = keccak256(abiEncoded);
          const serverSignature = await this.admins.deployer.walletAccount.signMessage({
            message: { raw: hashToSign },
          });

          if (isServer) {
            return await this.executeServerTransaction(
              {
                account: this.admins.deployer.walletAccount,
                address: ABSTRACT_CONTRACT_ADDRESS,
                chain: abstractClientConfig.chain,
                abi: DeathFunAbi.abi as Abi,
                functionName: 'markGameAsLost',
                args: [
                  onChainGameId,
                  JSON.stringify({ selectedTiles }),
                  gameSeed,
                  deadline,
                  serverSignature,
                ],
              },
              'deployer'
            );
          }

          return await this.executeTransaction(walletAddress as Address, sessionConfig!, {
            account: walletAddress as Address,
            address: ABSTRACT_CONTRACT_ADDRESS,
            chain: abstractClientConfig.chain,
            abi: DeathFunAbi.abi as Abi,
            functionName: 'markGameAsLost',
            args: [
              onChainGameId,
              JSON.stringify({ selectedTiles }),
              gameSeed,
              deadline,
              serverSignature,
            ],
          });
        } catch (error: any) {
          return {
            txHash: null,
            error: new Error(`Failed to mark game as lost: ${error.message || error}`),
          };
        }
      default:
        return {
          txHash: null,
          error: new Error(`Invalid release version: ${releaseVersion}`),
        };
    }
  }

  async waitForTransactionReceipt(
    txHash: Hash
  ): Promise<{ receipt: TransactionReceipt | null; error: Error | null }> {
    let err: Error | null = null;
    let receipt: TransactionReceipt | null = null;
    for (let i = 0; i < 3; i++) {
      try {
        receipt = await this.publicClient.waitForTransactionReceipt({
          hash: txHash,
          pollingInterval: ABSTRACT_POLLING_INTERVAL,
          timeout: ABSTRACT_TX_TIMEOUT,
        });
        err = null;
        break;
      } catch (error: any) {
        console.error(
          `[Abstract Session Service] Error waiting for transaction receipt: retry ${i + 1} of 3: ${error.message || error}`
        );
        err = error;
        const delayMs = Math.pow(2, i) * 1000; // Exponential backoff 1s, 2s, 4s
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
    if (err) {
      return {
        receipt: null,
        error: new Error(`Failed to wait for transaction receipt: ${err.message || err}`),
      };
    }
    return { receipt, error: null };
  }

  async getContractBalance(): Promise<{ balance: bigint; error: Error | null }> {
    try {
      const balance = await this.publicClient.getBalance({
        address: ABSTRACT_CONTRACT_ADDRESS,
      });
      return { balance, error: null };
    } catch (error: any) {
      return {
        balance: BigInt(0),
        error: new Error(`Failed to get contract balance: ${error.message || error}`),
      };
    }
  }

  async getWalletBalance(
    walletAddress: Address
  ): Promise<{ balance: bigint; error: Error | null }> {
    try {
      const balance = await this.publicClient.getBalance({
        address: walletAddress,
      });
      return { balance, error: null };
    } catch (error: any) {
      return {
        balance: BigInt(0),
        error: new Error(`Failed to get wallet balance: ${error.message || error}`),
      };
    }
  }

  async getOnChainGameId(
    preliminaryGameId: string,
    releaseVersion: string
  ): Promise<{ onChainGameId: bigint; error: Error | null }> {
    try {
      const onChainGameId = await this.publicClient.readContract({
        address:
          releaseVersion === 'v1' ? ABSTRACT_OLD_CONTRACT_ADDRESS : ABSTRACT_CONTRACT_ADDRESS,
        abi: releaseVersion === 'v1' ? (OldDeathFunAbi.abi as Abi) : (DeathFunAbi.abi as Abi),
        functionName: 'preliminaryToOnChainId',
        args: [preliminaryGameId],
      });
      return { onChainGameId: onChainGameId as bigint, error: null };
    } catch (error: any) {
      return {
        onChainGameId: BigInt(0),
        error: new Error(`Failed to get on chain game id: ${error.message || error}`),
      };
    }
  }

  async getGameCounter(
    releaseVersion: string
  ): Promise<{ gameCounter: bigint; error: Error | null }> {
    let gameCounter: bigint = BigInt(0);
    let error: Error | null = null;
    try {
      if (releaseVersion === 'v1') {
        gameCounter = (await this.publicClient.readContract({
          address: ABSTRACT_OLD_CONTRACT_ADDRESS,
          abi: OldDeathFunAbi.abi as Abi,
          functionName: 'gameCounter',
        })) as bigint;
      } else {
        gameCounter = (await this.publicClient.readContract({
          address: ABSTRACT_CONTRACT_ADDRESS,
          abi: DeathFunAbi.abi as Abi,
          functionName: 'gameCounter',
        })) as bigint;
      }
    } catch (err: any) {
      console.error(
        `[Abstract Service] Error getting game counter for release version ${releaseVersion}: ${err.message || err}`
      );
      error = new Error(`Failed to get game counter: ${err.message || err}`);
    }
    return { gameCounter, error };
  }

  async hasVotedForApp(
    walletAddress: string,
    gameType?: string | null
  ): Promise<{ hasVoted: boolean; error: Error | null }> {
    try {
      const currentEpoch = await this.publicClient.readContract({
        address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
        abi: AbstractVotingABI,
        functionName: 'currentEpoch',
      });
      const userVotes = await this.publicClient.readContract({
        address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
        abi: AbstractVotingABI,
        functionName: 'getUserVotes',
        args: [walletAddress, currentEpoch],
      });

      // Import the helper function to get the correct voting ID
      const { getVotingIdForGameType } = await import('@/lib/constants');
      const votingId = getVotingIdForGameType(gameType);

      const hasVoted = userVotes ? (userVotes as bigint[]).includes(BigInt(votingId)) : false;
      return { hasVoted, error: null };
    } catch (error: any) {
      return {
        hasVoted: false,
        error: new Error(`Failed to check if user has voted for app: ${error.message || error}`),
      };
    }
  }

  async migrateGameCounter(): Promise<{ txHash: Hash | null; error: Error | null }> {
    const { gameCounter: v2GameCounter, error: v2GameCounterError } =
      await this.getGameCounter('v2');
    if (v2GameCounterError) {
      return {
        txHash: null,
        error: v2GameCounterError,
      };
    }
    const expectedV2GameCounter = IS_MAINNET ? BigInt(0) : BigInt(2);
    const shouldMigrate = v2GameCounter === expectedV2GameCounter;
    if (!shouldMigrate) {
      return {
        txHash: null,
        error: new Error('v2 game counter is already set'),
      };
    }
    const { gameCounter: v1GameCounter, error: v1GameCounterError } =
      await this.getGameCounter('v1');
    if (v1GameCounterError) {
      return {
        txHash: null,
        error: v1GameCounterError,
      };
    }

    const { txHash: setGameCounterTxHash, error: setGameCounterError } =
      await this.executeServerTransaction(
        {
          account: this.admins.deployer.walletAccount,
          address: ABSTRACT_CONTRACT_ADDRESS,
          chain: abstractClientConfig.chain,
          abi: DeathFunAbi.abi as Abi,
          functionName: 'setGameCounter',
          args: [v1GameCounter],
        },
        'deployer'
      );
    if (setGameCounterError) {
      return {
        txHash: null,
        error: setGameCounterError,
      };
    }

    const { receipt: setGameCounterReceipt, error: setGameCounterReceiptError } =
      await this.waitForTransactionReceipt(setGameCounterTxHash as Hash);
    if (setGameCounterReceiptError || setGameCounterReceipt?.status !== 'success') {
      console.error(
        `[Abstract Service] Failed to set game counter: ${setGameCounterReceiptError?.message || setGameCounterReceipt?.status}`
      );
      return {
        txHash: null,
        error: setGameCounterReceiptError,
      };
    }
    return {
      txHash: setGameCounterTxHash,
      error: null,
    };
  }

  async withdrawFunds(
    walletAddress: Address,
    amount: bigint
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (amount <= BigInt(0)) {
      return {
        txHash: null,
        error: new Error('Amount must be greater than 0'),
      };
    }
    return this.executeServerTransaction(
      {
        account: this.admins.deployer.walletAccount,
        address: ABSTRACT_CONTRACT_ADDRESS,
        chain: abstractClientConfig.chain,
        abi: DeathFunAbi.abi as Abi,
        functionName: 'withdrawFunds',
        args: [amount, walletAddress],
      },
      'deployer'
    );
  }

  async payReferral(
    walletAddress: Address,
    amount: bigint
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (amount <= BigInt(0)) {
      return {
        txHash: null,
        error: new Error('Amount must be greater than 0'),
      };
    }
    return this.executeServerTransaction(
      {
        account: this.admins.referral.walletAccount,
        address: ABSTRACT_CONTRACT_ADDRESS,
        chain: abstractClientConfig.chain,
        abi: DeathFunAbi.abi as Abi,
        functionName: 'payReferral',
        args: [amount, walletAddress],
      },
      'referral'
    );
  }

  private async estimateGas<
    const tx extends EstimateContractGasParameters<
      Abi,
      string,
      readonly unknown[],
      Chain | undefined
    >,
  >(tx: tx): Promise<{ gasToUse: bigint; error: Error | null }> {
    let err: Error | null = null;
    let gasToUse: bigint = BigInt(0);
    for (let i = 0; i < 3; i++) {
      try {
        err = null;
        const gas = await this.publicClient.estimateContractGas(tx);
        gasToUse = gasWithBuffer(gas);
      } catch (error: any) {
        err = error;
        const delayMs = Math.pow(2, i) * 1000; // Exponential backoff 1s, 2s, 4s
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
    if (err) {
      return {
        gasToUse: BigInt(0),
        error: err,
      };
    }
    return { gasToUse, error: null };
  }

  private async executeServerTransaction<
    const tx extends WriteContractParameters<
      Abi,
      string,
      readonly unknown[],
      ChainEIP712 | undefined,
      Account | undefined,
      Chain | undefined
    >,
  >(tx: tx, adminType: AdminType): Promise<{ txHash: Hash | null; error: Error | null }> {
    let err: Error | null = null;
    let txHash: Hash | null = null;
    const { walletAccount, nonceManager, walletClient } = this.admins[adminType];
    for (let i = 0; i < 3; i++) {
      try {
        err = null;
        const { gasToUse, error: gasError } = await this.estimateGas(
          tx as EstimateContractGasParameters<Abi, string, readonly unknown[], Chain | undefined>
        );
        if (gasError) {
          return { txHash: null, error: gasError };
        }
        txHash = await walletClient.writeContract({
          ...tx,
          gas: gasToUse,
        });
        break;
      } catch (error: any) {
        if (error instanceof Error && error.message.includes('nonce too low')) {
          console.log(`[Abstract Service] Nonce too low. Resetting nonceManager`);
          nonceManager.reset({
            address: walletAccount.address,
            chainId: abstractClientConfig.chain.id,
          });
          err = error;
          continue;
        }
        console.error(
          `[Abstract Service] Error sending transaction, retry ${i + 1} of 3: ${error.message || error}`
        );
        err = error;
        break;
      }
    }
    if (err) {
      return {
        txHash: null,
        error: err,
      };
    }
    return {
      txHash: txHash,
      error: null,
    };
  }

  private async executeTransaction<
    const tx extends WriteContractParameters<
      Abi,
      string,
      readonly unknown[],
      ChainEIP712 | undefined,
      Account | undefined,
      Chain | undefined
    >,
  >(
    walletAddress: Address,
    sessionConfig: SessionConfig,
    tx: tx
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    const { sessionClient, error: sessionError } = await this.init(walletAddress, sessionConfig);
    if (sessionError || !sessionClient) {
      return { txHash: null, error: sessionError };
    }

    // --- estimate gas
    const { gasToUse, error: gasError } = await this.estimateGas(
      tx as EstimateContractGasParameters<Abi, string, readonly unknown[], Chain | undefined>
    );
    if (gasError) {
      return { txHash: null, error: gasError };
    }

    let err: Error | null = null;
    let txHash: Hash | null = null;
    for (let i = 0; i < 3; i++) {
      try {
        err = null;
        txHash = await sessionClient.writeContract({
          ...tx,
          gas: gasToUse,
        });
        break;
      } catch (error: any) {
        console.error(
          `[Abstract Session Service] Error sending transaction, retry ${i + 1} of 3: ${error.message || error}`
        );
        err = error;
        const delayMs = Math.pow(2, i) * 1000; // Exponential backoff 1s, 2s, 4s
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
    if (err) {
      return { txHash: null, error: err };
    }
    return { txHash: txHash, error: null };
  }
}

const gasWithBuffer = (gas: bigint) => {
  return (gas * ABSTRACT_GAS_BUFFER) / BigInt(100);
};

export const getAbstractProvider = async (
  walletAddress: string,
  sessionConfig: SessionConfig | null
): Promise<AbstractProvider> => {
  try {
    const provider = new AbstractProvider();
    if (sessionConfig) {
      const { sessionClient, error: sessionError } = await provider.init(
        walletAddress as Address,
        sessionConfig
      );
      if (sessionError) {
        throw sessionError;
      }
    }

    return provider;
  } catch (error) {
    console.error('[Abstract Provider] Failed to initialize provider:', error);
    throw new Error(`Failed to initialize Abstract provider: ${error}`);
  }
};
