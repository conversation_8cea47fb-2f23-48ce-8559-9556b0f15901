import {
  ABSCAN_API_KEY,
  ABSTRACT_CONTRACT_ADDRESS,
  ABSTRACT_RPC_URL,
} from '@/lib/server/constants';
import { createPublicClient, http, Hash, PublicClient } from 'viem';
import { abstractClientConfig } from './config';

export const getLogs = async (
  onChainGameId: bigint,
  gameCreatedAt: Date
): Promise<{ txHash: Hash | null; error: Error | null }> => {
  try {
    const createdAt = Math.floor(gameCreatedAt.getTime() / 1000) - 20; // 20 seconds before
    const url = `${abstractClientConfig.abscanUrl}?module=block&action=getblocknobytime&timestamp=${createdAt}&apikey=${ABSCAN_API_KEY}&closest=before`;
    const response = await fetch(url);
    const data = (await response.json()) as { status: string; messsage: string; result: string };
    if (!data) {
      console.error(`no data found for game ${onChainGameId}`);
      return { txHash: null, error: new Error('no data found for game') };
    }
    const blockNumber = data.result;
    const publicClient = createPublicClient({
      chain: abstractClientConfig.chain, // Explicit cast
      transport: http(ABSTRACT_RPC_URL), // Explicit cast
    }) as PublicClient; // Explicit cast
    const logs = await publicClient.getLogs({
      address: ABSTRACT_CONTRACT_ADDRESS as Hash,
      event: {
        anonymous: false,
        inputs: [
          { indexed: true, internalType: 'uint256', name: 'onChainGameId', type: 'uint256' },
          { indexed: false, internalType: 'uint256', name: 'amount', type: 'uint256' },
          { indexed: true, internalType: 'address', name: 'recipient', type: 'address' },
        ],
        name: 'PayoutSent',
        type: 'event',
      },
      args: { onChainGameId },
      fromBlock: BigInt(blockNumber),
      toBlock: BigInt(blockNumber) + BigInt(2000),
    });
    if (logs.length > 0) {
      // Return the first (should only be one per game)
      return { txHash: logs[0].transactionHash as Hash, error: null };
    }
    return { txHash: null, error: new Error('no logs found for game') };
  } catch (error) {
    return { txHash: null, error: new Error('error getting logs') };
  }
};
