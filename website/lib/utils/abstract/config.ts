import { IBlockchainConfig, ICurrencyInfo } from '@/lib/utils/interfaces'; // Use full path
import { abstract, abstractTestnet } from 'viem/chains'; // Using mainnet definition from viem

// This config is intended for client-side usage and should only contain
// publicly accessible information or values derived from NEXT_PUBLIC_ env vars.

// Extract currency info from the viem chain definition
const ABSTRACT_CURRENCY: ICurrencyInfo = {
  symbol: abstract.nativeCurrency.symbol, // Should be ETH
  decimals: abstract.nativeCurrency.decimals, // Should be 18
  name: abstract.nativeCurrency.name, // Should be Ethereum
};

const isMainnet = process.env.NEXT_PUBLIC_ABSTRACT_CHAIN_ID === '2741';
// Client-safe configuration for Abstract
export const abstractClientConfig: Omit<IBlockchainConfig, 'rpcUrl'> = {
  chainId: isMainnet ? abstract.id : abstractTestnet.id, // Default to Abstract Testnet chainId
  explorerUrl: isMainnet
    ? abstract.blockExplorers?.native?.url
    : abstractTestnet.blockExplorers?.native?.url,
  currency: ABSTRACT_CURRENCY,
  chain: isMainnet ? abstract : abstractTestnet,
  abscanUrl: isMainnet ? 'https://api.abscan.org/api' : 'https://api-sepolia.abscan.org/api',
};
