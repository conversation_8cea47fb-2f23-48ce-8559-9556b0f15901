import { createPublicClient, http, Hex } from 'viem';
import { abstractClientConfig } from './config';
import AbstractVotingABI from '@/lib/blockchain/abi/AbstractVoting.json';
import { ABSTRACT_VOTING_ID } from '@/lib/constants';
import { ABSTRACT_VOTING_CONTRACT_ADDRESS } from '@/lib/server/constants';
import { VotingInfo } from '@/lib/types/voting';

// Create a public client for server-side contract reads
const publicClient = createPublicClient({
  chain: abstractClientConfig.chain,
  transport: http(process.env.ABSTRACT_RPC_URL),
});

/**
 * Get the current voting epoch
 */
export async function getCurrentEpoch(): Promise<bigint> {
  if (!ABSTRACT_VOTING_CONTRACT_ADDRESS) {
    throw new Error('Abstract Voting contract address not configured');
  }

  const currentEpoch = await publicClient.readContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'currentEpoch',
  });

  return currentEpoch as bigint;
}

/**
 * Get user's votes for a specific epoch
 */
export async function getUserVotesForEpoch(
  walletAddress: string,
  epoch: bigint
): Promise<bigint[]> {
  if (!ABSTRACT_VOTING_CONTRACT_ADDRESS) {
    throw new Error('Abstract Voting contract address not configured');
  }

  const userVotes = await publicClient.readContract({
    address: ABSTRACT_VOTING_CONTRACT_ADDRESS,
    abi: AbstractVotingABI,
    functionName: 'getUserVotes',
    args: [walletAddress as Hex, epoch],
  });

  return userVotes as bigint[];
}

/**
 * Check if user has voted for Death Race (App ID 154) in the current epoch
 */
export async function checkUserVotedForApp(walletAddress: string): Promise<boolean> {
  try {
    const currentEpoch = await getCurrentEpoch();
    const userVotes = await getUserVotesForEpoch(walletAddress, currentEpoch);

    // Check if Death Race app ID is in the user's votes
    return userVotes.includes(BigInt(ABSTRACT_VOTING_ID));
  } catch (error) {
    console.error('Error checking user vote status:', error);
    throw new Error('Failed to verify voting status');
  }
}

/**
 * Get detailed voting information for a user
 */
export async function getUserVotingInfo(walletAddress: string): Promise<VotingInfo> {
  try {
    const currentEpoch = await getCurrentEpoch();
    const userVotes = await getUserVotesForEpoch(walletAddress, currentEpoch);
    const hasVotedForDeathRace = userVotes.includes(BigInt(ABSTRACT_VOTING_ID));

    return {
      currentEpoch,
      userVotes,
      hasVotedForDeathRace,
      totalVotes: userVotes.length,
    };
  } catch (error) {
    console.error('Error getting user voting info:', error);
    throw new Error('Failed to get voting information');
  }
}
