import { IAccountService } from '@/lib/utils/interfaces'; // Use full path
import {
  createPublicClient,
  http,
  formatEther,
  parseEther,
  PublicClient,
  Chain,
  Transport,
} from 'viem';
import { abstract } from 'viem/chains'; // Or import the specific chain needed
import { abstractClientConfig } from './config';
import { ABSTRACT_RPC_URL } from '@/lib/server/constants';

export class AbstractAccountService implements IAccountService {
  private publicClient: PublicClient;
  private config = abstractClientConfig; // Use client-safe config for formatting info

  constructor() {
    // This service is primarily for server-side use (like checking payout wallet balance?)
    // It needs the server-side RPC URL.
    this.publicClient = createPublicClient({
      chain: abstract as Chain, // Explicit cast
      transport: http(ABSTRACT_RPC_URL) as Transport, // Explicit cast
    }) as PublicClient; // Explicit cast
  }

  async getBalance(address: string): Promise<bigint> {
    try {
      // Ensure address is a valid EVM address (basic check)
      if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
        throw new Error(`Invalid EVM address format: ${address}`);
      }
      const balanceWei = await this.publicClient.getBalance({
        address: address as `0x${string}`,
      });
      return balanceWei;
    } catch (error) {
      console.error(`[Abstract Service] Error fetching balance for ${address}:`, error);
      throw error; // Rethrow for the caller to handle
    }
  }

  formatCurrency(
    amount: bigint,
    options?: { showSymbol?: boolean; significantDigits?: number }
  ): string {
    const { showSymbol = true, significantDigits = 4 } = options || {};
    const decimals = this.config.currency.decimals;
    const symbol = this.config.currency.symbol;
    const amountEth = formatEther(amount); // Use viem's formatter
    const formattedAmount = Number(amountEth).toFixed(significantDigits);
    return showSymbol ? `${formattedAmount} ${symbol}` : formattedAmount;
  }
}
