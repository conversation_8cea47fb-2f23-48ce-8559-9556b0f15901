import { ITransactionService, IBlockchainConfig } from '@/lib/utils/interfaces'; // Use IBlockchainConfig and fix duplicate
import {
  createWalletClient,
  http,
  createPublicClient,
  PublicClient,
  Hash,
  hashMessage,
  Hex,
  Transport,
  Chain,
  WalletClient,
  Account,
  TransactionReceipt,
  NonceManager,
} from 'viem';
import { createNonceManager, jsonRpc } from 'viem/nonce';
import { privateKeyToAccount } from 'viem/accounts';
import DeathRaceGameAbi from '@/lib/blockchain/abi/DeathRaceGame.json'; // Import ABI
import {
  ABSTRACT_GAS_LIMIT,
  ABSTRACT_GAS_BUFFER,
  ABSTRACT_POLLING_INTERVAL,
  ABSTRACT_RPC_URL,
  ABSTRACT_TX_TIMEOUT,
} from '@/lib/server/constants';
import { abstractClientConfig } from './config';
import { eip712WalletActions } from 'viem/zksync';

// EIP-1271 Magic Value
const EIP1271_MAGIC_VALUE = '0x1626ba7e';

// ABI fragment for the EIP-1271 isValidSignature function
const isValidSignatureAbi = [
  {
    type: 'function',
    name: 'isValidSignature',
    inputs: [
      { name: '_hash', type: 'bytes32', internalType: 'bytes32' },
      { name: '_signature', type: 'bytes', internalType: 'bytes' },
    ],
    outputs: [{ name: 'magicValue', type: 'bytes4', internalType: 'bytes4' }],
    stateMutability: 'view',
  },
] as const;

export class AbstractTransactionService implements ITransactionService {
  private nonceManager: NonceManager;
  private contractAddress: Hex;
  private publicClient: PublicClient<Transport, Chain>;
  private serverWalletClient: WalletClient<Transport, Chain, Account> | null = null; // Added for server-side txs
  private serverAccount: Account | null = null; // Added for server-side txs
  public config: IBlockchainConfig; // Use IBlockchainConfig type

  constructor() {
    // This service requires server-side config
    const transport: Transport = http(ABSTRACT_RPC_URL);
    const chain: Chain = abstractClientConfig.chain;
    this.contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS as Hex;

    // Initialize config
    this.config = {
      currency: {
        name: 'Abstract Ether',
        symbol: 'ETH',
        decimals: 18,
      },
      explorerUrl: process.env.NEXT_PUBLIC_ABSTRACT_EXPLORER_URL || '',
      chainId: abstractClientConfig.chainId, // Default to Abstract Testnet chainId
      rpcUrl: ABSTRACT_RPC_URL,
      chain: chain,
      abscanUrl: '',
    };

    this.publicClient = createPublicClient({
      chain: chain,
      transport: transport,
    });
    this.nonceManager = createNonceManager({ source: jsonRpc() });

    // Initialize server wallet client if private key is available
    const privateKey = process.env.SERVER_SIGNER_PRIVATE_KEY as Hex | undefined;
    if (privateKey) {
      try {
        this.serverAccount = privateKeyToAccount(privateKey, {
          nonceManager: this.nonceManager,
        });
        this.serverWalletClient = createWalletClient({
          account: this.serverAccount,
          chain: chain,
          transport: transport,
        }).extend(eip712WalletActions());
        // console.timeEnd('serverWalletClient');
      } catch (error) {
        console.error('[Abstract Service] Failed to initialize server wallet client:', error);
        // Keep serverWalletClient as null if initialization fails
        this.serverAccount = null;
        this.serverWalletClient = null;
      }
    } else {
      console.warn(
        '[Abstract Service] SERVER_SIGNER_PRIVATE_KEY not found. Server cannot send transactions.'
      );
    }
  }

  async prepareBetTransactionParams(
    senderAddress: string,
    betAmountWei: bigint
  ): Promise<{ to: string; value: bigint; data?: string; [key: string]: any }> {
    // For Abstract/EVM simple ETH transfer
    const gameWalletAddress = process.env.NEXT_PUBLIC_ABSTRACT_GAME_WALLET_ADDRESS;
    if (!gameWalletAddress) {
      throw new Error(
        '[Server Env] Abstract game wallet address not configured (NEXT_PUBLIC_ABSTRACT_GAME_WALLET_ADDRESS).'
      );
    }
    // TODO: Add memo/data field if needed for custom row config later
    return {
      to: gameWalletAddress,
      value: betAmountWei,
      // No data needed for basic ETH transfer
    };
  }

  async processIncomingTransaction(
    payload: any // Expecting Alchemy webhook payload structure
  ): Promise<{ sender: string; amount: bigint; txHash: string; uniqueId: string }> {
    // TODO: Implement robust parsing and validation of Alchemy payload
    // Ref: https://docs.alchemy.com/reference/alchemy-notify-api-quickstart#understanding-webhook-payloads
    console.warn(
      'AbstractTransactionService.processIncomingTransaction needs implementation based on Alchemy payload structure.'
    );

    // Example structure (highly depends on Alchemy setup - transfers)
    const activity = payload?.event?.activity?.[0];
    if (!activity || payload?.type !== 'ADDRESS_ACTIVITY' /* adjust type based on subscription */) {
      throw new Error('Invalid or non-transfer transaction payload from Alchemy');
    }

    const txHash = activity.hash;
    const sender = activity.fromAddress;
    // Amount might be hex string in wei
    const amountHex = activity.value;
    const amount = BigInt(amountHex);
    const uniqueId = txHash; // Use tx hash as unique identifier for EVM

    // TODO: Add checks: Is recipient the game wallet? Is asset ETH? Is amount valid?
    const gameWalletAddress = process.env.NEXT_PUBLIC_ABSTRACT_GAME_WALLET_ADDRESS?.toLowerCase();
    const recipient = activity.toAddress?.toLowerCase();
    if (recipient !== gameWalletAddress) {
      throw new Error('Transaction not sent to the configured game wallet address.');
    }
    if (activity.asset !== 'ETH' /* or native currency symbol */) {
      throw new Error('Transaction is not for the native currency (ETH).');
    }

    if (!sender || amount === undefined || amount === null || !txHash) {
      throw new Error('Missing required fields in Alchemy transaction payload');
    }

    return { sender, amount, txHash, uniqueId };
  }

  async initiatePayout(recipientAddress: string, amountWei: bigint): Promise<{ txHash: Hash }> {
    // Payout is handled by the smart contract's cashOut function for Abstract.
    // Server does not initiate direct transfers.
    console.warn(
      '[Abstract Service] initiatePayout called, but payouts are handled on-chain via contract interaction.'
    );
    throw new Error(
      'Server-side payout initiation is not applicable for the Abstract network flow.'
    );
  }

  async getTxStatus(txHash: Hash): Promise<'pending' | 'confirmed' | 'failed' | 'unknown'> {
    try {
      const receipt = await this.publicClient.getTransactionReceipt({ hash: txHash });
      if (!receipt) {
        // Check if transaction is still pending
        const tx = await this.publicClient.getTransaction({ hash: txHash });
        return tx ? 'pending' : 'unknown';
      }
      return receipt.status === 'success' ? 'confirmed' : 'failed';
    } catch (error: any) {
      console.error(`[Abstract Service] Error fetching tx status for ${txHash}:`, error);
      // Handle specific errors like unknown tx hash
      if (error.message?.toLowerCase().includes('not found')) {
        return 'unknown';
      }
      return 'unknown'; // Default to unknown on error
    }
  }

  async verifySignature(params: {
    address: string;
    message: string;
    signature: string;
  }): Promise<boolean> {
    const { address, message, signature } = params;
    console.log(
      `[Abstract VerifySig] Attempting EIP-1271 verification via readContract for address: ${address}`
    );
    try {
      // Hash the message according to EIP-191 (matches frontend signMessage)
      const messageHash = hashMessage(message);
      console.log(
        `[Abstract VerifySig] Verifying EIP-1271. Address: ${address}, Hash: ${messageHash}, Sig: ${signature.substring(0, 10)}... (Using hashMessage)`
      );

      // Call the isValidSignature method on the smart contract wallet
      const result = await this.publicClient.readContract({
        address: address as Hex,
        abi: isValidSignatureAbi,
        functionName: 'isValidSignature',
        args: [messageHash, signature as Hex],
      });

      console.log(`[Abstract VerifySig] isValidSignature result: ${result}`);
      // Check if the result is the magic value
      return result === EIP1271_MAGIC_VALUE;
    } catch (error: any) {
      console.error(
        `[Abstract Service] Error verifying EIP-1271 signature for ${address}:`,
        error.message || error
      );
      return false;
    }
  }

  /**
   * Sends a transaction from the server wallet to mark a game as lost on the contract.
   * @param onChainGameId The on-chain identifier for the game.
   * @param selectedTiles The array of selected tile indices.
   * @param gameSeed The game seed (as a hex string) to be stored on-chain.
   * @returns The transaction hash.
   * @throws Error if the server wallet is not configured or if the transaction fails.
   */
  async markGameLostOnChain(
    onChainGameId: bigint, // Assuming onChainGameId is uint256 in contract -> bigint here
    selectedTiles: number[], // Assuming hash is passed as string
    gameSeed: string // Add gameSeed parameter (expecting hex string like 0x...)
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!this.serverWalletClient || !this.serverAccount) {
      return {
        txHash: null,
        error: new Error('Server wallet client is not initialized. Cannot send transaction.'),
      };
    }

    const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS as
      | Hex
      | undefined;
    if (!contractAddress) {
      return {
        txHash: null,
        error: new Error(
          '[Server Env] Abstract contract address not configured (NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS).'
        ),
      };
    }

    // Validate gameSeed format
    if (!gameSeed || !/^0x[0-9a-fA-F]{64}$/.test(gameSeed)) {
      return {
        txHash: null,
        error: new Error(
          `Invalid gameSeed format provided: ${gameSeed}. Expected 32-byte hex string (0x...).`
        ),
      };
    }

    const params = {
      address: contractAddress,
      abi: DeathRaceGameAbi.abi, // Use the imported ABI
      functionName: 'markGameAsLost',
      args: [onChainGameId, JSON.stringify({ selectedTiles }), gameSeed], // Pass gameSeed
      account: this.serverAccount, // Explicitly specify account
      chain: this.config.chain, // Explicitly specify chain
    };

    const { gasToUse, error: gasError } = await this.estimateGas(params);
    if (gasError) {
      return {
        txHash: null,
        error: new Error(`Failed to estimate gas: ${gasError.message || gasError}`),
      };
    }

    return this.executeServerTransaction({
      ...params,
      gas: gasToUse,
    });
  }

  /**
   * Sends a transaction from the server wallet to cash out a game on the contract.
   * @param onChainGameId The on-chain identifier for the game.
   * @param payoutAmountWei The payout amount in Wei (as a bigint).
   * @param selectedTiles The array of selected tile indices.
   * @param gameSeed The final game seed
   * @returns The transaction hash.
   * @throws Error if the server wallet is not configured or if the transaction fails.
   */
  async cashOutOnChain(
    onChainGameId: bigint,
    payoutAmountWei: bigint,
    selectedTiles: number[] | number[][],
    gameSeed: string
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!this.serverWalletClient || !this.serverAccount) {
      return {
        txHash: null,
        error: new Error('Server wallet client is not initialized. Cannot send transaction.'),
      };
    }

    const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS as
      | Hex
      | undefined;
    if (!contractAddress) {
      return {
        txHash: null,
        error: new Error(
          ' [Server Env] Abstract contract address not configured (NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS).'
        ),
      };
    }

    if (payoutAmountWei <= BigInt(0)) {
      return {
        txHash: null,
        error: new Error('Payout amount must be positive.'),
      };
    }

    const params = {
      address: contractAddress,
      abi: DeathRaceGameAbi.abi,
      functionName: 'cashOut',
      args: [onChainGameId, payoutAmountWei, JSON.stringify({ selectedTiles }), gameSeed],
      account: this.serverAccount,
      chain: this.config.chain,
    };

    const { gasToUse, error: gasError } = await this.estimateGas(params);
    if (gasError) {
      return {
        txHash: null,
        error: new Error(`Failed to estimate gas: ${gasError.message || gasError}`),
      };
    }

    return await this.executeServerTransaction({
      ...params,
      gas: gasToUse,
    });
  }

  /**
   * Sends a transaction from the server wallet to withdraw funds from the contract to the specified recipient.
   * @param recipientAddress The address to receive the withdrawn funds.
   * @param amountWei The amount to withdraw in Wei (as a bigint).
   * @returns The transaction hash.
   * @throws Error if the server wallet is not configured or if the transaction fails.
   */
  async withdrawFunds(
    recipientAddress: Hex,
    amountWei: bigint
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!this.serverWalletClient || !this.serverAccount) {
      return {
        txHash: null,
        error: new Error('Server wallet client is not initialized. Cannot send transaction.'),
      };
    }

    const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS as
      | Hex
      | undefined;
    if (!contractAddress) {
      return {
        txHash: null,
        error: new Error(
          '[Server Env] Abstract contract address not configured (NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS).'
        ),
      };
    }

    if (!/^0x[a-fA-F0-9]{40}$/.test(recipientAddress)) {
      return {
        txHash: null,
        error: new Error(`Invalid recipient address format: ${recipientAddress}`),
      };
    }

    if (amountWei <= BigInt(0)) {
      return {
        txHash: null,
        error: new Error('Withdraw amount must be positive.'),
      };
    }

    const params = {
      address: contractAddress,
      abi: DeathRaceGameAbi.abi,
      functionName: 'withdrawFunds',
      args: [amountWei, recipientAddress], // Updated args order
      account: this.serverAccount,
      chain: this.config.chain,
    };

    const { gasToUse, error: gasError } = await this.estimateGas(params);
    if (gasError) {
      return {
        txHash: null,
        error: new Error(`Failed to estimate gas: ${gasError.message || gasError}`),
      };
    }

    return await this.executeServerTransaction({
      ...params,
      gas: gasToUse,
    });
  }

  async getOnChainGameId(preliminaryGameId: string): Promise<bigint> {
    let error: any;
    let onchainGameId = BigInt(0);
    for (let i = 0; i < 3; i++) {
      error = undefined;
      try {
        const result = await this.publicClient.readContract({
          address: this.contractAddress as Hex,
          abi: DeathRaceGameAbi.abi,
          functionName: 'preliminaryToOnChainId',
          args: [preliminaryGameId],
        });
        onchainGameId = result as bigint;
        break;
      } catch (err: any) {
        console.log(`[Abstract Service] Attempt ${i + 1} failed. Retrying...: ${err.message}`);
        error = err;
      }
    }
    if (error) {
      throw new Error(`Failed to get onchain game id: ${error.message || error}`);
    }
    return onchainGameId;
  }

  async waitForTransactionReceipt(
    txHash: Hash,
    confirmations?: number
  ): Promise<{ receipt: TransactionReceipt | null; error: Error | null }> {
    try {
      const receipt = await this.publicClient.waitForTransactionReceipt({
        hash: txHash,
        pollingInterval: ABSTRACT_POLLING_INTERVAL,
        timeout: ABSTRACT_TX_TIMEOUT,
        confirmations: confirmations || 1,
      });
      return { receipt, error: null };
    } catch (error: any) {
      console.error(
        `[Abstract Service] Error waiting for transaction receipt: ${error.message || error}`
      );
      return {
        receipt: null,
        error: new Error(`Failed to wait for transaction receipt: ${error.message || error}`),
      };
    }
  }

  async executeServerTransaction(
    tx: Parameters<WalletClient['writeContract']>[0]
  ): Promise<{ txHash: Hash | null; error: Error | null }> {
    if (!this.serverWalletClient || !this.serverAccount) {
      return {
        txHash: null,
        error: new Error('Server wallet client is not initialized. Cannot send transaction.'),
      };
    }

    let err: Error | null = null;
    let txHash: Hash | null = null;
    for (let i = 0; i < 3; i++) {
      try {
        err = null;
        txHash = await this.serverWalletClient.writeContract(tx);
        break;
      } catch (error: any) {
        if (error instanceof Error && error.message.includes('nonce too low')) {
          console.log(`[Abstract Service] Nonce too low. Resetting nonceManager`);
          this.nonceManager.reset({
            address: this.serverAccount.address,
            chainId: this.config.chainId as number,
          });
          continue;
        }
        console.error(
          `[Abstract Service] Error sending transaction, retry ${i + 1} of 3: ${error.message || error}`
        );
        err = error;
        break;
      }
    }
    if (err) {
      return {
        txHash: null,
        error: err,
      };
    }
    return {
      txHash: txHash,
      error: null,
    };
  }

  async estimateGas(
    tx: Parameters<WalletClient['writeContract']>[0]
  ): Promise<{ gasToUse: bigint; error: Error | null }> {
    if (!this.serverWalletClient || !this.serverAccount) {
      return {
        gasToUse: BigInt(0),
        error: new Error('Server wallet client is not initialized. Cannot send transaction.'),
      };
    }

    try {
      const gas = await this.publicClient.estimateContractGas({
        account: this.serverAccount,
        address: tx.address,
        abi: tx.abi,
        functionName: tx.functionName,
        args: tx.args,
        value: tx.value,
      });
      return { gasToUse: gasWithBuffer(gas), error: null };
    } catch (error) {
      return { gasToUse: BigInt(0), error: error as Error };
    }
  }
}

export const gasWithBuffer = (gas: bigint) => {
  return (gas * ABSTRACT_GAS_BUFFER) / BigInt(100);
};
