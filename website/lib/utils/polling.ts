import { toast } from 'sonner';

interface PollingOptions {
  maxAttempts?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  onError?: (error: any) => void;
  onSuccess?: (data: any) => boolean;
  onTimeout?: () => void;
  interval?: (attempt: number) => number;
}

/**
 * Generic polling function for any API endpoint
 * @param url The API endpoint to poll
 * @param options Polling options
 * @returns Promise that resolves with the response data
 */
export async function pollEndpoint<T>(url: string, options: PollingOptions = {}): Promise<T> {
  const {
    maxAttempts = 10,
    initialDelay = 1000,
    maxDelay = 5000,
    backoffFactor = 1.5,
    onError,
    onSuccess,
    onTimeout,
    interval,
  } = options;

  let attempt = 0;
  let delay = initialDelay;

  while (attempt < maxAttempts) {
    try {
      // Wait before trying (except on first attempt if initialDelay is 0)
      if (attempt > 0 || initialDelay > 0) {
        // Use custom interval if provided, otherwise use exponential backoff
        const waitTime = interval ? interval(attempt) : delay;
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }

      // Make the request
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }

      const data = await response.json();

      // If onSuccess returns true, we're done
      // If it returns false, continue polling
      if (onSuccess) {
        const result = onSuccess(data);
        if (result === true) {
          return data;
        } else if (result === false) {
          // Continue polling - increase attempt count and delay
          attempt++;
          // Only update delay if no custom interval is provided
          if (!interval) {
            delay = Math.min(delay * backoffFactor, maxDelay);
          }
          continue;
        }
      }

      // If no onSuccess or it didn't return false, just return the data
      return data;
    } catch (error) {
      attempt++;

      if (onError) {
        onError(error);
      }

      // If we've hit max attempts, call onTimeout and throw
      if (attempt >= maxAttempts) {
        if (onTimeout) {
          onTimeout();
        }
        toast.error('Polling failed after maximum attempts. Please refresh.');
        throw error;
      }

      // Only update delay if no custom interval is provided
      if (!interval) {
        delay = Math.min(delay * backoffFactor, maxDelay);
      }
    }
  }

  toast.error('Polling failed after maximum attempts. Please refresh.');
  throw new Error('Polling failed after maximum attempts');
}
