import { fromZonedTime, toZonedTime } from 'date-fns-tz';
import { DAILY_LEADERBOARD_RESET_TIME_ET } from '@/lib/constants';

const ET_TIMEZONE = 'America/New_York';

// Core function that calculates the next reset time in ET timezone
export function getNextResetTimeET(now: Date = new Date()): Date {
  const [hour, minute] = DAILY_LEADERBOARD_RESET_TIME_ET.split(':').map(Number);

  // Convert current time to ET timezone
  const nowET = toZonedTime(now, ET_TIMEZONE);

  // Create reset time for today in ET
  const resetET = new Date(nowET);
  resetET.setHours(hour, minute, 0, 0);

  // If current time is past reset time, move to next day
  if (nowET >= resetET) {
    resetET.setDate(resetET.getDate() + 1);
  }

  return resetET;
}

// Returns a Date object in UTC representing the next leaderboard reset time in ET
export function getNextLeaderboardResetUTC(now: Date = new Date()): Date {
  const resetET = getNextResetTimeET(now);
  // Convert ET time to UTC, properly handling DST
  return fromZonedTime(resetET, ET_TIMEZONE);
}

// Check if the current time matches the configured reset time
export function isResetTime(now: Date = new Date()): boolean {
  const [hour, minute] = DAILY_LEADERBOARD_RESET_TIME_ET.split(':').map(Number);

  // Convert current time to ET timezone
  const nowET = toZonedTime(now, ET_TIMEZONE);

  return nowET.getHours() === hour && nowET.getMinutes() === minute;
}

export function getDeathPointsDistribution(
  totalPoints: number,
  winners: number,
  decay: number = 0.93
): number[] {
  if (winners <= 0) return [];
  if (decay === 1) {
    // Equal distribution when decay is 1
    const pointsPerWinner = Math.round(totalPoints / winners);
    return Array(winners).fill(pointsPerWinner);
  }
  if (decay <= 0 || decay > 1) {
    throw new Error('Decay factor must be between 0 (exclusive) and 1 (inclusive)');
  }
  const norm = (1 - Math.pow(decay, winners)) / (1 - decay);
  return Array.from({ length: winners }, (_, i) =>
    Math.round((totalPoints * Math.pow(decay, i)) / norm)
  );
}

export { ET_TIMEZONE };
