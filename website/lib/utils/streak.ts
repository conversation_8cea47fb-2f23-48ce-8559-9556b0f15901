import {
  MAX_STREAK_DAYS,
  BASE_MULTIPLIER,
  MULTIPLIER_INCREMENT_PER_DAY,
  MAX_MULTIPLIER,
} from '@/lib/constants';

/**
 * Calculates the points multiplier based on current streak days.
 * @param streakDays - Number of consecutive days played (0-30)
 * @returns Multiplier value (1.0 to 1.6)
 */
export function calculatePointsMultiplier(streakDays: number): number {
  const clampedStreak = Math.max(0, Math.min(streakDays, MAX_STREAK_DAYS));
  const multiplier = BASE_MULTIPLIER + clampedStreak * MULTIPLIER_INCREMENT_PER_DAY;
  return Math.min(multiplier, MAX_MULTIPLIER);
}

/**
 * Formats a multiplier for display (e.g., "1.24x")
 * @param multiplier - The multiplier value
 * @returns Formatted string with "x" suffix
 */
export function formatMultiplier(multiplier: number): string {
  return `${multiplier.toFixed(2)}x`;
}

/**
 * Calculates final points after applying the multiplier.
 * @param basePoints - Base points before multiplier
 * @param multiplier - Points multiplier
 * @returns Final points rounded to 2 decimal places
 */
export function applyPointsMultiplier(basePoints: number, multiplier: number): number {
  return Math.round(basePoints * multiplier * 100) / 100;
}
