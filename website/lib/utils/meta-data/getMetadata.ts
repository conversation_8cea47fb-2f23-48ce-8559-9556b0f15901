import { Metadata } from "next";
import { getBaseMetadata, metadataConfig } from "./metadataConfig";

export const getMetadata = (slug?: string): Metadata => {
    const key = slug || 'default';
    const config = metadataConfig[key] ?? metadataConfig.default;
  
    const base = getBaseMetadata(config.title as string || '', config.description || '');
  
    return {
      ...base,
      ...config,
      openGraph: {
        ...base.openGraph,
        ...config.openGraph,
      },
      twitter: {
        ...base.twitter,
        ...config.twitter,
      },
    };
  };
  