import { Metadata } from 'next';
import {
  DEFAULT_GAME_NAME,
  DEFAULT_GAME_DESCRIPTION,
  LASER_PARTY_GAME_NAME,
  LASER_PARTY_GAME_DESCRIPTION,
  X_USERNAME,
} from '@/lib/constants';

export const metadataConfig: Record<
  string,
  Partial<Metadata> & { segment: string }
> = {
  default: {
    segment: '',
    title: DEFAULT_GAME_NAME,
    description: DEFAULT_GAME_DESCRIPTION,
    icons: { icon: '/favicon.png' },
    openGraph: {
      images: [
        {
          url: '/og.png',
          width: 1200,
          height: 674,
          alt: DEFAULT_GAME_NAME,
        },
      ],
      videos: [
        {
          url: '/og.mp4',
          width: 1200,
          height: 674,
          type: 'video/mp4',
        },
      ],
    },
    twitter: {
      images: ['/og.png'],
    },
  },
  laser: {
    segment: 'laser',
    title: LASER_PARTY_GAME_NAME,
    description: LASER_PARTY_GAME_DESCRIPTION,
    icons: { icon: '/laser-party/favicon.png' },
    openGraph: {
      images: [
        {
          url: '/og.png',
          width: 1200,
          height: 674,
          alt: LASER_PARTY_GAME_NAME,
        },
      ],
      videos: [
        {
          url: '/laser-party/og.mp4',
          width: 1200,
          height: 674,
          type: 'video/mp4',
        },
      ],
    },
    twitter: {
      images: ['/og.png'],
    },
    alternates: {
      canonical: '/laser',
    },
  },
};

export const getBaseMetadata = (
  title: string,
  description: string
): Partial<Metadata> => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

  return {
    metadataBase: new URL(baseUrl),
    keywords: ['game', title?.toLowerCase(), 'crypto', 'solana'],
    authors: [{ name: title }],
    robots: {
      index: true,
      follow: true,
    },
    openGraph: {
      title,
      description,
      siteName: title,
      type: 'video.other',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      site: `@${X_USERNAME}`,
      creator: `@${X_USERNAME}`,
    },
  };
};

