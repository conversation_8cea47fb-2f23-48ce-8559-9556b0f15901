import { GameRow, DisplayRow, LPDisplayRow } from '@/components/death-race/types';
import { DEATH_RACE_HOUSE_EDGE, POINTS_ETH_RATIO, LP_HOUSE_EDGE } from '@/lib/constants';
import { NormalizedGame, GameStatus, Game as DatabaseGame, ClientGame } from '@/lib/types/game';
import { getDeathTileIndex } from '@/lib/utils/provably-fair';
import { formatEther } from 'viem';

// Chain-agnostic game logic utilities will reside here.

// Formatting utilities
export function formatMultiplier(multiplier: number): string {
  if (multiplier >= 100) {
    return `${Math.floor(multiplier)}x`;
  }
  if (multiplier >= 10) {
    return `${(Math.floor(multiplier * 10) / 10).toFixed(1)}x`;
  }
  return `${(Math.floor(multiplier * 100) / 100).toFixed(2)}x`;
}

// Game logic calculations

/**
 * Calculates the cumulative multiplier for each row, applying the house edge.
 * The multiplier for a row represents the total multiplier achieved *after* completing that row.
 */
export function calculateRowMultipliers(rows: GameRow[]): number[] {
  const multipliers: number[] = [];
  let currentMultiplier = 1;

  for (const row of rows) {
    // Calculate base multiplier for this row (1 / (1 - death_chance))
    const baseMultiplier = 1 / (1 - 1 / row.tiles);
    // Multiply with previous cumulative multiplier
    currentMultiplier *= baseMultiplier;
    // Apply house edge to the multiplier
    const multiplierWithEdge = currentMultiplier * (1 - DEATH_RACE_HOUSE_EDGE);
    // Push the multiplier with house edge to the array
    multipliers.push(multiplierWithEdge);
  }

  return multipliers;
}

/**
 * Calculates points earned based on bet amount and multiplier.
 * Points are calculated as: (ETH amount * multiplier * POINTS_ETH_RATIO) rounded to 2 decimal places.
 */
export function calculatePoints(betAmount: string, multiplier: number): number {
  const ethAmount = Number(formatEther(BigInt(betAmount))) * multiplier;
  return Math.floor(ethAmount * POINTS_ETH_RATIO * 100) / 100;
}

// --- Game Data Normalization ---

// Define a more specific type for potential raw input data
// Combines DB schema and potential camelCase variations from APIs
type RawGameDataSource = Partial<
  DatabaseGame & {
    // Potential camelCase versions/additions
    walletAddress?: string;
    betAmount?: number | string;
    currentRowIndex?: number;
    finalMultiplier?: number | string | null;
    createdAt?: string;
    updatedAt?: string;
    payoutTxSignature?: string | null;
    gameSeed?: string | null;
    commitmentHash?: string | null;
    payoutError?: string | null;
    selectedTiles?: number[];
    // Additional API-specific fields often in camelCase
    payoutAmount?: number | string | null; // API might send this after cashout
    onchainGameId?: string | null;
    isVerified?: boolean;
    refundNotified?: boolean;
    // Ensure `rows` allows for partial data before processing
    rows?: Partial<GameRow & DisplayRow>[];
  }
>;

// Updated Type guard
function isRawGameData(obj: any): obj is RawGameDataSource {
  return typeof obj === 'object' && obj !== null && 'id' in obj && 'status' in obj;
}
// --- Game Row Generation/Manipulation ---

/**
 * Generates a single game row with a random number of tiles.
 */
export function generateRandomRow(minTiles = 2, maxTiles = 7): GameRow {
  const tiles = Math.floor(Math.random() * (maxTiles - minTiles + 1)) + minTiles;
  return {
    tiles,
    deathTileIndex: null,
    chosenTileIndex: undefined,
  };
}

/**
 * Sets the death tile index for a given row based on the game seed and row index.
 * Uses the provably fair mechanism.
 */
export function setDeathTile(row: DisplayRow, seed: string, rowIndex: number): DisplayRow {
  return {
    ...row,
    deathTileIndex: getDeathTileIndex(seed, rowIndex, row.tiles),
  };
}

/**
 * Generates the full set of game rows based on a seed and a configuration string.
 * Parses the config string (or uses default), calculates multipliers, and sets death tiles.
 */
export function generateRowsFromSeedAndConfig(
  gameSeed: string,
  rowConfig: { tiles: number; dimension?: 'col' | 'row' }[]
): DisplayRow[] {
  const initialRows: GameRow[] = rowConfig.map(({ tiles, dimension }) => ({
    tiles,
    dimension,
    deathTileIndex: null, // Will be set below
    chosenTileIndex: undefined,
  }));

  // Calculate multipliers using the function defined in this file
  const multipliers = calculateRowMultipliers(initialRows);

  // Determine death tiles and finalize rows using the function defined in this file
  const finalRows = initialRows.map((row, index) => {
    const deathIndex = getDeathTileIndex(gameSeed, index, row.tiles);
    return {
      ...row,
      deathTileIndex: deathIndex,
      multiplier: multipliers[index], // Add multiplier for completeness
    };
  });

  return finalRows;
}

// --- Client-Side Game Preparation ---

/**
 * Prepares the normalized game data for display on the client.
 * Key transformations:
 * - Hides deathTileIndex for rows at or after currentRowIndex if the game is active.
 * - Ensures rows are of type DisplayRow.
 */
export function prepareGameForClient(normalizedGame: NormalizedGame | null): ClientGame | null {
  if (!normalizedGame) {
    return null;
  }

  const isGameActive = normalizedGame.status === 'active';

  // Process rows: Hide future death tiles if game is active
  const clientRows = normalizedGame.rows.map((row, index) => {
    // If the game is active AND this row index is the current one or a future one
    if (isGameActive && index >= normalizedGame.currentRowIndex) {
      return {
        ...row,
        deathTileIndex: null, // Hide the death tile
      };
    }
    // Otherwise, return the row as is (death tile revealed if game over or row passed)
    return row;
  });

  // Assemble the ClientGame object
  const clientGame: ClientGame = {
    ...normalizedGame,
    rows: clientRows, // Use the processed rows
    hasUpcomingGames: normalizedGame.status === 'active',
  };

  return clientGame;
}

// ------------------------------------------------------------ LASER PARTY UTILITIES ------------------------------------------------------------

/**
 * Generates row configuration for Laser Party based on grid size.
 * Creates alternating row and column eliminations, following the same pattern as the default config.
 */
export function generateLaserPartyRowConfig(gridSize: number): LPDisplayRow[] {
  const config: LPDisplayRow[] = [];

  // Generate pairs of eliminations: col then row, starting from gridSize down to 1
  for (let size = gridSize; size >= 1; size--) {
    // Add column elimination
    config.push({
      tiles: size,
      dimension: 'col',
      deathTileIndex: null,
      multiplier: 1, // Will be calculated later
    });

    // Add row elimination
    config.push({
      tiles: size,
      dimension: 'row',
      deathTileIndex: null,
      multiplier: 1, // Will be calculated later
    });
  }

  return config;
}

/**
 * Calculates the maximum possible multiplier for a given grid size in Laser Party.
 * This is the multiplier achieved if the player survives all rounds.
 * Uses the same logic as ProgressBar.calculateActualMultiplier but for the full config.
 */
export function calculateMaxMultiplierForGridSize(gridSize: number): number {
  const rowConfig = generateLaserPartyRowConfig(gridSize);

  // Calculate the maximum possible turn (before hitting tiles = 1)
  // Find the last row that has tiles > 1
  let maxSurvivableTurn = 0;
  for (let i = 0; i < rowConfig.length; i++) {
    if (rowConfig[i].tiles > 1) {
      maxSurvivableTurn = i + 1;
    }
  }

  // Calculate multiplier for surviving up to the last possible turn
  // This matches the ProgressBar logic exactly
  let multiplier = 1;
  for (let i = 0; i < Math.min(maxSurvivableTurn, rowConfig.length); i++) {
    const row = rowConfig[i];
    const baseMultiplier = 1 / (1 - 1 / row.tiles);
    multiplier *= baseMultiplier;
  }

  // Apply house edge (0.95) - same as ProgressBar
  return multiplier * 0.95;
}

// Other utilities will be added below...

export const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
