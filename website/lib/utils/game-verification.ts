import { createHash } from 'crypto';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a random seed for game verification
 */
export function generateGameSeed(): string {
  return uuidv4();
}

/**
 * Generate a commitment hash from game seed and rows
 * This allows for provable fairness
 */
export function generateCommitmentHash(seed: string, rows: any): string {
  const dataToHash = JSON.stringify({
    seed,
    rows: rows.map((row: any) => ({
      tiles: row.tiles,
      multiplier: row.multiplier,
      deathTileIndex: row.deathTileIndex,
    })),
  });

  return createHash('sha256').update(dataToHash).digest('hex');
}
