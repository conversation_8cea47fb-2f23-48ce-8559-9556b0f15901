import mixpanel from 'mixpanel-browser';

class AnalyticsClient {
  private isInitialized = false;

  private ensureInit() {
    if (!this.isInitialized && process.env.NEXT_PUBLIC_MIXPANEL_TOKEN) {
      mixpanel.init(process.env.NEXT_PUBLIC_MIXPANEL_TOKEN, {
        debug: process.env.NODE_ENV === 'development',
        ignore_dnt: true,
        api_host: '/analytics',
      });
      this.isInitialized = true;
    }
  }

  identifyUser(userId?: string) {
    this.ensureInit();
    if (!userId) return;
    mixpanel.identify(userId);
  }

  private trackEvent(event: string, properties?: Record<string, any>) {
    this.ensureInit();
    mixpanel.track(event, properties);
  }

  // Client-side events only
  walletConnected(props?: Record<string, any>) {
    this.trackEvent('Wallet Connected', props);
  }
  demoPlayed(props?: Record<string, any>) {
    this.trackEvent('Demo Played', props);
  }
  copiedReferralLink(props?: Record<string, any>) {
    this.trackEvent('Referral Link Copied', props);
  }
  faqViewed(props?: Record<string, any>) {
    this.trackEvent('FAQ Viewed', props);
  }
  errorOccurred(props?: Record<string, any>) {
    this.trackEvent('Error', props);
  }
  signInClicked(props?: Record<string, any>) {
    this.trackEvent('Sign In Clicked', props);
  }
  addFundsClicked(props?: Record<string, any>) {
    this.trackEvent('Add Funds Clicked', props);
  }
  fundsBridged(props?: Record<string, any>) {
    this.trackEvent('Funds Bridged', props);
  }

  // --- Laser Party ---
  gridSizeChanged(props?: Record<string, any>) {
    this.trackEvent('Grid Size Changed', props);
  }
}

export const analytics = new AnalyticsClient();

export const identifyUser = (userId?: string) => analytics.identifyUser(userId);
