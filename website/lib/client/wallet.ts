import { CrossAppAccountWithMetadata } from '@privy-io/react-auth';
import { formatEther } from 'viem';

export const getCrossAppWalletAddress = (linkedAccounts: CrossAppAccountWithMetadata[]) => {
  if (!linkedAccounts || linkedAccounts.length === 0) {
    return undefined;
  }
  const linkedAccount = linkedAccounts[0];
  if (!linkedAccount.smartWallets || linkedAccount.smartWallets.length === 0) {
    return undefined;
  }
  return linkedAccount.smartWallets[0].address.toLowerCase() as `0x${string}`;
};

export const formatCurrency = (balance: bigint, showSymbol: boolean = true) => {
  const num = Number(formatEther(balance));
  // Format with up to 5 decimals, trim trailing zeros, and use toLocaleString for commas
  const fixed = num.toFixed(4);
  const trimmed = fixed.replace(/\.?(0+)$/, '');
  const [intPart, decPart] = trimmed.split('.');
  const intWithCommas = Number(intPart).toLocaleString();
  const formattedBalance = decPart ? `${intWithCommas}.${decPart}` : intWithCommas;
  return showSymbol ? `${formattedBalance} ETH` : formattedBalance;
};

export const formatWithDecimals = (num: bigint, decimals: number = 4) => {
  const numFloat = Number(formatEther(num));
  return numFloat.toFixed(decimals);
};
