import { usePrivy, getAccessToken } from '@privy-io/react-auth';
import { queryOptions, useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { ActiveGame, CurrentGame, Game, GameWithOptionalUsername } from '../types/game';
import { UserNotFoundError } from './errors';
import { CamelCasedProperties, CamelCasedPropertiesDeep } from 'type-fest';
import { AdminStats, LeaderboardEntry } from '../types/api';
import { DEFAULT_ROW_CONFIG, GAME_TYPES, GameType } from '../constants';
import { SessionConfig } from '@abstract-foundation/agw-client/sessions';
import superjson from 'superjson';
import { isAdmin } from '@/lib/utils';

// --------------------------------------------- USERS
interface UserData {
  id: string;
  wallet_address: string;
  referral_code: string;
  created_at: string;
  updated_at: string;
  username?: string;
  role: 'user' | 'admin' | 'super-admin';
  session_config: SessionConfig;
  testnet_claim_status: boolean;
  points: number;
  streak: number;
  multiplier: number;
  last_game_date?: string;
  custom_referral_percentage?: number | null;
}
export type UserDataResponse = CamelCasedPropertiesDeep<UserData>;

export const getUserOptions = (enabled: boolean = true) =>
  queryOptions({
    queryKey: ['/users'],
    queryFn: async ({ signal }) => {
      await getAccessToken();
      const response = await fetch('/api/users', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (response.status === 404) {
        throw new UserNotFoundError('User not found');
      }
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const rawBody = await response.text();
      const body = superjson.parse<UserDataResponse>(rawBody);
      if (body.sessionConfig) {
        body.sessionConfig = superjson.parse<SessionConfig>(
          body.sessionConfig as unknown as string
        );
      }
      return body;
    },
    staleTime: Infinity,
    enabled,
    retry(_, error) {
      if (error instanceof UserNotFoundError) {
        return false;
      }
      return true;
    },
  });

export const useGetUser = () => {
  const { authenticated } = usePrivy();
  return useQuery(getUserOptions(authenticated));
};

// --------------------------------------------- GAME HISTORY
interface GameHistoryResponse {
  games: CurrentGame[];
  totalGames: number;
  totalPages: number;
  currentPage: number;
  stats: {
    totalGames: number;
    totalBetAmount: string;
    totalProfitLoss: string;
  };
  viewedUsername?: string;
  viewedWalletAddress?: string;
}

export const getGameHistoryOptions = (
  isAllowed: boolean,
  page: number,
  walletAddress?: string,
  username?: string,
  gameId?: string,
  gameType?: (typeof GAME_TYPES)[number]
) =>
  queryOptions({
    queryKey: (() => {
      const queryKey = ['/api/games/history', gameType];
      if (gameType) {
        queryKey.push(gameType);
      }
      if (walletAddress) {
        queryKey.push(walletAddress);
      }
      if (page) {
        queryKey.push(page.toString());
      }
      if (username) {
        queryKey.push(username);
      }
      if (gameId) {
        queryKey.push(`gameId:${gameId}`);
      }

      return queryKey;
    })(),
    queryFn: async ({ signal }) => {
      await getAccessToken();
      const params = new URLSearchParams();
      if (gameType) {
        params.set('gameType', gameType);
      }
      if (walletAddress) {
        params.set('walletAddress', walletAddress);
      }
      if (page) {
        params.set('page', page.toString());
      }
      if (username) {
        params.set('username', username);
      }
      if (gameId) {
        params.set('gameId', gameId);
      }
      const response = await fetch(`/api/games/history?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
      });
      if (!response.ok) {
        throw new Error('Failed to fetch game history');
      }
      const rawBody = await response.text();
      const body = superjson.parse<GameHistoryResponse>(rawBody);
      return body;
    },
    enabled: isAllowed,
  });

export const useGetGameHistory = (
  isAllowed: boolean,
  page: number,
  walletAddress?: string,
  username?: string,
  gameId?: string,
  gameType?: (typeof GAME_TYPES)[number]
) => {
  return useQuery(
    getGameHistoryOptions(isAllowed, page, walletAddress, username, gameId, gameType)
  );
};

// --------------------------------------------- REFERRAL BONUSES
interface ReferralBonusesResponse {
  total: string;
  version: number;
}
export const getUserReferralBonusesOptions = (enabled: boolean = true) =>
  queryOptions({
    queryKey: ['/users/referral-bonuses'],
    queryFn: async ({ signal }) => {
      await getAccessToken();
      const response = await fetch('/api/users/referral-bonuses', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch referral bonuses');
      }
      return (await response.json()) as ReferralBonusesResponse;
    },
    staleTime: Infinity,
    enabled,
  });

export const useGetUserReferralBonuses = () => {
  const { authenticated } = usePrivy();
  return useQuery(getUserReferralBonusesOptions(authenticated));
};

// --------------------------------------------- GET ACTIVE GAME
export const getActiveGameOptions = <T = ActiveGame>(
  gameType: (typeof GAME_TYPES)[number],
  enabled: boolean = true
) =>
  queryOptions({
    queryKey: ['/api/games/active', gameType],
    queryFn: async ({ signal }) => {
      const params = new URLSearchParams();
      params.set('gameType', gameType);
      const response = await fetch(`/api/games/active?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch active game');
      }
      return (await response.json()) as T;
    },
    staleTime: Infinity,
    refetchInterval: (query) => {
      const data = query.state.data as ActiveGame | undefined;
      const statuses = ['cashout_pending', 'pending_onchain'];
      const shouldRefetch = statuses.includes(data?.currentGame?.status ?? '');
      return shouldRefetch && query.state.dataUpdateCount < 5 ? 2000 : false;
    },
    enabled,
  });

export const useGetActiveGame = <T = ActiveGame>(gameType: (typeof GAME_TYPES)[number]) => {
  const { authenticated } = usePrivy();
  return useQuery(getActiveGameOptions<T>(gameType, authenticated));
};

export const getRowConfigOptions = (enabled: boolean = true) =>
  queryOptions({
    queryKey: ['currentRowConfig'],
    initialData: {
      rows: DEFAULT_ROW_CONFIG,
      fresh: false,
    },
    staleTime: Infinity,
    gcTime: Infinity,
    enabled,
  });

export const useGetRowConfig = () => {
  const { authenticated } = usePrivy();
  return useQuery(getRowConfigOptions(authenticated));
};

// --------------------------------------------- LEADERBOARD ENTRIES
export const getLeaderboardEntriesOptions = (
  gameType: (typeof GAME_TYPES)[number],
  timeFilter?: 'daily' | '7d' | '30d' | 'all_time' | 'points',
  walletAddress?: string
) =>
  queryOptions({
    queryKey: ['/api/leaderboard', gameType, timeFilter, walletAddress].filter(Boolean),
    queryFn: async ({ signal }) => {
      let apiUrl = `/api/leaderboard?timeFilter=${timeFilter}`;
      if (walletAddress) {
        apiUrl += `&currentWallet=${walletAddress}`;
      }
      if (gameType && timeFilter !== 'points') {
        apiUrl += `&gameType=${gameType}`;
      }
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
      });
      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard entries');
      }
      return (await response.json()) as CamelCasedProperties<LeaderboardEntry[]>;
    },
    staleTime: Infinity,
  });

export const useGetLeaderboardEntries = (
  gameType: (typeof GAME_TYPES)[number],
  timeFilter?: 'daily' | '7d' | '30d' | 'all_time' | 'points'
) => {
  const { data: userData } = useGetUser();
  const { walletAddress } = userData ?? {};
  return useQuery(getLeaderboardEntriesOptions(gameType, timeFilter, walletAddress));
};

// --------------------------------------------- ADMIN STATS
export const getAdminStatsOptions = (
  enabled: boolean = true,
  gameType?: (typeof GAME_TYPES)[number],
  startDate?: string,
  endDate?: string
) =>
  queryOptions({
    queryKey: ['/api/admin/stats', gameType, startDate, endDate],
    queryFn: async ({ signal }) => {
      const params = new URLSearchParams();
      if (gameType) {
        params.set('gameType', gameType);
      }
      if (startDate) {
        params.set('startDate', startDate);
      }
      if (endDate) {
        params.set('endDate', endDate);
      }
      const response = await fetch(`/api/admin/stats?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch admin stats');
      }
      return (await response.json()) as AdminStats;
    },
    enabled,
  });

export const useGetAdminStats = (
  role: string,
  gameType?: (typeof GAME_TYPES)[number],
  startDate?: string,
  endDate?: string
) => {
  const { authenticated } = usePrivy();
  return useQuery(
    getAdminStatsOptions(authenticated && isAdmin(role), gameType, startDate, endDate)
  );
};

interface AdminAllGamesResponse {
  games: GameWithOptionalUsername[];
  totalGames: number;
  totalPages: number;
  currentPage: number;
}

export const getAllGamesAdminOptions = (
  page: number,
  pageSize: number = 15,
  enabled: boolean = true,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  gameType?: (typeof GAME_TYPES)[number] | undefined
) =>
  queryOptions({
    queryKey: ['/api/admin/games', page, pageSize, sortBy, sortDirection, gameType],
    queryFn: async ({ signal }) => {
      const params = new URLSearchParams();
      params.set('page', page.toString());
      params.set('pageSize', pageSize.toString());
      params.set('sortBy', sortBy);
      params.set('sortDirection', sortDirection);
      if (gameType) {
        params.set('gameType', gameType);
      }
      const response = await fetch(`/api/admin/games?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch all games (admin)');
      }
      const rawBody = await response.text();
      const body = superjson.parse<AdminAllGamesResponse>(rawBody);
      return body;
    },
    enabled,
  });

export const useGetAllGamesAdmin = (
  page: number,
  pageSize: number = 15,
  enabled: boolean = true,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  gameType?: (typeof GAME_TYPES)[number] | undefined
) => {
  return useQuery(
    getAllGamesAdminOptions(page, pageSize, enabled, sortBy, sortDirection, gameType)
  );
};

// --------------------------------------------- ETH PRICE
export const getEthPriceOptions = () =>
  queryOptions({
    queryKey: ['ethPrice'],
    queryFn: async ({ signal }) => {
      const response = await fetch(
        'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
        { signal }
      );
      if (!response.ok) {
        throw new Error('Failed to fetch ETH price');
      }
      const data = await response.json();
      return data.ethereum.usd as number;
    },
    refetchInterval: 300000, // Refetch every 5 minutes
  });

export const useEthPrice = () => {
  return useQuery(getEthPriceOptions());
};

// --------------------------------------------- CHALLENGES
type Challenge = CamelCasedPropertiesDeep<{
  id: number;
  title: string;
  description: string | null;
  points: number;
  published: boolean;
  link: string;
  type: 'standard' | 'permanent' | 'abstractUpvote';
  status: 'incomplete' | 'pending' | 'verifying' | 'completed';
  icon: string | null;
  gameType: string | null; // null means shows on all games, otherwise specific to game type
}>;
export const getChallengesOptions = (enabled: boolean = true, mode: 'user' | 'admin' = 'user') =>
  queryOptions({
    queryKey: ['/api/challenges', mode],
    queryFn: async ({ signal }) => {
      const params = new URLSearchParams();
      params.set('mode', mode);
      const response = await fetch(`/api/challenges?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch challenges');
      }
      return (await response.json()) as CamelCasedProperties<Challenge[]>;
    },
    enabled,
  });

export const useGetChallenges = () => {
  const { authenticated } = usePrivy();
  return useQuery(getChallengesOptions(authenticated));
};

export const useGetAdminChallenges = () => {
  const { authenticated } = usePrivy();
  return useQuery(getChallengesOptions(authenticated, 'admin'));
};

// --------------------------------------------- GAME BALANCE
export const getGameBalanceOptions = (gameType: GameType) =>
  queryOptions({
    queryKey: ['gameBalance', gameType],
    queryFn: async ({ signal }) => {
      const response = await fetch(`/api/game-balance/${gameType}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal,
      });
      if (!response.ok) {
        throw new Error('Failed to fetch game balance');
      }
      const data = await response.json();
      return {
        effectiveBalance: BigInt(data.effectiveBalance || 0),
        hasMaxBalance: data.hasMaxBalance,
        contractBalance: BigInt(data.contractBalance || 0),
      };
    },
  });

export const useGameBalance = (gameType: GameType) => {
  return useQuery(getGameBalanceOptions(gameType));
};
