[{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "contract IAppRegistry", "name": "_appRegistry", "type": "address"}, {"internalType": "contract IVoteGovernor", "name": "_voteGovernor", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AlreadyInitialized", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "appId", "type": "uint256"}], "name": "AlreadyVotedFor", "type": "error"}, {"inputs": [], "name": "AppNotActive", "type": "error"}, {"inputs": [], "name": "IndexOutOfBounds", "type": "error"}, {"inputs": [], "name": "InvalidSchedule", "type": "error"}, {"inputs": [], "name": "InvalidValue", "type": "error"}, {"inputs": [], "name": "NewOwnerIsZeroAddress", "type": "error"}, {"inputs": [], "name": "NoHandoverRequest", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"inputs": [], "name": "UsedAllVotes", "type": "error"}, {"inputs": [], "name": "VotingNotActive", "type": "error"}, {"inputs": [], "name": "WithdrawFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newAppRegistry", "type": "address"}], "name": "AppRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "OwnershipHandoverCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "OwnershipHandoverRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "RolesUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "startTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "epochDuration", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "epochsCompleted", "type": "uint256"}], "name": "ScheduleInitialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "VoteGovernorUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "appId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "epoch", "type": "uint256"}], "name": "Voted", "type": "event"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "appRegistry", "outputs": [{"internalType": "contract IAppRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cancelOwnershipHandover", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "completeOwnershipHandover", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "currentEpoch", "outputs": [{"internalType": "uint256", "name": "epoch", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentSchedule", "outputs": [{"internalType": "uint40", "name": "startTime", "type": "uint40"}, {"internalType": "uint40", "name": "epochDuration", "type": "uint40"}, {"internalType": "uint40", "name": "epochsCompleted", "type": "uint40"}, {"internalType": "uint96", "name": "voteCost", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}], "name": "getUserVotes", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "appId", "type": "uint256"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}], "name": "getVotesForApp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "grantRoles", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "hasAllRoles", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "hasAnyRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint40", "name": "_startTime", "type": "uint40"}, {"internalType": "uint40", "name": "_epochDuration", "type": "uint40"}, {"internalType": "uint96", "name": "_voteCost", "type": "uint96"}], "name": "initializeSchedule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "nextSchedule", "outputs": [{"internalType": "uint40", "name": "startTime", "type": "uint40"}, {"internalType": "uint40", "name": "epochDuration", "type": "uint40"}, {"internalType": "uint40", "name": "epochsCompleted", "type": "uint40"}, {"internalType": "uint96", "name": "voteCost", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "renounceRoles", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "requestOwnershipHandover", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "roles", "type": "uint256"}], "name": "revokeRoles", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON>Of", "outputs": [{"internalType": "uint256", "name": "roles", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAppRegistry", "type": "address"}], "name": "setAppRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newGovernor", "type": "address"}], "name": "setVoteGovernor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "userVoteSpend", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "userVotesRemaining", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "voteCost", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "appId", "type": "uint256"}], "name": "voteForApp", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "voteGovernor", "outputs": [{"internalType": "contract IVoteGovernor", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]