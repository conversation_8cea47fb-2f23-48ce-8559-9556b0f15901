/**
 * Defines core properties for a blockchain's native currency.
 */
export interface ICurrencyInfo {
  symbol: string; // e.g., 'SOL', 'ETH'
  decimals: number; // e.g., 9 for SOL, 18 for ETH
  name: string; // e.g., 'Solana', 'Ethereum'
}

/**
 * Holds configuration specific to a blockchain network.
 */
export interface IBlockchainConfig {
  rpcUrl: string; // RPC endpoint URL
  chainId: string | number; // Network chain ID (can be string or number depending on chain)
  explorerUrl: string; // Base URL for the block explorer
  currency: ICurrencyInfo; // Details of the native currency
  // Add any other chain-specific config needed (e.g., specific contract addresses)
}

/**
 * Interface for wallet interactions.
 */
export interface IWalletProvider {
  // Properties
  isConnected: boolean;
  address: string | null; // The connected wallet address

  // Methods
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  signMessage(message: string): Promise<string>; // Returns the signature
  // Add other methods like signTransaction if needed directly
}

/**
 * Interface for handling account-related actions like fetching balances.
 */
export interface IAccountService {
  getBalance(address: string): Promise<bigint>; // Returns balance in smallest unit (lamports/wei)
  formatCurrency(
    amount: bigint,
    options?: { showSymbol?: boolean; significantDigits?: number }
  ): string; // Formats the smallest unit into a readable string (e.g., '1.23 SOL')
}

/**
 * Interface for managing transactions (sending, processing, verifying).
 */
export interface ITransactionService {
  // Method to prepare parameters for a bet transaction (called server-side usually)
  // Returns parameters needed by the frontend to send the actual tx
  prepareBetTransactionParams(
    senderAddress: string,
    betAmountLamportsOrWei: bigint
  ): Promise<{ to: string; value: bigint; data?: string; [key: string]: any }>; // Flexible return

  // Method to process an incoming transaction detected by a webhook
  // Returns relevant data like sender, amount, txHash, etc.
  processIncomingTransaction(
    payload: any
  ): Promise<{ sender: string; amount: bigint; txHash: string; uniqueId: string }>;

  // Method to initiate a payout from the server-side hot wallet
  initiatePayout(
    recipientAddress: string,
    amountLamportsOrWei: bigint
  ): Promise<{ txHash: string }>;

  // Method to get the status of a transaction
  // Returns a status like 'pending', 'confirmed', 'failed'
  getTxStatus(txHash: string): Promise<'pending' | 'confirmed' | 'failed' | 'unknown'>;

  // Method to verify a signed message
  verifySignature(params: {
    address: string;
    message: string;
    signature: string;
  }): Promise<boolean>;
}

// Combine all services into a single provider interface for easier injection
export interface IBlockchainProvider extends IAccountService, ITransactionService {
  config: IBlockchainConfig;
  wallet: IWalletProvider; // Access wallet provider through here
}
