import { withPlausibleProxy } from 'next-plausible';

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Enable React Compiler
    reactCompiler: true,
    // Enable optimistic updates in Server Actions
    optimisticClientCache: true,
    // // Enable the new cache system
    // typedRoutes: true,
  },
  // Recommended: Enable strict mode
  reactStrictMode: true,

  eslint: {
    // Don't fail builds due to ESLint errors (pre-commit hooks handle this)
    ignoreDuringBuilds: true,
  },

  // Redirects configuration
  async redirects() {
    const isProd = process.env.NODE_ENV === 'production';

    return [
      {
        source: '/account',
        destination: '/stats',
        permanent: true,
      },
      ...(isProd
        ? [
          {
            source: '/style-guide',
            destination: '/not-found',
            permanent: false,
          },
        ]
        : []),
    ];
  },

  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              base-uri 'self';
              form-action 'self';
              frame-ancestors 'none';
              child-src https://auth.privy.io https://verify.walletconnect.com https://verify.walletconnect.org;
              frame-src https://auth.privy.io https://verify.walletconnect.com https://verify.walletconnect.org https://challenges.cloudflare.com;
              worker-src 'self';
              manifest-src 'self'
            `
              .replace(/\s{2,}/g, ' ')
              .trim(),
          },
        ],
      },
    ];
  },

  async rewrites() {
    return [
      {
        source: '/analytics/:match*',
        destination: 'https://api.mixpanel.com/:match*',
      },
      {
        source: '/ld-events/:path*',
        destination: 'https://events.launchdarkly.com/:path*',
      },
    ];
  },
};

export default withPlausibleProxy()(nextConfig);
