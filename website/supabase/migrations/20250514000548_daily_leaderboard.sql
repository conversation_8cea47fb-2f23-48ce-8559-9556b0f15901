-- Add a column to track profit/loss for the current daily leaderboard (resets every day at 12:00 PM ET)
ALTER TABLE users
ADD COLUMN IF NOT EXISTS profit_loss_daily NUMERIC DEFAULT 0;

-- Helper: Returns true if a timestamp is in the current Death Race Day (12:00 PM ET to next 12:00 PM ET, America/New_York)
-- Usage: is_in_current_day(updated_at)
CREATE OR REPLACE FUNCTION is_in_current_day(ts timestamptz)
RETURNS boolean AS $$
DECLARE
  now_et timestamptz := TIMEZONE('America/New_York', NOW());
  day_start timestamptz;
  next_day_start timestamptz;
BEGIN
  day_start := DATE_TRUNC('day', now_et) + INTERVAL '12 hours';
  IF now_et < day_start THEN
    day_start := day_start - INTERVAL '1 day';
  END IF;
  next_day_start := day_start + INTERVAL '1 day';
  RETURN TIMEZONE('America/New_York', ts) >= day_start AND TIMEZONE('America/New_York', ts) < next_day_start;
END;
$$ LANGUAGE plpgsql STABLE;

-- Drop the trigger before replacing the function
DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
DROP FUNCTION IF EXISTS update_user_stats();

-- Re-create the update_user_stats function to handle profit_loss_daily
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    -- Remove stats for old status
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily - CASE
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = OLD.wallet_address;
    END IF;

    -- Add stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily + CASE
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = NEW.wallet_address;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger after the function
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats(); 