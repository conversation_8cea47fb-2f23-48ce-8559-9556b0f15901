CREATE OR REPLACE FUNCTION get_admin_stats()
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
    total_games_count INTEGER;
    lost_games_count INTEGER;
    bust_percentage DECIMAL;
    total_volume NUMERIC;
    average_win_multiplier DECIMAL;
    average_row_reached DECIMAL;
    treasury_delta NUMERIC;
    total_users_count INTEGER;
    total_payouts NUMERIC;
BEGIN
    -- Get total games played
    SELECT COUNT(*) INTO total_games_count
    FROM games;
    
    -- Get count of lost games
    SELECT COUNT(*) INTO lost_games_count
    FROM games
    WHERE status = 'lost';
    
    -- Calculate bust percentage
    bust_percentage := CASE WHEN total_games_count > 0 
                         THEN (lost_games_count::DECIMAL / total_games_count) * 100 
                         ELSE 0 
                      END;
    
    -- Get total volume (sum of all bets)
    SELECT COALESCE(SUM(bet_amount::NUMERIC), 0) INTO total_volume
    FROM games
    WHERE status IN ('won', 'lost');
    
    -- Get average win multiplier
    SELECT COALESCE(AVG(final_multiplier), 0) INTO average_win_multiplier
    FROM games
    WHERE status = 'won'
    AND final_multiplier IS NOT NULL
    AND final_multiplier > 0;
    
    -- Get average row reached
    SELECT COALESCE(AVG(current_row_index), 0) INTO average_row_reached
    FROM games
    WHERE status IN ('won', 'lost');
    
    -- Get treasury delta
    SELECT COALESCE(SUM(net), 0) INTO treasury_delta
    FROM games
    WHERE net IS NOT NULL;
    
    -- Get total users
    SELECT COUNT(*) INTO total_users_count
    FROM users;
    
    -- Calculate total payouts to players
    SELECT COALESCE(SUM(bet_amount::NUMERIC * final_multiplier), 0) INTO total_payouts
    FROM games
    WHERE status = 'won'
    AND final_multiplier IS NOT NULL;
    
    -- Return all stats as JSON
    RETURN json_build_object(
        'totalGames', total_games_count,
        'totalVolume', total_volume::TEXT,
        'totalPayouts', total_payouts::TEXT,
        'totalUsers', total_users_count,
        'treasuryDelta', treasury_delta::TEXT,
        'bustPercentage', ROUND(bust_percentage::NUMERIC, 1),
        'averageWinMultiplier', ROUND(average_win_multiplier::NUMERIC, 2),
        'averageRowReached', ROUND(average_row_reached::NUMERIC, 1)
    );
END;
$$;