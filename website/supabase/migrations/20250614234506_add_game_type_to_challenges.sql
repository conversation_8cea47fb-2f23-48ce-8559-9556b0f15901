-- Add game_type column to challenges table
-- NULL means the challenge shows on all game pages
-- If set to a specific game type (death_race, laser_party), it only shows on that game's pages
ALTER TABLE challenges 
ADD COLUMN game_type TEXT;

-- Add comment to document the column
COMMENT ON COLUMN challenges.game_type IS 'Game type this challenge is specific to. NULL means it shows on all games.';

-- Create index for efficient filtering
CREATE INDEX challenges_game_type_idx ON challenges (game_type);
