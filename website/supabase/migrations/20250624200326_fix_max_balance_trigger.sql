--- <PERSON>reate trigger to automatically update the max_balance column
CREATE OR REPLACE FUNCTION update_game_max_balances_max_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle game becoming active (reserve bet_amount)
  IF NEW.status = 'active' AND OLD.status != 'active' THEN
    UPDATE game_max_balances
    SET 
      max_balance = (max_balance::NUMERIC + NEW.bet_amount::NUMERIC)::TEXT
    WHERE 
      game_type = NEW.game_type
      AND active = TRUE;
  END IF;

  -- Handle status changes from/to final states
  IF (NEW.status IN ('won', 'lost', 'refunded') AND OLD.status NOT IN ('won', 'lost', 'refunded')) OR
     (OLD.status IN ('won', 'lost', 'refunded') AND NEW.status NOT IN ('won', 'lost', 'refunded')) OR
     (NEW.status IN ('won', 'lost', 'refunded') AND OLD.status IN ('won', 'lost', 'refunded') AND NEW.status != OLD.status) THEN

    -- First, reverse the OLD status effect (corrections)
    IF OLD.status = 'won' THEN
      -- Reverse: treasury had paid out, now gain it back
      UPDATE game_max_balances
      SET max_balance = (max_balance::NUMERIC + ROUND(OLD.bet_amount::NUMERIC * (OLD.final_multiplier::NUMERIC * 1000) / 1000))::TEXT
      WHERE game_type = OLD.game_type AND active = TRUE;
    ELSIF OLD.status = 'refunded' THEN
      -- Reverse: bet was returned, now reserve it again
      UPDATE game_max_balances
      SET max_balance = (max_balance::NUMERIC + OLD.bet_amount::NUMERIC)::TEXT
      WHERE game_type = OLD.game_type AND active = TRUE;
    END IF;

    -- Then apply the NEW status effect
    IF NEW.status = 'lost' THEN
      -- Game lost: treasury gains the bet (but it was already reserved as active)
      -- No additional change needed - the reserved amount becomes treasury gain
    ELSIF NEW.status = 'won' THEN
      -- Game won: treasury pays out (subtract payout, the reserved bet stays reserved)
      UPDATE game_max_balances 
      SET max_balance = (max_balance::NUMERIC - ROUND(NEW.bet_amount::NUMERIC * (NEW.final_multiplier::NUMERIC * 1000) / 1000))::TEXT
      WHERE game_type = NEW.game_type AND active = TRUE;
    ELSIF NEW.status = 'refunded' THEN
      -- Game refunded: return the reserved bet
      UPDATE game_max_balances
      SET max_balance = (max_balance::NUMERIC - NEW.bet_amount::NUMERIC)::TEXT
      WHERE game_type = NEW.game_type AND active = TRUE;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_game_max_balances_max_balance ON games;

CREATE TRIGGER trigger_update_game_max_balances_max_balance
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_game_max_balances_max_balance();

-- Fix max_balance for laser_party to reflect correct treasury balance
-- Calculate the actual balance starting from 10 ETH initial balance
DO $$
DECLARE
    initial_balance NUMERIC := 10000000000000000000; -- 10 ETH in wei
    current_max_balance NUMERIC;
    treasury_delta NUMERIC;
    correct_balance NUMERIC;
BEGIN
    -- Get the current incorrect max_balance
    SELECT max_balance::NUMERIC INTO current_max_balance 
    FROM game_max_balances 
    WHERE game_type = 'laser_party' AND active = TRUE;
    
    -- Calculate treasury delta by summing the actual game outcomes
    SELECT COALESCE(SUM(
        CASE 
            WHEN status = 'active' THEN bet_amount::NUMERIC  -- Reserve bet for active games
            WHEN status = 'lost' THEN bet_amount::NUMERIC
            WHEN status = 'won' THEN -1 * ROUND(bet_amount::NUMERIC * (final_multiplier::NUMERIC * 1000) / 1000)
            ELSE 0
        END
    ), 0) INTO treasury_delta
    FROM games 
    WHERE game_type = 'laser_party' AND status IN ('active', 'won', 'lost');
    
    -- Calculate correct balance: initial + treasury delta
    correct_balance := initial_balance + treasury_delta;
    
    -- Update the max_balance to the correct value
    UPDATE game_max_balances 
    SET max_balance = correct_balance::TEXT
    WHERE game_type = 'laser_party' AND active = TRUE;
    
    RAISE NOTICE 'Fixed laser_party max_balance: current=%, initial=%, treasury_delta=%, correct=%', 
        current_max_balance, initial_balance, treasury_delta, correct_balance;
END $$;