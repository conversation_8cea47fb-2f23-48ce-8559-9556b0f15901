-- Add custom_referral_percentage column to users table
-- This allows setting custom referral percentages for special partners
-- NULL value means use the global REFERRAL_PERCENTAGE constant
-- Non-null value overrides the global percentage for that specific user

ALTER TABLE users 
ADD COLUMN custom_referral_percentage NUMERIC(5,4) NULL;

-- Add a comment to document the column
COMMENT ON COLUMN users.custom_referral_percentage IS 'Custom referral percentage for special partners. NULL means use global percentage. Value should be between 0 and 1 (e.g., 0.002 for 0.2%)';

-- Add a check constraint to ensure the percentage is reasonable (between 0 and 2.5%)
-- This prevents setting unreasonably high referral percentages that could break the economics
ALTER TABLE users
ADD CONSTRAINT users_custom_referral_percentage_check
CHECK (custom_referral_percentage IS NULL OR (custom_referral_percentage >= 0 AND custom_referral_percentage <= 0.025));
