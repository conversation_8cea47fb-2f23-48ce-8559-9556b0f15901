-- Fix the update_user_stats function to include net calculation
-- The net calculation was accidentally removed in the daily leaderboard migration

DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
DROP FUNCTION IF EXISTS update_user_stats();

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
    WHERE id = NEW.id;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::NUMERIC
    WHERE id = NEW.id;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    -- Remove stats for old status
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily - CASE
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = OLD.wallet_address;
    END IF;

    -- Add stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily + CASE
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = NEW.wallet_address;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats();

-- Backfill net values for games that are missing them
-- This will calculate net for all completed games that don't have a net value
UPDATE games 
SET net = CASE 
  WHEN status = 'won' THEN -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
  WHEN status = 'lost' THEN bet_amount::NUMERIC
  ELSE NULL
END
WHERE status IN ('won', 'lost') AND net IS NULL;

-- Add comment explaining the fix
COMMENT ON FUNCTION update_user_stats() IS 'Fixed version that includes net calculation for treasury delta tracking. The net calculation was accidentally removed in the daily leaderboard migration.'; 