-- adjust the distribute_and_reset_daily_leaderboard function to sync to leaderboard_records
DROP FUNCTION IF EXISTS distribute_and_reset_daily_leaderboard(TEXT[], INT[], TEXT);

-- Distribute points to top N users and reset daily leaderboard in a single transaction
CREATE OR REPLACE FUNCTION distribute_and_reset_daily_leaderboard(wallet_addresses TEXT[], points_to_add INT[], p_game_type TEXT)
RETURNS void AS $$
DECLARE
  i integer;
  wallet_count integer;
  points_count integer;
  updated_rows integer;
  successful_updates integer := 0;
  skipped_updates integer := 0;
BEGIN
  -- Validate input arrays
  wallet_count := array_length(wallet_addresses, 1);
  points_count := array_length(points_to_add, 1);
  
  -- Check for null or empty arrays - if so, just do the reset
  IF wallet_count IS NULL OR points_count IS NULL THEN
    RAISE NOTICE 'Input arrays are null or empty, skipping point distribution';
    -- Still do the daily reset
    UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = p_game_type;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % leaderboard records (game_type: %)', updated_rows, p_game_type;
    RETURN;
  END IF;
  
  -- Check that arrays have the same length - if not, skip point distribution
  IF wallet_count != points_count THEN
    RAISE NOTICE 'Array length mismatch (wallets: %, points: %), skipping point distribution', wallet_count, points_count;
    -- Still do the daily reset
    UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = p_game_type;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % leaderboard records (game_type: %)', updated_rows, p_game_type;
    RETURN;
  END IF;

  -- Update each winner's points (skip problematic ones, continue with others)
  FOR i IN 1..wallet_count LOOP
    BEGIN
      -- Skip negative points but log it
      IF points_to_add[i] < 0 THEN
        RAISE NOTICE 'Skipping negative points (%) for wallet %', points_to_add[i], wallet_addresses[i];
        skipped_updates := skipped_updates + 1;
        CONTINUE;
      END IF;
      
      UPDATE users
      SET points = points + points_to_add[i]
      WHERE wallet_address = wallet_addresses[i];
      
      -- Check if the update succeeded
      GET DIAGNOSTICS updated_rows = ROW_COUNT;
      IF updated_rows = 1 THEN
        successful_updates := successful_updates + 1;
      ELSE
        RAISE NOTICE 'Wallet address not found, skipping: %', wallet_addresses[i];
        skipped_updates := skipped_updates + 1;
      END IF;
      
    EXCEPTION
      WHEN OTHERS THEN
        -- Log the error but continue processing
        RAISE NOTICE 'Error updating wallet %, skipping: %', wallet_addresses[i], SQLERRM;
        skipped_updates := skipped_updates + 1;
    END;
  END LOOP;

  -- Always do the daily reset regardless of point distribution success
  UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = p_game_type;
  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  
  -- Log final results
  RAISE NOTICE 'Point distribution complete: % successful, % skipped. Reset profit_loss_daily for % leaderboard records (game_type: %)', 
    successful_updates, skipped_updates, updated_rows, p_game_type;
END;
$$ LANGUAGE plpgsql; 