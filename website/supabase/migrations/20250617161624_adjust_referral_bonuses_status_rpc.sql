CREATE OR REPLACE FUNCTION adjust_referral_bonuses_status(
    p_ids UUID[],
    p_status TEXT,
    p_tx_hash TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE v_count INTEGER;
BEGIN
    -- check if the status is valid
    IF p_status NOT IN ('unpaid', 'pending', 'paid', 'failed') THEN
        RAISE EXCEPTION 'Invalid status: %', p_status;
    END IF;

    -- check if the tx_hash is provided if the status is paid
    IF p_status = 'paid' AND p_tx_hash IS NULL THEN
        RAISE EXCEPTION 'Tx hash is required for paid status';
    END IF;
    
    -- check if tx_hash is provided but the status is not paid
    IF p_tx_hash IS NOT NULL AND p_status != 'paid' THEN
        RAISE EXCEPTION 'Tx hash is only allowed for paid status';
    END IF;

    -- update the status of the bonuses and return the number of rows updated
    UPDATE referral_bonuses
    SET 
        status = p_status,
        transaction_signature = CASE WHEN p_status = 'paid' THEN p_tx_hash ELSE transaction_signature END,
        updated_at = NOW()
    WHERE id IN (SELECT UNNEST(p_ids));

    -- return the number of rows updated
    GET DIAGNOSTICS v_count = ROW_COUNT;
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;