create table locks (
    user_id uuid references users(id) on delete cascade not null,
    game_id uuid references games(id) on delete cascade not null,
    type text not null,
    created_at timestamptz not null default now(),
    constraint locks_user_id_type_unique unique (user_id, type)
);

-- Function to create a lock, handling constraint failures by checking the age of existing locks
create or replace function get_lock(
    p_user_id uuid,
    p_game_id uuid,
    p_type text,
    p_status text,
    p_threshold interval default interval '40 seconds'
) returns void as $$
declare
    v_existing_created_at timestamptz;
begin
    -- Try to insert the lock
    insert into locks (user_id, game_id, type)
    values (p_user_id, p_game_id, p_type);

    -- Update game status to cashout_pending
    update games
    set status = p_status
    where id = p_game_id;
exception
    when unique_violation then
        -- If a unique constraint violation occurs, check the age of the existing lock
        select created_at into v_existing_created_at
        from locks
        where user_id = p_user_id and type = p_type;
        
        if v_existing_created_at < now() - p_threshold then
            -- If the existing lock is older than the threshold, delete it and re-insert
            delete from locks
            where user_id = p_user_id and type = p_type;
            
            insert into locks (user_id, game_id, type)
            values (p_user_id, p_game_id, p_type);

            -- Update game status to cashout_pending
            update games
            set status = p_status
            where id = p_game_id;
        else
            -- If the existing lock is not old enough, raise an exception
            raise exception 'Lock already exists and is not expired';
        end if;
end;
$$ language plpgsql;


