-- Create game_max_balances table for storing per-game balance limits
CREATE TABLE IF NOT EXISTS game_max_balances (
  game_type TEXT PRIMARY KEY,
  max_balance TEXT NOT NULL, -- Store as TEXT to handle BigInt values
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Add comment to document the table purpose
COMMENT ON TABLE game_max_balances IS 'Stores maximum balance limits for specific game types for security during new game launches';
COMMENT ON COLUMN game_max_balances.game_type IS 'Type of game (death_race, laser_party, etc.)';
COMMENT ON COLUMN game_max_balances.max_balance IS 'Maximum balance this game type can use, stored as TEXT to handle BigInt values';
COMMENT ON COLUMN game_max_balances.active IS 'Whether this max balance limit is currently active';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_game_max_balances_active ON game_max_balances(active) WHERE active = TRUE;

-- <PERSON>reate trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_game_max_balances_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_game_max_balances_updated_at
  BEFORE UPDATE ON game_max_balances
  FOR EACH ROW
  EXECUTE FUNCTION update_game_max_balances_updated_at();



--- Create trigger to automatically update the max_balance column
CREATE OR REPLACE FUNCTION update_game_max_balances_max_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost', 'refunded') AND OLD.status NOT IN ('won', 'lost', 'refunded')) OR
     (OLD.status IN ('won', 'lost', 'refunded') AND NEW.status NOT IN ('won', 'lost', 'refunded')) OR
     (NEW.status IN ('won', 'lost', 'refunded') AND OLD.status IN ('won', 'lost', 'refunded') AND NEW.status != OLD.status) THEN

     --- refunded
     IF NEW.status = 'refunded' THEN
      UPDATE game_max_balances
      SET 
        max_balance = (max_balance::NUMERIC - NEW.bet_amount::NUMERIC)::TEXT
      WHERE 
        game_type = NEW.game_type
        AND active = TRUE;
     END IF;

     -- lost
     IF NEW.status = 'lost' THEN
      UPDATE game_max_balances
      SET 
        max_balance = (max_balance::NUMERIC + NEW.bet_amount::NUMERIC)::TEXT
      WHERE 
        game_type = NEW.game_type
        AND active = TRUE;
     END IF;

     -- won
     IF NEW.status = 'won' THEN
      UPDATE game_max_balances 
      SET 
        max_balance = (max_balance::NUMERIC - ROUND(NEW.bet_amount::NUMERIC * (NEW.final_multiplier::NUMERIC * 1000) / 1000))::TEXT
      WHERE 
        game_type = NEW.game_type
        AND active = TRUE;
     END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_game_max_balances_max_balance
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_game_max_balances_max_balance();