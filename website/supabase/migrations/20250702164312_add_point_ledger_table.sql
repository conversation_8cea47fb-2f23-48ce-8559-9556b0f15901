-- 0. Deprecate the old, unused increment_user_points function.
DROP FUNCTION IF EXISTS increment_user_points(text, numeric);

-- 1. Create the points_ledger table to log all point transactions.
CREATE TABLE IF NOT EXISTS public.points_ledger (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    points NUMERIC(10, 2) NOT NULL,
    source TEXT NOT NULL,
    context JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.points_ledger IS 'A ledger of all points awarded to users, providing a detailed transaction history.';
COMMENT ON COLUMN public.points_ledger.source IS 'The origin of the points award (e.g., "game_won", "game_lost", "daily_leaderboard_reward"). Sources ending with ":total" represent running totals.';
COMMENT ON COLUMN public.points_ledger.context IS 'JSON object containing data used for point calculation (e.g., bet amount, multipliers).';
CREATE INDEX IF NOT EXISTS idx_points_ledger_user_id ON public.points_ledger(user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_points_ledger_user_source_totals ON public.points_ledger(user_id, source) WHERE source LIKE '%:total';


-- 2. Create the core public function for awarding fixed-value points.
-- This is the primary function for all non-multiplier point awards and is called by other functions.
CREATE OR REPLACE FUNCTION public.add_points_to_user_and_log(
    p_user_id UUID,
    p_points_to_add NUMERIC,
    p_source TEXT,
    p_context JSONB
)
RETURNS NUMERIC AS $$
DECLARE
    v_new_total_points NUMERIC;
BEGIN
    IF p_user_id IS NULL OR p_points_to_add = 0 THEN
        SELECT points INTO v_new_total_points FROM public.users WHERE id = p_user_id;
        RETURN COALESCE(v_new_total_points, 0);
    END IF;

    UPDATE public.users
    SET points = COALESCE(points, 0) + p_points_to_add
    WHERE id = p_user_id
    RETURNING points INTO v_new_total_points;

    IF v_new_total_points IS NOT NULL THEN
        -- Insert the individual transaction record
        INSERT INTO public.points_ledger (user_id, points, source, context)
        VALUES (p_user_id, p_points_to_add, p_source, p_context);
        
        -- Upsert the running total record using ON CONFLICT with partial index WHERE clause
        INSERT INTO public.points_ledger (user_id, points, source, context)
        VALUES (p_user_id, p_points_to_add, p_source || ':total', NULL)
        ON CONFLICT (user_id, source) WHERE source LIKE '%:total'
        DO UPDATE SET 
            points = points_ledger.points + EXCLUDED.points,
            context = NULL,
            created_at = now();
    END IF;
    
    RETURN COALESCE(v_new_total_points, 0);
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.add_points_to_user_and_log IS 'Adds a fixed amount of points to a user, logs the transaction, and returns the new total points.';


-- 3. Create overloaded functions for gameplay points that apply a streak multiplier.

-- Version A: The efficient function that accepts a user_id directly.
CREATE OR REPLACE FUNCTION public.increment_user_points_with_multiplier(
    p_user_id UUID,
    p_base_points NUMERIC,
    p_source TEXT,
    p_context JSONB
)
RETURNS NUMERIC AS $$
DECLARE
    v_multiplier NUMERIC(4, 2);
    v_final_points NUMERIC(10, 2);
    v_new_total_points NUMERIC;
    v_updated_context JSONB;
BEGIN
    IF p_user_id IS NULL THEN RETURN 0; END IF;

    SELECT multiplier INTO v_multiplier FROM public.users WHERE id = p_user_id;
    IF v_multiplier IS NULL THEN RETURN 0; END IF;

    v_final_points := ROUND(p_base_points * v_multiplier, 2);
    -- Ensure points are never negative
    IF v_final_points < 0 THEN v_final_points := 0; END IF;

    v_updated_context := COALESCE(
        CASE 
            WHEN jsonb_typeof(p_context) = 'object' THEN p_context
            ELSE jsonb_build_object('original_context', p_context)
        END, 
        '{}'::jsonb
    ) || jsonb_build_object('streak_multiplier', v_multiplier, 'base_points', p_base_points);

    SELECT public.add_points_to_user_and_log(p_user_id, v_final_points, p_source, v_updated_context)
    INTO v_new_total_points;
    
    RETURN v_new_total_points;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.increment_user_points_with_multiplier(UUID, NUMERIC, TEXT, JSONB) IS 'Awards points by user_id, applies a streak multiplier, and logs the transaction.';

-- Version B: The function that accepts a wallet_address.
CREATE OR REPLACE FUNCTION public.increment_user_points_with_multiplier(
    p_wallet_address TEXT,
    p_base_points NUMERIC,
    p_source TEXT,
    p_context JSONB
)
RETURNS NUMERIC AS $$
DECLARE
    v_user_id UUID;
    v_multiplier NUMERIC(4, 2);
    v_final_points NUMERIC(10, 2);
    v_new_total_points NUMERIC;
    v_updated_context JSONB;
BEGIN
    SELECT id, multiplier INTO v_user_id, v_multiplier FROM public.users WHERE wallet_address = p_wallet_address;
    IF v_user_id IS NULL THEN RETURN 0; END IF;
    IF v_multiplier IS NULL THEN RETURN 0; END IF;

    v_final_points := ROUND(p_base_points * v_multiplier, 2);
    -- Ensure points are never negative
    IF v_final_points < 0 THEN v_final_points := 0; END IF;

    v_updated_context := COALESCE(
        CASE 
            WHEN jsonb_typeof(p_context) = 'object' THEN p_context
            ELSE jsonb_build_object('original_context', p_context)
        END, 
        '{}'::jsonb
    ) || jsonb_build_object('streak_multiplier', v_multiplier, 'base_points', p_base_points);

    SELECT public.add_points_to_user_and_log(v_user_id, v_final_points, p_source, v_updated_context)
    INTO v_new_total_points;
    
    RETURN v_new_total_points;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.increment_user_points_with_multiplier(TEXT, NUMERIC, TEXT, JSONB) IS 'Awards points by wallet_address, applies a streak multiplier, and logs the transaction.';


-- 4. Create overloaded functions for distributing daily leaderboard rewards.

-- Version A: The efficient function that accepts an array of user_ids directly.
CREATE OR REPLACE FUNCTION public.distribute_and_reset_daily_leaderboard(
    p_user_ids UUID[],
    points_to_add INT[],
    p_game_type TEXT
)
RETURNS void AS $$
DECLARE
    i integer;
    user_id_count integer;
    points_count integer;
    updated_rows integer;
    successful_updates integer := 0;
    skipped_updates integer := 0;
BEGIN
    user_id_count := array_length(p_user_ids, 1);
    points_count := array_length(points_to_add, 1);

    IF user_id_count IS NULL OR user_id_count = 0 OR points_count IS NULL OR points_count = 0 THEN
        RAISE NOTICE 'Leaderboard distribution for % skipped: input arrays are null or empty.', p_game_type;
    ELSIF user_id_count != points_count THEN
        RAISE NOTICE 'Leaderboard distribution for % skipped: array length mismatch (user_ids: %, points: %).', p_game_type, user_id_count, points_count;
    ELSE
        FOR i IN 1..user_id_count LOOP
            BEGIN
                IF points_to_add[i] <= 0 THEN
                    IF points_to_add[i] < 0 THEN
                        RAISE NOTICE 'Skipping negative points (%) for user_id %', points_to_add[i], p_user_ids[i];
                    END IF;
                    skipped_updates := skipped_updates + 1;
                    CONTINUE;
                END IF;

                PERFORM public.add_points_to_user_and_log(
                    p_user_ids[i],
                    points_to_add[i],
                    'daily_leaderboard',
                    jsonb_build_object('game_type', p_game_type, 'rank', i)
                );
                successful_updates := successful_updates + 1;
                
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error processing user_id % for leaderboard reward, skipping: %', p_user_ids[i], SQLERRM;
                    skipped_updates := skipped_updates + 1;
            END;
        END LOOP;
        RAISE NOTICE 'Point distribution for % complete: % successful, % skipped.', p_game_type, successful_updates, skipped_updates;
    END IF;

    UPDATE public.leaderboard_records SET profit_loss_daily = 0 WHERE game_type = p_game_type AND profit_loss_daily != 0;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % records for game_type %.', updated_rows, p_game_type;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.distribute_and_reset_daily_leaderboard(UUID[], INT[], TEXT) IS 'Efficiently distributes daily leaderboard points using user_ids, with full logging.';

-- Version B: The legacy, backward-compatible function that accepts an array of wallet_addresses.
CREATE OR REPLACE FUNCTION public.distribute_and_reset_daily_leaderboard(
    wallet_addresses TEXT[],
    points_to_add INT[],
    p_game_type TEXT
)
RETURNS void AS $$
DECLARE
    i integer;
    wallet_count integer;
    points_count integer;
    updated_rows integer;
    successful_updates integer := 0;
    skipped_updates integer := 0;
    v_user_id UUID;
BEGIN
    wallet_count := array_length(wallet_addresses, 1);
    points_count := array_length(points_to_add, 1);

    IF wallet_count IS NULL OR wallet_count = 0 OR points_count IS NULL OR points_count = 0 THEN
        RAISE NOTICE 'Leaderboard distribution for % skipped: input arrays are null or empty.', p_game_type;
    ELSIF wallet_count != points_count THEN
        RAISE NOTICE 'Leaderboard distribution for % skipped: array length mismatch (wallets: %, points: %).', p_game_type, wallet_count, points_count;
    ELSE
        FOR i IN 1..wallet_count LOOP
            BEGIN
                IF points_to_add[i] <= 0 THEN
                    IF points_to_add[i] < 0 THEN
                        RAISE NOTICE 'Skipping negative points (%) for wallet %', points_to_add[i], wallet_addresses[i];
                    END IF;
                    skipped_updates := skipped_updates + 1;
                    CONTINUE;
                END IF;

                SELECT id INTO v_user_id FROM public.users WHERE wallet_address = wallet_addresses[i];

                IF v_user_id IS NOT NULL THEN
                    PERFORM public.add_points_to_user_and_log(
                        v_user_id,
                        points_to_add[i],
                        'daily_leaderboard',
                        jsonb_build_object('game_type', p_game_type, 'rank', i)
                    );
                    successful_updates := successful_updates + 1;
                ELSE
                    RAISE NOTICE 'Wallet address not found, skipping leaderboard reward for: %', wallet_addresses[i];
                    skipped_updates := skipped_updates + 1;
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error processing wallet % for leaderboard reward, skipping: %', wallet_addresses[i], SQLERRM;
                    skipped_updates := skipped_updates + 1;
            END;
        END LOOP;
        
        RAISE NOTICE 'Point distribution for % complete: % successful, % skipped.', p_game_type, successful_updates, skipped_updates;
    END IF;

    UPDATE public.leaderboard_records SET profit_loss_daily = 0 WHERE game_type = p_game_type AND profit_loss_daily != 0;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % records for game_type %.', updated_rows, p_game_type;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.distribute_and_reset_daily_leaderboard(TEXT[], INT[], TEXT) IS 'Backward-compatible leaderboard distribution that accepts wallet_addresses. Standalone implementation.';


-- 5. Create a function to process challenge completions with logging.
CREATE OR REPLACE FUNCTION public.process_challenge(
    challenge_id INTEGER,
    user_id UUID
)
RETURNS void AS $$
DECLARE
    v_challenge_points INTEGER;
    v_challenge_title TEXT;
BEGIN
    SELECT points, title INTO v_challenge_points, v_challenge_title
    FROM public.challenges
    WHERE id = challenge_id;

    IF v_challenge_points IS NULL THEN
        RAISE NOTICE 'Challenge with id % not found, skipping point award.', challenge_id;
        RETURN;
    END IF;

    UPDATE public.challenge_submissions
    SET status = 'completed'
    WHERE challenge_submissions.challenge_id = process_challenge.challenge_id 
    AND challenge_submissions.user_id = process_challenge.user_id;

    IF NOT FOUND THEN
        RAISE NOTICE 'No submission found for challenge %, user %, skipping award.', challenge_id, user_id;
        RETURN;
    END IF;

    PERFORM public.add_points_to_user_and_log(
        user_id,
        v_challenge_points,
        'challenge',
        jsonb_build_object('challenge_id', challenge_id)
    );
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.process_challenge(INTEGER, UUID) IS 'Processes challenge completion, updates submission status, and awards points with logging.';

-- 6. Add backward-compatible function for old gameplay point awards.
CREATE OR REPLACE FUNCTION public.increment_user_points_with_multiplier(
    p_wallet_address TEXT,
    p_base_points NUMERIC
)
RETURNS NUMERIC AS $$
BEGIN
    RETURN public.increment_user_points_with_multiplier(
        p_wallet_address,
        p_base_points,
        'gameplay',
        jsonb_build_object('game_type', 'unknown')
    );
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.increment_user_points_with_multiplier(TEXT, NUMERIC) IS 'Backward-compatible wrapper to award gameplay points. This version is logged but lacks detailed context.';
