DROP FUNCTION IF EXISTS get_user_stats(UUID, TEXT);
CREATE OR REPLACE FUNCTION get_user_stats(p_user_id UUID, p_game_type TEXT DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  total_games INT;
  total_bet_amount NUMERIC;
  total_profit_loss NUMERIC;
BEGIN

  CASE
    WHEN p_game_type IS NULL THEN
      -- Aggregate across all game types
      SELECT 
        SUM(lr.total_games),
        SUM(lr.total_bet_amount),
        SUM(lr.total_profit_loss)
      INTO 
        total_games,
        total_bet_amount,
        total_profit_loss
      FROM leaderboard_records lr
      WHERE lr.user_id = p_user_id;
    ELSE
      -- Get specific game type
      SELECT 
        lr.total_games,
        lr.total_bet_amount,
        lr.total_profit_loss
      INTO 
        total_games,
        total_bet_amount,
        total_profit_loss
      FROM leaderboard_records lr
      WHERE lr.user_id = p_user_id
      AND lr.game_type = p_game_type;
  <PERSON>ND CASE;

  RETURN json_build_object(
    'total_games', COALESCE(total_games, 0),
    'total_bet_amount', ROUND(COALESCE(total_bet_amount, 0))::text,
    'total_profit_loss', ROUND(COALESCE(total_profit_loss, 0))::text
  );
END;
$$;