DROP FUNCTION IF EXISTS get_admin_stats(TEXT);

-- Update get_admin_stats function to count users by game_type from leaderboard_records
CREATE OR REPLACE FUNCTION get_admin_stats(game_type TEXT DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    stats json;
BEGIN
    -- Single query to get all game-related stats
    WITH game_stats AS (
        SELECT 
            COUNT(*) as total_games,
            COUNT(*) FILTER (WHERE status = 'lost') as lost_games,
            COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0) as total_volume,
            COALESCE(AVG(final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier > 0), 0) as avg_win_multiplier,
            COALESCE(AVG(current_row_index) FILTER (WHERE status IN ('won', 'lost')), 0) as avg_row_reached,
            COALESCE(ROUND(SUM(net)), 0) as treasury_delta,
            COALESCE(ROUND(SUM(bet_amount::NUMERIC * final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier IS NOT NULL)), 0) as total_payouts
        FROM games
        WHERE (get_admin_stats.game_type IS NULL OR games.game_type = get_admin_stats.game_type)
    ),
    user_count AS (
        SELECT 
            CASE 
                WHEN get_admin_stats.game_type IS NULL THEN 
                    COUNT(DISTINCT user_id)
                ELSE 
                    COUNT(DISTINCT CASE WHEN leaderboard_records.game_type = get_admin_stats.game_type THEN user_id END)
            END as total_users
        FROM leaderboard_records
    )
    SELECT json_build_object(
        'totalGames', gs.total_games,
        'totalVolume', gs.total_volume::TEXT,
        'totalPayouts', gs.total_payouts::TEXT,
        'totalUsers', uc.total_users,
        'treasuryDelta', gs.treasury_delta::TEXT,
        'bustPercentage', ROUND((gs.lost_games::DECIMAL / NULLIF(gs.total_games, 0) * 100)::NUMERIC, 1),
        'averageWinMultiplier', ROUND(gs.avg_win_multiplier::NUMERIC, 2),
        'averageRowReached', ROUND(gs.avg_row_reached::NUMERIC, 1)
    ) INTO stats
    FROM game_stats gs
    CROSS JOIN user_count uc;

    RETURN stats;
END;
$$;


-- create index for leaderboard_records(user_id, game_type)
CREATE INDEX IF NOT EXISTS idx_leaderboard_records_user_id_game_type ON leaderboard_records(user_id, game_type);