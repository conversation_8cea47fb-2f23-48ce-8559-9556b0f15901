-- Create a function to increment user points atomically
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION increment_user_points(
  p_wallet_address TEXT,
  p_points NUMERIC
) RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_new_points NUMERIC;
BEGIN
  UPDATE users
  SET points = COALESCE(points, 0) + p_points
  WHERE wallet_address = p_wallet_address
  RETURNING points INTO v_new_points;

  RETURN v_new_points;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_user_points TO authenticated;
