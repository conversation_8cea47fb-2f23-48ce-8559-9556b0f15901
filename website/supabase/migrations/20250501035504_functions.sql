--- Get admin stats
CREATE OR REPLACE FUNCTION get_admin_stats()
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
    total_games_count INTEGER;
    lost_games_count INTEGER;
    bust_percentage DECIMAL;
    total_volume BIGINT;
    average_win_multiplier DECIMAL;
    average_row_reached DECIMAL;
    treasury_delta BIGINT;
    total_users_count INTEGER;
    total_payouts BIGINT;
BEGIN
    -- Get total games played
    SELECT COUNT(*) INTO total_games_count
    FROM games;
    
    -- Get count of lost games
    SELECT COUNT(*) INTO lost_games_count
    FROM games
    WHERE status = 'lost';
    
    -- Calculate bust percentage
    bust_percentage := CASE WHEN total_games_count > 0 
                         THEN (lost_games_count::DECIMAL / total_games_count) * 100 
                         ELSE 0 
                      END;
    
    -- Get total volume (sum of all bets)
    SELECT COALESCE(SUM(bet_amount), 0) INTO total_volume
    FROM games
    WHERE status IN ('won', 'lost');
    
    -- Get average win multiplier
    SELECT COALESCE(AVG(final_multiplier), 0) INTO average_win_multiplier
    FROM games
    WHERE status = 'won'
    AND final_multiplier IS NOT NULL
    AND final_multiplier > 0;
    
    -- Get average row reached
    SELECT COALESCE(AVG(current_row_index), 0) INTO average_row_reached
    FROM games
    WHERE status IN ('won', 'lost');
    
    -- Get treasury delta
    SELECT COALESCE(SUM(net), 0) INTO treasury_delta
    FROM games
    WHERE net IS NOT NULL;
    
    -- Get total users
    SELECT COUNT(*) INTO total_users_count
    FROM users;
    
    -- Calculate total payouts to players
    SELECT COALESCE(SUM(bet_amount * final_multiplier), 0) INTO total_payouts
    FROM games
    WHERE status = 'won'
    AND final_multiplier IS NOT NULL;
    
    -- Return all stats as JSON
    RETURN json_build_object(
        'totalGames', total_games_count,
        'totalVolume', total_volume,
        'totalPayouts', total_payouts,
        'totalUsers', total_users_count,
        'treasuryDelta', treasury_delta,
        'bustPercentage', ROUND(bust_percentage::NUMERIC, 1),
        'averageWinMultiplier', ROUND(average_win_multiplier::NUMERIC, 2),
        'averageRowReached', ROUND(average_row_reached::NUMERIC, 1)
    );
END;
$$;

-- get game with lock
CREATE OR REPLACE FUNCTION get_game_with_lock(game_id UUID)
RETURNS SETOF games
LANGUAGE plpgsql
AS $$
DECLARE
    locked_game games%ROWTYPE;
BEGIN
    -- Acquire an exclusive lock on the row 
    SELECT * INTO locked_game
    FROM games
    WHERE id = game_id
    FOR UPDATE NOWAIT;
    
    -- If the row exists, return it
    IF FOUND THEN
        RETURN NEXT locked_game;
    END IF;
    
    RETURN;
END;
$$;


-- get user stats
CREATE OR REPLACE FUNCTION get_user_stats(wallet TEXT)
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  total_games INT;
  total_bet_amount BIGINT;
  total_won_amount BIGINT;
  total_profit_loss BIGINT;
  profit_loss_7d BIGINT;
  profit_loss_30d BIGINT;
BEGIN
  -- Get total number of completed games
  SELECT COUNT(*) INTO total_games
  FROM games
  WHERE wallet_address = wallet
  AND status IN ('complete', 'lost');
  
  -- Get total bet amount from all completed games
  SELECT COALESCE(SUM(bet_amount), 0) INTO total_bet_amount
  FROM games
  WHERE wallet_address = wallet
  AND status IN ('complete', 'lost');
  
  -- Get total won amount (from completed games with positive multiplier)
  SELECT COALESCE(SUM(bet_amount * final_multiplier), 0) INTO total_won_amount
  FROM games
  WHERE wallet_address = wallet
  AND status = 'complete'
  AND final_multiplier > 0;
  
  -- Get profit/loss values from users table
  SELECT u.total_profit_loss, u.profit_loss_7d, u.profit_loss_30d 
  INTO total_profit_loss, profit_loss_7d, profit_loss_30d
  FROM users u
  WHERE u.wallet_address = wallet;
  
  -- Return as JSON
  RETURN json_build_object(
    'total_games', total_games,
    'total_bet_amount', total_bet_amount,
    'total_won_amount', total_won_amount,
    'total_profit_loss', total_profit_loss,
    'profit_loss_7d', profit_loss_7d,
    'profit_loss_30d', profit_loss_30d
  );
END;
$$;


-- generate referral code
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  code TEXT := '';
  i INTEGER;
  exists BOOLEAN;
BEGIN
  -- Keep trying until we get a unique code
  LOOP
    -- Generate a random 6-character code
    FOR i IN 1..6 LOOP
      code := code || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    
    -- Check if code exists
    SELECT EXISTS (
      SELECT 1 FROM users WHERE referral_code = code
    ) INTO exists;
    
    -- If code doesn't exist, we can use it
    IF NOT exists THEN
      RETURN code;
    END IF;
    
    -- Reset code for next attempt
    code := '';
  END LOOP;
END;
$$;
