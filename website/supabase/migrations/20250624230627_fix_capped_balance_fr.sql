-- Fix max_balance for laser_party to reflect correct treasury balance
-- Calculate the actual balance starting from 10 ETH initial balance
DO $$
DECLARE
    initial_balance NUMERIC := 10000000000000000000; -- 10 ETH in wei
    current_max_balance NUMERIC;
    treasury_delta NUMERIC;
    correct_balance NUMERIC;
BEGIN
    -- Get the current incorrect max_balance
    SELECT max_balance::NUMERIC INTO current_max_balance 
    FROM game_max_balances 
    WHERE game_type = 'laser_party' AND active = TRUE;
    
    -- Calculate treasury delta by summing the actual game outcomes
    SELECT COALESCE(SUM(
        CASE 
            WHEN status = 'active' THEN bet_amount::NUMERIC  -- Reserve bet for active games
            WHEN status = 'lost' THEN net
            WHEN status = 'won' THEN  net
            ELSE 0
        END
    ), 0) INTO treasury_delta
    FROM games 
    WHERE game_type = 'laser_party' AND status IN ('active', 'won', 'lost');
    
    -- Calculate correct balance: initial + treasury delta
    correct_balance := initial_balance + treasury_delta;
    
    -- Update the max_balance to the correct value
    UPDATE game_max_balances 
    SET max_balance = ROUND(correct_balance)::TEXT
    WHERE game_type = 'laser_party' AND active = TRUE;
    
    RAISE NOTICE 'Fixed laser_party max_balance: current=%, initial=%, treasury_delta=%, correct=%', 
        current_max_balance, initial_balance, treasury_delta, correct_balance;
END $$;