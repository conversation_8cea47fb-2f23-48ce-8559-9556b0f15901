-- Drop the old function
DROP FUNCTION IF EXISTS get_admin_stats();

-- Create optimized function
CREATE OR REPLACE FUNCTION get_admin_stats()
RETURNS json
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    stats json;
BEGIN
    -- Single query to get all game-related stats
    WITH game_stats AS (
        SELECT 
            COUNT(*) as total_games,
            COUNT(*) FILTER (WHERE status = 'lost') as lost_games,
            COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0) as total_volume,
            COALESCE(AVG(final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier > 0), 0) as avg_win_multiplier,
            COALESCE(AVG(current_row_index) FILTER (WHERE status IN ('won', 'lost')), 0) as avg_row_reached,
            COALESCE(SUM(net), 0) as treasury_delta,
            COALESCE(ROUND(SUM(bet_amount::NUMERIC * final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier IS NOT NULL)), 0) as total_payouts
        FROM games
    ),
    user_count AS (
        SELECT COUNT(*) as total_users
        FROM users
    )
    SELECT json_build_object(
        'totalGames', gs.total_games,
        'totalVolume', gs.total_volume::TEXT,
        'totalPayouts', gs.total_payouts::TEXT,
        'totalUsers', uc.total_users,
        'treasuryDelta', gs.treasury_delta::TEXT,
        'bustPercentage', ROUND((gs.lost_games::DECIMAL / NULLIF(gs.total_games, 0) * 100)::NUMERIC, 1),
        'averageWinMultiplier', ROUND(gs.avg_win_multiplier::NUMERIC, 2),
        'averageRowReached', ROUND(gs.avg_row_reached::NUMERIC, 1)
    ) INTO stats
    FROM game_stats gs
    CROSS JOIN user_count uc;

    RETURN stats;
END;
$$;

-- Add helpful indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
CREATE INDEX IF NOT EXISTS idx_games_status_final_multiplier ON games(status, final_multiplier) WHERE final_multiplier IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_games_status_current_row ON games(status, current_row_index);

-- Add comment explaining the optimization
COMMENT ON FUNCTION get_admin_stats() IS 'Optimized version that uses a single table scan and leverages indexes for better performance with large datasets';
