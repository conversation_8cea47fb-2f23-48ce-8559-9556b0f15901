-- Allow null values for game_id in locks table
-- This enables locks that aren't tied to specific games (e.g., global user locks)
ALTER TABLE locks ALTER COLUMN game_id DROP NOT NULL;

-- Function to create a user lock, preventing concurrent operations of a specific type
create or replace function get_user_lock(
    p_user_id uuid,
    p_type text,
    p_threshold interval default interval '40 seconds'
) returns void as $$
declare
    v_existing_created_at timestamptz;
begin
    -- Try to insert the lock (game_id is null for user-level locks)
    insert into locks (user_id, game_id, type)
    values (p_user_id, null, p_type);

exception
    when unique_violation then
        -- If a unique constraint violation occurs, check the age of the existing lock
        select created_at into v_existing_created_at
        from locks
        where user_id = p_user_id and type = p_type;
        
        if v_existing_created_at < now() - p_threshold then
            -- If the existing lock is older than the threshold, delete it and re-insert
            delete from locks
            where user_id = p_user_id and type = p_type;
            
            insert into locks (user_id, game_id, type)
            values (p_user_id, null, p_type);
        else
            -- If the existing lock is not old enough, raise an exception
            raise exception 'User lock of type % already exists and is not expired', p_type;
        end if;
end;
$$ language plpgsql;


