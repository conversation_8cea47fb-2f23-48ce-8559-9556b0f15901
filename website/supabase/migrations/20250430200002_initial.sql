-- games triggers

DROP FUNCTION IF EXISTS update_user_stats();

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::BIGINT * (final_multiplier - 1))::BIGINT
    WHERE id = NEW.id;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::BIGINT
    WHERE id = NEW.id;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    -- Remove stats for old status
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::BIGINT - OLD.bet_amount::BIGINT
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::BIGINT
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN OLD.final_multiplier * OLD.bet_amount::BIGINT - OLD.bet_amount::BIGINT
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN -OLD.bet_amount::BIGINT
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d - CASE
          WHEN OLD.status = 'won' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN OLD.final_multiplier * OLD.bet_amount::BIGINT - OLD.bet_amount::BIGINT
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN -OLD.bet_amount::BIGINT
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = OLD.wallet_address;
    END IF;

    -- Add stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::BIGINT - NEW.bet_amount::BIGINT
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::BIGINT
          ELSE 0
        END,
        profit_loss_7d = profit_loss_7d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN NEW.final_multiplier * NEW.bet_amount::BIGINT - NEW.bet_amount::BIGINT
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN -NEW.bet_amount::BIGINT
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d + CASE
          WHEN NEW.status = 'won' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN NEW.final_multiplier * NEW.bet_amount::BIGINT - NEW.bet_amount::BIGINT
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN -NEW.bet_amount::BIGINT
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = NEW.wallet_address;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
DROP FUNCTION IF EXISTS trigger_set_timestamp();
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- games

DROP TABLE IF EXISTS public.games;
create table public.games (
  id uuid not null default extensions.uuid_generate_v4 (),
  wallet_address text not null,
  bet_amount text not null,
  status text not null,
  rows jsonb not null,
  current_row_index integer not null default '-1'::integer,
  selected_tiles integer[] null default array[]::integer[],
  final_multiplier numeric(10, 2) null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  transaction_signature text null,
  payout_tx_signature text null,
  payout_attempts integer null default 0,
  payout_error text null,
  game_seed text null,
  commitment_hash text null,
  error_reason text null,
  requires_manual_action boolean null default false,
  payout_sent_at timestamp with time zone null,
  refund_tx_signature text null,
  refund_amount double precision null,
  net bigint null,
  app text null,
  onchain_game_id text null,
  constraint games_pkey primary key (id),
  constraint unique_transaction_signature unique (transaction_signature)
) TABLESPACE pg_default;

create index IF not exists idx_games_transaction_signature on public.games using btree (transaction_signature) TABLESPACE pg_default;

create index IF not exists idx_games_payout_tx_signature on public.games using btree (payout_tx_signature) TABLESPACE pg_default;

create index IF not exists idx_games_app on public.games using btree (app) TABLESPACE pg_default;

create index IF not exists idx_games_pending_payouts on public.games using btree (status, updated_at) TABLESPACE pg_default
where
  (status = 'payout_pending'::text);

create index IF not exists idx_games_upcoming_by_wallet on public.games using btree (wallet_address, status) TABLESPACE pg_default
where
  (status = 'upcoming'::text);

create index IF not exists idx_games_tx_signature on public.games using btree (transaction_signature) TABLESPACE pg_default;

create index IF not exists idx_games_wallet_address on public.games using btree (wallet_address) TABLESPACE pg_default;

create trigger update_user_stats_trigger
after
update on games for EACH row
execute FUNCTION update_user_stats ();

create trigger set_timestamp BEFORE
update on games for EACH row
execute FUNCTION trigger_set_timestamp ();


-- users

DROP TABLE IF EXISTS public.users;
create table public.users (
  id uuid not null default extensions.uuid_generate_v4 (),
  wallet_address text not null,
  referral_code text not null,
  referred_by uuid null,
  created_at timestamp with time zone not null default now(),
  last_login_at timestamp with time zone null,
  total_referral_bonuses bigint null default 0,
  total_referral_bonuses_paid bigint null default 0,
  total_profit_loss bigint null default 0,
  profit_loss_7d bigint null default 0,
  profit_loss_30d bigint null default 0,
  total_games integer null default 0,
  games_won integer null default 0,
  games_lost integer null default 0,
  last_updated timestamp with time zone null default now(),
  username text null,
  role text not null default 'user'::text,
  app text null,
  session_config jsonb null,
  constraint users_pkey primary key (id),
  constraint users_username_key unique (username),
  constraint users_wallet_address_app_key unique (wallet_address, app),
  constraint users_referral_code_key unique (referral_code),
  constraint users_wallet_address_key unique (wallet_address),
  constraint fk_referred_by foreign KEY (referred_by) references users (id),
  constraint users_referred_by_fkey foreign KEY (referred_by) references users (id),
  constraint users_role_check check ((role = any (array['user'::text, 'admin'::text])))
) TABLESPACE pg_default;

create index IF not exists idx_users_app on public.users using btree (app) TABLESPACE pg_default;

create index IF not exists idx_users_app_wallet_address on public.users using btree (app, wallet_address) TABLESPACE pg_default;

create index IF not exists idx_users_wallet on public.users using btree (wallet_address) TABLESPACE pg_default;

create index IF not exists idx_users_referral_code on public.users using btree (referral_code) TABLESPACE pg_default;

create index IF not exists idx_users_referred_by on public.users using btree (referred_by) TABLESPACE pg_default;


-- referral_bonuses

DROP TABLE IF EXISTS public.referral_bonuses;

create table public.referral_bonuses (
  id uuid not null default extensions.uuid_generate_v4 (),
  referrer_id uuid not null,
  referred_id uuid not null,
  game_id uuid not null,
  bonus_amount bigint not null,
  status text not null,
  created_at timestamp with time zone not null default now(),
  paid_at timestamp with time zone null,
  transaction_signature text null,
  app text null,
  constraint referral_bonuses_pkey primary key (id),
  constraint fk_game foreign KEY (game_id) references games (id),
  constraint referral_bonuses_game_id_fkey foreign KEY (game_id) references games (id),
  constraint referral_bonuses_referred_id_fkey foreign KEY (referred_id) references users (id),
  constraint referral_bonuses_referrer_id_fkey foreign KEY (referrer_id) references users (id),
  constraint referral_bonuses_status_check check (
    (
      status = any (
        array['unpaid'::text, 'pending'::text, 'paid'::text]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_referral_bonuses_tx_signature on public.referral_bonuses using btree (transaction_signature) TABLESPACE pg_default;

create index IF not exists idx_referral_bonuses_app on public.referral_bonuses using btree (app) TABLESPACE pg_default;

create index IF not exists idx_bonuses_referrer on public.referral_bonuses using btree (referrer_id) TABLESPACE pg_default;

create index IF not exists idx_bonuses_referred on public.referral_bonuses using btree (referred_id) TABLESPACE pg_default;

create index IF not exists idx_bonuses_status on public.referral_bonuses using btree (status) TABLESPACE pg_default;