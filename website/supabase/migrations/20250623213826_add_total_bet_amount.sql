DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'leaderboard_records' 
        AND column_name = 'total_bet_amount'
    ) THEN
        ALTER TABLE leaderboard_records
        ADD COLUMN total_bet_amount NUMERIC;
    END IF;
END $$;

-- adjust the update_user_stats function to sync to leaderboard_records
DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
DROP FUNCTION IF EXISTS update_user_stats();

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
    WHERE id = NEW.id;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::NUMERIC
    WHERE id = NEW.id;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    --- migrate old stats or create new default record
    INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily, total_bet_amount)
    SELECT 
        id,
        OLD.game_type,
        total_games,
        games_won,
        games_lost,
        total_profit_loss,
        profit_loss_daily,
        (SELECT COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0)::NUMERIC from games where wallet_address = OLD.wallet_address and game_type = OLD.game_type) as total_bet_amount
    FROM users
    WHERE wallet_address = OLD.wallet_address
    ON CONFLICT (user_id, game_type) DO NOTHING;

    --- Update stats for corrections
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily - CASE
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        total_bet_amount = total_bet_amount - OLD.bet_amount::NUMERIC
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = OLD.wallet_address)
        AND game_type = OLD.game_type;
    END IF;


    --- Update stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = COALESCE(total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END, 0),
        profit_loss_daily = profit_loss_daily + CASE
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        total_bet_amount = total_bet_amount + NEW.bet_amount::NUMERIC
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = NEW.wallet_address)
        AND game_type = NEW.game_type;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats();


-- backfill total_bet_amount in batches
DO $$
DECLARE
    batch_size INT := 1000;
    rows_processed INT;
BEGIN
    LOOP
        WITH batch AS (
          SELECT user_id, game_type
          FROM leaderboard_records 
          WHERE game_type IN ('death_race', 'laser_party')
          AND total_bet_amount IS NULL
          ORDER BY user_id, game_type
          LIMIT batch_size
        )
        UPDATE leaderboard_records lr
        SET total_bet_amount = (
          SELECT COALESCE(SUM(g.bet_amount::NUMERIC) FILTER (WHERE g.status IN ('won', 'lost')), 0)::NUMERIC 
          FROM games g 
          WHERE g.wallet_address = u.wallet_address 
          AND g.game_type = lr.game_type
        )
        FROM users u, batch b
        WHERE lr.user_id = u.id
        AND lr.user_id = b.user_id
        AND lr.game_type = b.game_type;

        GET DIAGNOSTICS rows_processed = ROW_COUNT;
        EXIT WHEN rows_processed = 0;
        
        -- Optional: Add small delay to reduce load
        PERFORM pg_sleep(0.1);
    END LOOP;
END $$;