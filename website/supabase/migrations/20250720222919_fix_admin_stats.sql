-- Fix admin stats accounting issues in update_user_stats function
-- Addresses double counting, incorrect volume/payout tracking, treasury delta calculation, and ensures whole number results
-- Uses bet_amount - net calculation for payouts to match actual payout system precision

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
DECLARE
  user_id_var UUID;
BEGIN
  
  -- Get user_id once and reuse it throughout the function
  SELECT id INTO user_id_var FROM users WHERE wallet_address = OLD.wallet_address;
  
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
    WHERE id = NEW.id
    RETURNING net INTO NEW.net;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::NUMERIC
    WHERE id = NEW.id
    RETURNING net INTO NEW.net;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    -- Migrate old stats or create new default record only if record doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM leaderboard_records 
        WHERE user_id = user_id_var
        AND game_type = OLD.game_type
    ) THEN
        INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily, total_bet_amount)
        WITH user_stats AS (
            SELECT 
                COUNT(*) FILTER (WHERE status IN ('won', 'lost')) as total_games,
                COUNT(*) FILTER (WHERE status = 'won') as games_won,
                COUNT(*) FILTER (WHERE status = 'lost') as games_lost,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as total_profit_loss,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' AND is_in_current_day(updated_at) THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' AND is_in_current_day(updated_at) THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as profit_loss_daily,
                COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0)::NUMERIC as total_bet_amount
            FROM games 
            WHERE wallet_address = OLD.wallet_address 
            AND game_type = OLD.game_type
            AND id != OLD.id  -- Exclude current game to avoid double counting
        )
        SELECT 
            user_id_var,
            OLD.game_type,
            user_stats.total_games,
            user_stats.games_won,
            user_stats.games_lost,
            user_stats.total_profit_loss,
            user_stats.profit_loss_daily,
            user_stats.total_bet_amount
        FROM user_stats;

    END IF;

    -- Update admin_stats_overall for corrections (remove old stats)
    IF OLD.status IN ('won', 'lost') THEN
      -- Check if user transitions from >0 to 0 games for unique user counting
      DECLARE
        user_games_after INT;
        user_total_games_after INT;
      BEGIN
        -- Update leaderboard_records and get the new total_games value
        UPDATE leaderboard_records
        SET
          total_games = total_games - 1,
          games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
          games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
          total_profit_loss = total_profit_loss - CASE
            WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
            WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
            ELSE 0
          END,
          profit_loss_daily = profit_loss_daily - CASE
            WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
            WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
            ELSE 0
          END,
          total_bet_amount = total_bet_amount - OLD.bet_amount::NUMERIC
        WHERE user_id = user_id_var
          AND game_type = OLD.game_type
        RETURNING total_games INTO user_games_after;
        
        -- If user went from 1 to 0 games for this game type, decrement unique user count
        IF user_games_after = 0 THEN
          INSERT INTO admin_stats_overall (game_type, unique_users)
          VALUES (OLD.game_type, 0)
          ON CONFLICT (game_type) DO UPDATE SET 
            unique_users = GREATEST(admin_stats_overall.unique_users - 1, 0),
            updated_at = NOW();
          
          -- Check if user went from >0 to 0 games across ALL game types
          SELECT COALESCE(SUM(total_games), 0) INTO user_total_games_after
          FROM leaderboard_records
          WHERE user_id = user_id_var;
          
          -- If user now has 0 games across all types, decrement overall unique user count
          IF user_total_games_after = 0 THEN
            INSERT INTO admin_stats_overall (game_type, unique_users)
            VALUES ('all', 0)
            ON CONFLICT (game_type) DO UPDATE SET 
              unique_users = GREATEST(admin_stats_overall.unique_users - 1, 0),
              updated_at = NOW();
          END IF;
        END IF;
      END;

      -- Update admin_stats_overall for this game type
      UPDATE admin_stats_overall SET
        total_games = total_games - 1,
        lost_games = lost_games - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_volume = total_volume - OLD.bet_amount::NUMERIC,
        total_payouts = total_payouts - CASE 
          WHEN OLD.status = 'won' AND OLD.net IS NOT NULL 
          THEN OLD.bet_amount::NUMERIC - OLD.net 
          ELSE 0 
        END,
        treasury_delta = treasury_delta - COALESCE(OLD.net, 0),
        sum_win_multipliers = sum_win_multipliers - CASE 
          WHEN OLD.status = 'won' AND OLD.final_multiplier IS NOT NULL 
          THEN OLD.final_multiplier 
          ELSE 0 
        END,
        sum_row_reached = sum_row_reached - COALESCE(OLD.current_row_index, 0),
        updated_at = NOW()
      WHERE game_type = OLD.game_type;

      -- Update overall admin stats (game_type = 'all')
      UPDATE admin_stats_overall SET
        total_games = total_games - 1,
        lost_games = lost_games - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_volume = total_volume - OLD.bet_amount::NUMERIC,
        total_payouts = total_payouts - CASE 
          WHEN OLD.status = 'won' AND OLD.net IS NOT NULL 
          THEN OLD.bet_amount::NUMERIC - OLD.net 
          ELSE 0 
        END,
        treasury_delta = treasury_delta - COALESCE(OLD.net, 0),
        sum_win_multipliers = sum_win_multipliers - CASE 
          WHEN OLD.status = 'won' AND OLD.final_multiplier IS NOT NULL 
          THEN OLD.final_multiplier 
          ELSE 0 
        END,
        sum_row_reached = sum_row_reached - COALESCE(OLD.current_row_index, 0),
        updated_at = NOW()
      WHERE game_type = 'all';
    END IF;

    -- Update admin_stats_overall for new status (add new stats)
    IF NEW.status IN ('won', 'lost') THEN
      -- Check if user transitions from 0 to >0 games for unique user counting
      DECLARE
        user_games_after INT;
        user_total_games_after INT;
      BEGIN
        -- Update leaderboard_records and get the new total_games value
        UPDATE leaderboard_records
        SET
          total_games = total_games + 1,
          games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
          games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
          total_profit_loss = COALESCE(total_profit_loss + CASE
            WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
            WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
            ELSE 0
          END, 0),
          profit_loss_daily = COALESCE(profit_loss_daily, 0) + CASE
            WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
            WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
            ELSE 0
          END,
          total_bet_amount = total_bet_amount + NEW.bet_amount::NUMERIC
        WHERE user_id = user_id_var
          AND game_type = NEW.game_type
        RETURNING total_games INTO user_games_after;
        
        -- If user went from 0 to 1 games for this game type, increment unique user count
        IF user_games_after = 1 THEN
          INSERT INTO admin_stats_overall (game_type, unique_users)
          VALUES (NEW.game_type, 1)
          ON CONFLICT (game_type) DO UPDATE SET 
            unique_users = admin_stats_overall.unique_users + 1,
            updated_at = NOW();
          
          -- Check if user went from 0 to 1 games across ALL game types
          SELECT COALESCE(SUM(total_games), 0) INTO user_total_games_after
          FROM leaderboard_records
          WHERE user_id = user_id_var;
          
          -- If user now has exactly 1 game across all types, increment overall unique user count
          IF user_total_games_after = 1 THEN
            INSERT INTO admin_stats_overall (game_type, unique_users)
            VALUES ('all', 1)
            ON CONFLICT (game_type) DO UPDATE SET 
              unique_users = admin_stats_overall.unique_users + 1,
              updated_at = NOW();
          END IF;
        END IF;
      END;

      -- Update admin_stats_overall for this game type
      INSERT INTO admin_stats_overall (game_type, total_games, lost_games, total_volume, total_payouts, treasury_delta, sum_win_multipliers, sum_row_reached)
      VALUES (
        NEW.game_type,
        1,
        CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        NEW.bet_amount::NUMERIC,
        CASE 
          WHEN NEW.status = 'won' AND NEW.net IS NOT NULL 
          THEN NEW.bet_amount::NUMERIC - NEW.net 
          ELSE 0 
        END,
        COALESCE(NEW.net, 0),
        CASE 
          WHEN NEW.status = 'won' AND NEW.final_multiplier IS NOT NULL 
          THEN NEW.final_multiplier 
          ELSE 0 
        END,
        COALESCE(NEW.current_row_index, 0)
      )
      ON CONFLICT (game_type) DO UPDATE SET
        total_games = admin_stats_overall.total_games + 1,
        lost_games = admin_stats_overall.lost_games + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_volume = admin_stats_overall.total_volume + NEW.bet_amount::NUMERIC,
        total_payouts = admin_stats_overall.total_payouts + CASE 
          WHEN NEW.status = 'won' AND NEW.net IS NOT NULL 
          THEN NEW.bet_amount::NUMERIC - NEW.net 
          ELSE 0 
        END,
        treasury_delta = admin_stats_overall.treasury_delta + COALESCE(NEW.net, 0),
        sum_win_multipliers = admin_stats_overall.sum_win_multipliers + CASE 
          WHEN NEW.status = 'won' AND NEW.final_multiplier IS NOT NULL 
          THEN NEW.final_multiplier 
          ELSE 0 
        END,
        sum_row_reached = admin_stats_overall.sum_row_reached + COALESCE(NEW.current_row_index, 0),
        updated_at = NOW();

      -- Update overall admin stats (game_type = 'all')
      INSERT INTO admin_stats_overall (game_type, total_games, lost_games, total_volume, total_payouts, treasury_delta, sum_win_multipliers, sum_row_reached)
      VALUES (
        'all',
        1,
        CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        NEW.bet_amount::NUMERIC,
        CASE 
          WHEN NEW.status = 'won' AND NEW.net IS NOT NULL 
          THEN NEW.bet_amount::NUMERIC - NEW.net 
          ELSE 0 
        END,
        COALESCE(NEW.net, 0),
        CASE 
          WHEN NEW.status = 'won' AND NEW.final_multiplier IS NOT NULL 
          THEN NEW.final_multiplier 
          ELSE 0 
        END,
        COALESCE(NEW.current_row_index, 0)
      )
      ON CONFLICT (game_type) DO UPDATE SET
        total_games = admin_stats_overall.total_games + 1,
        lost_games = admin_stats_overall.lost_games + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_volume = admin_stats_overall.total_volume + NEW.bet_amount::NUMERIC,
        total_payouts = admin_stats_overall.total_payouts + CASE 
          WHEN NEW.status = 'won' AND NEW.net IS NOT NULL 
          THEN NEW.bet_amount::NUMERIC - NEW.net 
          ELSE 0 
        END,
        treasury_delta = admin_stats_overall.treasury_delta + COALESCE(NEW.net, 0),
        sum_win_multipliers = admin_stats_overall.sum_win_multipliers + CASE 
          WHEN NEW.status = 'won' AND NEW.final_multiplier IS NOT NULL 
          THEN NEW.final_multiplier 
          ELSE 0 
        END,
        sum_row_reached = admin_stats_overall.sum_row_reached + COALESCE(NEW.current_row_index, 0),
        updated_at = NOW();
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION update_user_stats() IS 'Fixed admin statistics trigger function that correctly handles state transitions, prevents double counting, and ensures whole number calculations. Uses bet_amount - net for payout calculations to match actual payout system precision. Only updates stats when games transition to/from completed states (won/lost).';

-- Function to recalculate admin_stats_overall to fix incorrect values from buggy trigger
-- This uses the exact same backfill logic as the original migration but deletes existing data first
-- Uses bet_amount - net for payout calculations to ensure whole number results matching actual payouts
-- Can be called separately to avoid long-running migration transaction
CREATE OR REPLACE FUNCTION backfill_admin_stats_overall()
RETURNS void AS $$
DECLARE
    batch_size INT := 10000;
    total_processed INT := 0;
    batch_count INT := 0;
    temp_stats RECORD;
    game_type_list TEXT[];
    gt TEXT;
    migration_cutoff TIMESTAMP WITH TIME ZONE; -- Will be set after deletion
    -- Track backfill totals separately to avoid double-counting trigger data
    backfill_total_games INT := 0;
    backfill_total_volume NUMERIC := 0;
    backfill_total_payouts NUMERIC := 0;
    backfill_treasury_delta NUMERIC := 0;
    backfill_lost_games INT := 0;
    backfill_sum_win_multipliers NUMERIC := 0;
    backfill_sum_row_reached NUMERIC := 0;
BEGIN
    RAISE NOTICE 'Fixing admin_stats_overall by deleting and recalculating from games data...';
    
    -- Delete existing corrupted records to start fresh
    DELETE FROM admin_stats_overall;
    
    -- Capture cutoff time after deletion when clean backfill begins
    migration_cutoff := NOW();
    RAISE NOTICE 'Migration cutoff time (new function active): %', migration_cutoff;
    
     SELECT COALESCE(ARRAY_AGG(DISTINCT game_type), '{}'::TEXT[]) INTO game_type_list
    FROM games 
    WHERE status IN ('won', 'lost') AND game_type IS NOT NULL;
    
    RAISE NOTICE 'Starting batched backfill for admin_stats_overall';
    RAISE NOTICE 'Found game types: %', game_type_list;
    
    -- Process each game type separately to reduce memory usage
    FOREACH gt IN ARRAY game_type_list
    LOOP
        RAISE NOTICE 'Processing game type: %', gt;
        
        -- Initialize aggregation table for this game type (don't overwrite existing trigger data)
        INSERT INTO admin_stats_overall (
            game_type, 
            total_games, 
            total_volume, 
            total_payouts, 
            treasury_delta, 
            lost_games, 
            unique_users,
            sum_win_multipliers,
            sum_row_reached
        ) VALUES (gt, 0, 0, 0, 0, 0, 0, 0, 0)
        ON CONFLICT (game_type) DO NOTHING; -- Keep existing trigger data, don't reset
        
        -- Process games in batches for this game type using cursor-based pagination (O(n) instead of O(n²))
        DECLARE
            last_created_at TIMESTAMP WITH TIME ZONE := '1970-01-01 00:00:00+00'::TIMESTAMP WITH TIME ZONE;
            last_id UUID := '00000000-0000-0000-0000-000000000000'::UUID;
            current_batch INT := 0;
        BEGIN
            LOOP
                -- Process a batch of games for this game type using keyset pagination
                WITH batch_games AS (
                    SELECT *
                    FROM games g
                    WHERE g.game_type = gt 
                      AND g.status IN ('won', 'lost')
                      AND g.created_at < migration_cutoff
                      AND (g.created_at, g.id) > (last_created_at, last_id)
                    ORDER BY g.created_at, g.id
                    LIMIT batch_size
                ),
                batch_stats AS (
                    SELECT 
                        COUNT(*) as games_count,
                        COALESCE(SUM(bet_amount::NUMERIC), 0) as volume,
                        COALESCE(SUM(bet_amount::NUMERIC - net) FILTER (WHERE status = 'won' AND net IS NOT NULL), 0) as payouts,
                        COALESCE(SUM(net), 0) as net_delta,
                        COUNT(*) FILTER (WHERE status = 'lost') as lost_count,
                        COALESCE(SUM(final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier IS NOT NULL), 0) as sum_multipliers,
                        COALESCE(SUM(current_row_index), 0) as sum_rows,
                        (SELECT created_at FROM batch_games ORDER BY created_at DESC, id DESC LIMIT 1) as max_created_at,
                        (SELECT id FROM batch_games ORDER BY created_at DESC, id DESC LIMIT 1) as max_id
                    FROM batch_games g
                )
                SELECT * INTO temp_stats FROM batch_stats;
                
                -- Exit if no more records to process
                IF temp_stats.games_count = 0 THEN
                    EXIT;
                END IF;
                
                -- Update cursors for next iteration
                last_created_at := temp_stats.max_created_at;
                last_id := temp_stats.max_id;
                
                -- Update aggregated stats for this game type
                UPDATE admin_stats_overall SET
                    total_games = total_games + temp_stats.games_count,
                    total_volume = total_volume + temp_stats.volume,
                    total_payouts = total_payouts + temp_stats.payouts,
                    treasury_delta = treasury_delta + temp_stats.net_delta,
                    lost_games = lost_games + temp_stats.lost_count,
                    sum_win_multipliers = sum_win_multipliers + temp_stats.sum_multipliers,
                    sum_row_reached = sum_row_reached + temp_stats.sum_rows,
                    updated_at = NOW()
                WHERE game_type = gt;
                
                -- Accumulate backfill totals for 'all' calculation
                backfill_total_games := backfill_total_games + temp_stats.games_count;
                backfill_total_volume := backfill_total_volume + temp_stats.volume;
                backfill_total_payouts := backfill_total_payouts + temp_stats.payouts;
                backfill_treasury_delta := backfill_treasury_delta + temp_stats.net_delta;
                backfill_lost_games := backfill_lost_games + temp_stats.lost_count;
                backfill_sum_win_multipliers := backfill_sum_win_multipliers + temp_stats.sum_multipliers;
                backfill_sum_row_reached := backfill_sum_row_reached + temp_stats.sum_rows;
                
                total_processed := total_processed + temp_stats.games_count;
                current_batch := current_batch + 1;
                
                -- Progress logging every 10 batches
                IF current_batch % 10 = 0 THEN
                    RAISE NOTICE 'Game type %, batch %, processed % games total', gt, current_batch, total_processed;
                END IF;
                
                -- Small delay to reduce DB stress
                PERFORM pg_sleep(0.1);
            END LOOP;
        END;
        
        -- Skip unique users calculation during backfill (will recalculate at the end)
        
        RAISE NOTICE 'Completed game type: %', gt;
    END LOOP;
    
    -- Calculate overall stats (game_type = 'all') by aggregating all game type stats
    RAISE NOTICE 'Calculating overall stats by aggregating game type stats';
    
    -- Initialize 'all' game type record if it doesn't exist (don't overwrite trigger data)
    INSERT INTO admin_stats_overall (
        game_type, 
        total_games, 
        total_volume, 
        total_payouts, 
        treasury_delta, 
        lost_games, 
        unique_users,
        sum_win_multipliers,
        sum_row_reached
    ) VALUES ('all', 0, 0, 0, 0, 0, 0, 0, 0)
    ON CONFLICT (game_type) DO NOTHING; -- Keep existing trigger data
    
    -- Add only the backfill totals to 'all' record (avoid double-counting trigger data)
    UPDATE admin_stats_overall SET
        total_games = total_games + backfill_total_games,
        total_volume = total_volume + backfill_total_volume,
        total_payouts = total_payouts + backfill_total_payouts,
        treasury_delta = treasury_delta + backfill_treasury_delta,
        lost_games = lost_games + backfill_lost_games,
        sum_win_multipliers = sum_win_multipliers + backfill_sum_win_multipliers,
        sum_row_reached = sum_row_reached + backfill_sum_row_reached,
        updated_at = NOW()
    WHERE game_type = 'all';
    
    -- Calculate actual unique users across all game types (to avoid double counting)
    WITH unique_user_count AS (
        SELECT COUNT(DISTINCT user_id) as user_count
        FROM leaderboard_records lr
    )
    UPDATE admin_stats_overall SET
        unique_users = (SELECT user_count FROM unique_user_count),
        updated_at = NOW()
    WHERE game_type = 'all';

    -- Recalculate all unique users at the end (avoids trigger overhead and race conditions)
    RAISE NOTICE 'Recalculating unique users for all game types';
    
    -- Update unique users for each individual game type
    UPDATE admin_stats_overall SET
        unique_users = (
            SELECT COUNT(DISTINCT user_id)
            FROM leaderboard_records lr 
            WHERE lr.game_type = admin_stats_overall.game_type
        ),
        updated_at = NOW()
    WHERE game_type != 'all';
    
    -- Update unique users for 'all' game types (avoid double counting)
    UPDATE admin_stats_overall SET
        unique_users = (
            SELECT COUNT(DISTINCT user_id)
            FROM leaderboard_records lr
        ),
        updated_at = NOW()
    WHERE game_type = 'all';

    RAISE NOTICE 'Completed batched backfill for admin_stats_overall. Total games processed: %', total_processed;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION backfill_admin_stats_overall() IS 'Recalculates admin_stats_overall from scratch by processing games in batches. Deletes existing data and rebuilds it using exact migration logic. Can be called separately to avoid long-running migration transactions.';