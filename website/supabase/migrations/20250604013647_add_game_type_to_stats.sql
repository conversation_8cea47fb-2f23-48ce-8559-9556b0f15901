-- Update get_user_stats function to accept optional game_type parameter
DROP FUNCTION IF EXISTS get_user_stats(TEXT);
CREATE OR REPLACE FUNCTION get_user_stats(wallet TEXT, game_type TEXT DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  result json;
BEGIN
  -- Single optimized query combining all stats
  WITH game_stats AS (
    SELECT 
        COUNT(*) FILTER (WHERE status IN ('won', 'lost')) as total_games,
        COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0)::TEXT as total_bet_amount,
        COALESCE(ROUND(SUM(bet_amount::NUMERIC * final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier > 0)), 0)::TEXT as total_won_amount
    FROM games g
    WHERE g.wallet_address = get_user_stats.wallet
    AND (get_user_stats.game_type IS NULL OR g.game_type = get_user_stats.game_type)
  ), 
  user_stats AS (
    SELECT 
        total_profit_loss,
        profit_loss_7d,
        profit_loss_30d
    FROM users
    WHERE wallet_address = get_user_stats.wallet
  )
  SELECT json_build_object(
    'total_games', gs.total_games,
    'total_bet_amount', gs.total_bet_amount,
    'total_won_amount', gs.total_won_amount,
    'total_profit_loss', us.total_profit_loss,
    'profit_loss_7d', us.profit_loss_7d,
    'profit_loss_30d', us.profit_loss_30d
  ) INTO result
  FROM game_stats gs
  CROSS JOIN user_stats us;
  
  -- Return as JSON
  RETURN result;
END;
$$;

DROP FUNCTION IF EXISTS get_admin_stats();
-- Update get_admin_stats function to accept optional game_type parameter
CREATE OR REPLACE FUNCTION get_admin_stats(game_type TEXT DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    stats json;
BEGIN
    -- Single query to get all game-related stats
    WITH game_stats AS (
        SELECT 
            COUNT(*) as total_games,
            COUNT(*) FILTER (WHERE status = 'lost') as lost_games,
            COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0) as total_volume,
            COALESCE(AVG(final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier > 0), 0) as avg_win_multiplier,
            COALESCE(AVG(current_row_index) FILTER (WHERE status IN ('won', 'lost')), 0) as avg_row_reached,
            COALESCE(SUM(net), 0) as treasury_delta,
            COALESCE(ROUND(SUM(bet_amount::NUMERIC * final_multiplier) FILTER (WHERE status = 'won' AND final_multiplier IS NOT NULL)), 0) as total_payouts
        FROM games
        WHERE (get_admin_stats.game_type IS NULL OR games.game_type = get_admin_stats.game_type)
    ),
    user_count AS (
        SELECT COUNT(*) as total_users
        FROM users
    )
    SELECT json_build_object(
        'totalGames', gs.total_games,
        'totalVolume', gs.total_volume::TEXT,
        'totalPayouts', gs.total_payouts::TEXT,
        'totalUsers', uc.total_users,
        'treasuryDelta', gs.treasury_delta::TEXT,
        'bustPercentage', ROUND((gs.lost_games::DECIMAL / NULLIF(gs.total_games, 0) * 100)::NUMERIC, 1),
        'averageWinMultiplier', ROUND(gs.avg_win_multiplier::NUMERIC, 2),
        'averageRowReached', ROUND(gs.avg_row_reached::NUMERIC, 1)
    ) INTO stats
    FROM game_stats gs
    CROSS JOIN user_count uc;

    RETURN stats;
END;
$$;

-- Add index on game_type column for performance
CREATE INDEX IF NOT EXISTS idx_games_wallet_game_type_status ON games(wallet_address, game_type, status);

-- Add comments explaining the updated functions
COMMENT ON FUNCTION get_user_stats(TEXT, TEXT) IS 'Returns user statistics filtered by game type. Pass NULL for game_type to get stats for all game types.';
COMMENT ON FUNCTION get_admin_stats(TEXT) IS 'Returns admin statistics filtered by game type. Pass NULL for game_type to get stats for all game types.';
