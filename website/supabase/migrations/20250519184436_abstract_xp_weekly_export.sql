-- Create a function to get weekly game stats
CREATE OR R<PERSON>LACE FUNCTION get_weekly_game_stats(days_ago integer)
RETURNS TABLE (
    wallet_address text,
    transactions bigint,
    total_eth_bet numeric
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.wallet_address,
        COUNT(*)::bigint as transactions,
        (SUM(CAST(g.bet_amount AS numeric)) / 1e18) as total_eth_bet
    FROM games g
    WHERE g.created_at >= NOW() - (days_ago || ' days')::interval
    GROUP BY g.wallet_address
    ORDER BY total_eth_bet DESC;
END;
$$;
