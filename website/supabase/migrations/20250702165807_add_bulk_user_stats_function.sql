-- Create a bulk user stats function that accepts an array of user IDs and returns an array of stats
-- This function provides efficient batch retrieval of user statistics for multiple users

CREATE OR REPLACE FUNCTION public.get_bulk_user_stats(
    p_user_ids UUID[],
    p_game_type TEXT DEFAULT NULL
)
RETURNS json AS $$
BEGIN
    -- Handle null or empty input array
    IF p_user_ids IS NULL OR array_length(p_user_ids, 1) IS NULL THEN
        RETURN json_build_array();
    END IF;
    
    -- Single set-based query that handles all users at once
    -- When p_game_type IS NULL: aggregate across all game types (SUM)
    -- When p_game_type is specified: get single record per user (no SUM needed)
    RETURN (
        CASE 
            WHEN p_game_type IS NULL THEN
                -- Aggregate across all game types - includes all requested users even if no records
                (SELECT jsonb_agg(user_stats)
                 FROM (
                     SELECT jsonb_build_object(
                         'user_id', u.user_id::text,
                         'total_games', COALESCE(SUM(lr.total_games), 0),
                         'total_bet_amount', ROUND(COALESCE(SUM(lr.total_bet_amount), 0))::text,
                         'total_profit_loss', ROUND(COALESCE(SUM(lr.total_profit_loss), 0))::text
                     ) AS user_stats
                     FROM unnest(p_user_ids) AS u(user_id)
                     LEFT JOIN leaderboard_records lr ON lr.user_id = u.user_id
                     GROUP BY u.user_id
                 ) aggregated_stats)
            ELSE
                -- Get specific game type - includes all requested users even if no records
                (SELECT jsonb_agg(user_stats)
                 FROM (
                     SELECT jsonb_build_object(
                         'user_id', u.user_id::text,
                         'total_games', COALESCE(lr.total_games, 0),
                         'total_bet_amount', ROUND(COALESCE(lr.total_bet_amount, 0))::text,
                         'total_profit_loss', ROUND(COALESCE(lr.total_profit_loss, 0))::text
                     ) AS user_stats
                     FROM unnest(p_user_ids) AS u(user_id)
                     LEFT JOIN leaderboard_records lr ON lr.user_id = u.user_id AND lr.game_type = p_game_type
                     GROUP BY u.user_id, lr.total_games, lr.total_bet_amount, lr.total_profit_loss
                 ) specific_stats)
        END
    );
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION public.get_bulk_user_stats IS 'Efficiently retrieves user statistics for multiple users in a single call. Accepts an array of user IDs and optional game type filter. Returns JSON array with stats for each user.';