-- Add cascade delete for referral_bonuses when games are deleted
-- This ensures referral bonuses are automatically cleaned up when their associated game is deleted

-- Drop the existing foreign key constraints (removing redundant duplicate)
ALTER TABLE referral_bonuses 
  DROP CONSTRAINT IF EXISTS fk_game,
  DROP CONSTRAINT IF EXISTS referral_bonuses_game_id_fkey;

-- Add back only the more explicit foreign key constraint with ON DELETE CASCADE
ALTER TABLE referral_bonuses 
  ADD CONSTRAINT referral_bonuses_game_id_fkey 
  FOREIGN KEY (game_id) 
  REFERENCES games (id) 
  ON DELETE CASCADE;

-- Add comment for documentation
COMMENT ON CONSTRAINT referral_bonuses_game_id_fkey ON referral_bonuses IS 'Foreign key to games table with cascade delete - referral bonuses are automatically deleted when their associated game is deleted';
