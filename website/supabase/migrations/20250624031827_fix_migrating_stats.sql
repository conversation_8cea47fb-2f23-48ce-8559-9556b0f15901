-- adjust the update_user_stats function to sync to leaderboard_records
DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
DROP FUNCTION IF EXISTS update_user_stats();

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
    WHERE id = NEW.id;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::NUMERIC
    WHERE id = NEW.id;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    --- migrate old stats or create new default record only if record doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM leaderboard_records 
        WHERE user_id = (SELECT id FROM users WHERE wallet_address = OLD.wallet_address)
        AND game_type = OLD.game_type
    ) THEN
        INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily, total_bet_amount)
        WITH user_stats AS (
            SELECT 
                COUNT(*) FILTER (WHERE status IN ('won', 'lost')) as total_games,
                COUNT(*) FILTER (WHERE status = 'won') as games_won,
                COUNT(*) FILTER (WHERE status = 'lost') as games_lost,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as total_profit_loss,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' AND is_in_current_day(updated_at) THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' AND is_in_current_day(updated_at) THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as profit_loss_daily,
                COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0)::NUMERIC as total_bet_amount
            FROM games 
            WHERE wallet_address = OLD.wallet_address 
            AND game_type = OLD.game_type
        )
        SELECT 
            (SELECT id FROM users WHERE wallet_address = OLD.wallet_address),
            OLD.game_type,
            user_stats.total_games,
            user_stats.games_won,
            user_stats.games_lost,
            user_stats.total_profit_loss,
            user_stats.profit_loss_daily,
            user_stats.total_bet_amount
        FROM user_stats;
    END IF;

    --- Update stats for corrections
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily - CASE
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        total_bet_amount = total_bet_amount - OLD.bet_amount::NUMERIC
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = OLD.wallet_address)
        AND game_type = OLD.game_type;
    END IF;


    --- Update stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = COALESCE(total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END, 0),
        profit_loss_daily = COALESCE(profit_loss_daily, 0) + CASE
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END,
        total_bet_amount = total_bet_amount + NEW.bet_amount::NUMERIC
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = NEW.wallet_address)
        AND game_type = NEW.game_type;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats();


-- Delete incorrect laser_party records from original backfill
DELETE FROM leaderboard_records WHERE game_type = 'laser_party';

-- backfill leaderboard_records in batches (concurrency safe)
DO $$
DECLARE
    batch_size INT := 1000;
    rows_processed INT;
BEGIN
    LOOP
        WITH batch AS (
            SELECT DISTINCT u.id as user_id, g.game_type
            FROM users u
            JOIN games g ON g.wallet_address = u.wallet_address
            WHERE g.game_type IN ('death_race', 'laser_party')
            AND g.status IN ('won', 'lost')
            AND NOT EXISTS (
                SELECT 1 FROM leaderboard_records lr 
                WHERE lr.user_id = u.id 
                AND lr.game_type = g.game_type
            )
            ORDER BY u.id, g.game_type
            LIMIT batch_size
        )
        INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily, total_bet_amount)
        SELECT 
            b.user_id,
            b.game_type,
            user_stats.total_games,
            user_stats.games_won,
            user_stats.games_lost,
            user_stats.total_profit_loss,
            user_stats.profit_loss_daily,
            user_stats.total_bet_amount
        FROM batch b
        JOIN users u ON u.id = b.user_id
        CROSS JOIN LATERAL (
            SELECT 
                COUNT(*) FILTER (WHERE status IN ('won', 'lost')) as total_games,
                COUNT(*) FILTER (WHERE status = 'won') as games_won,
                COUNT(*) FILTER (WHERE status = 'lost') as games_lost,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as total_profit_loss,
                COALESCE(SUM(
                    CASE 
                        WHEN status = 'won' AND is_in_current_day(updated_at) THEN final_multiplier * bet_amount::NUMERIC - bet_amount::NUMERIC
                        WHEN status = 'lost' AND is_in_current_day(updated_at) THEN -bet_amount::NUMERIC
                        ELSE 0
                    END
                ), 0)::NUMERIC as profit_loss_daily,
                COALESCE(SUM(bet_amount::NUMERIC) FILTER (WHERE status IN ('won', 'lost')), 0)::NUMERIC as total_bet_amount
            FROM games 
            WHERE wallet_address = u.wallet_address 
            AND game_type = b.game_type
        ) user_stats
        ON CONFLICT (user_id, game_type) DO NOTHING;

        GET DIAGNOSTICS rows_processed = ROW_COUNT;
        EXIT WHEN rows_processed = 0;
        
        -- Small delay to reduce load
        PERFORM pg_sleep(0.1);
    END LOOP;
END $$;
