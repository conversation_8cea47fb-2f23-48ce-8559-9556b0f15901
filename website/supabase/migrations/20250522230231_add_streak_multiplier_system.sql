-- Add streak and multiplier columns to users table for the streak system
-- Streak tracks consecutive days of playing (max 30 days)
-- Multiplier is calculated based on streak and applied to points earned
-- Both reset if user doesn't play for 24+ hours (Eastern Time)

-- Add new columns to users table
ALTER TABLE users ADD COLUMN streak integer NOT NULL DEFAULT 0;
ALTER TABLE users ADD COLUMN multiplier numeric NOT NULL DEFAULT 1.00;
ALTER TABLE users ADD COLUMN last_game_date date NULL;

-- <PERSON>reate function to calculate multiplier based on streak
CREATE OR REPLACE FUNCTION calculate_points_multiplier(streak_days integer)
RETURNS numeric(4,2)
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  -- Base multiplier is 1.0, add 0.02 per day up to 30 days max
  -- Max multiplier: 1.0 + (30 * 0.02) = 1.6
  RETURN LEAST(1.00 + (streak_days * 0.02), 1.60);
END;
$$;

-- Create function to update user streak when a game is started
CREATE OR REPLACE FUNCTION update_user_streak(
  p_wallet_address TEXT
) RETURNS TABLE(
  new_streak integer,
  new_multiplier numeric(4,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_streak integer;
  v_last_game_date date;
  v_today_et date;
  v_yesterday_et date;
  v_new_streak integer;
  v_new_multiplier numeric(4,2);
BEGIN
  -- Get current date in Eastern Time
  v_today_et := (NOW() AT TIME ZONE 'America/New_York')::date;
  v_yesterday_et := v_today_et - INTERVAL '1 day';
  
  -- Get current user streak data
  SELECT streak, last_game_date
  INTO v_current_streak, v_last_game_date
  FROM users
  WHERE wallet_address = p_wallet_address;
  
  -- If no user found, return defaults
  IF NOT FOUND THEN
    RETURN QUERY SELECT 0, 1.00::numeric(4,2);
    RETURN;
  END IF;
  
  -- Determine new streak based on last game date
  IF v_last_game_date IS NULL THEN
    -- First game ever
    v_new_streak := 1;
  ELSIF v_last_game_date = v_today_et THEN
    -- Already played today, no change to streak
    v_new_streak := v_current_streak;
  ELSIF v_last_game_date = v_yesterday_et THEN
    -- Played yesterday, increment streak (max 30)
    v_new_streak := LEAST(v_current_streak + 1, 30);
  ELSE
    -- Missed a day, reset streak
    v_new_streak := 1;
  END IF;
  
  -- Calculate new multiplier
  v_new_multiplier := calculate_points_multiplier(v_new_streak);
  
  -- Update user record
  UPDATE users
  SET 
    streak = v_new_streak,
    multiplier = v_new_multiplier,
    last_game_date = v_today_et
  WHERE wallet_address = p_wallet_address;
  
  -- Return new values
  RETURN QUERY SELECT v_new_streak, v_new_multiplier;
END;
$$;

-- Create function to increment user points with multiplier applied
CREATE OR REPLACE FUNCTION increment_user_points_with_multiplier(
  p_wallet_address TEXT,
  p_base_points NUMERIC
) RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_multiplier NUMERIC(4,2);
  v_final_points NUMERIC;
  v_new_total_points NUMERIC;
BEGIN
  -- Get current multiplier
  SELECT multiplier INTO v_multiplier
  FROM users
  WHERE wallet_address = p_wallet_address;
  
  -- If no user found, return 0
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Calculate final points with multiplier applied
  v_final_points := ROUND(p_base_points * v_multiplier, 2);
  
  -- Update user points
  UPDATE users
  SET points = COALESCE(points, 0) + v_final_points
  WHERE wallet_address = p_wallet_address
  RETURNING points INTO v_new_total_points;
  
  RETURN v_new_total_points;
END;
$$;