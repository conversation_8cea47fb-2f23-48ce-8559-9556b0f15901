CREATE TABLE IF NOT EXISTS leaderboard_records (
    user_id UUID REFERENCES users(id),
    game_type TEXT NOT NULL,
    total_games INT,
    games_won INT,
    games_lost INT,
    total_profit_loss NUMERIC,
    profit_loss_daily NUMERIC
);
CREATE UNIQUE INDEX IF NOT EXISTS idx_leaderboard_records_user_id_game_type ON leaderboard_records(user_id, game_type);

-- adjust the update_user_stats function to sync to leaderboard_records
DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
DROP FUNCTION IF EXISTS update_user_stats();

CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate net amount for Treasury when game status changes to won or lost
  IF (NEW.status = 'won' AND OLD.status != 'won') THEN
    -- If player won, Treasury loses: bet_amount * (final_multiplier - 1) as negative number
    UPDATE games
    SET net = -1 * (bet_amount::NUMERIC * (final_multiplier - 1))::NUMERIC
    WHERE id = NEW.id;
  ELSIF (NEW.status = 'lost' AND OLD.status != 'lost') THEN
    -- If player lost, Treasury gains the bet_amount as positive number
    UPDATE games
    SET net = bet_amount::NUMERIC
    WHERE id = NEW.id;
  END IF;

  -- If the game status changed to or from won/lost
  IF (NEW.status IN ('won', 'lost') AND OLD.status NOT IN ('won', 'lost')) OR
     (OLD.status IN ('won', 'lost') AND NEW.status NOT IN ('won', 'lost')) OR
     (NEW.status IN ('won', 'lost') AND OLD.status IN ('won', 'lost') AND NEW.status != OLD.status) THEN

    --- migrate old stats or create new default record
    INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily)
    SELECT 
        id,
        OLD.game_type,
        total_games,
        games_won,
        games_lost,
        total_profit_loss,
        profit_loss_daily
    FROM users
    WHERE wallet_address = OLD.wallet_address
    ON CONFLICT (user_id, game_type) DO NOTHING;

    --- Update stats for corrections
    IF OLD.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE
          WHEN OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END,
        profit_loss_daily = profit_loss_daily - CASE
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'won' THEN OLD.final_multiplier * OLD.bet_amount::NUMERIC - OLD.bet_amount::NUMERIC
          WHEN is_in_current_day(OLD.updated_at) AND OLD.status = 'lost' THEN -OLD.bet_amount::NUMERIC
          ELSE 0
        END
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = OLD.wallet_address)
        AND game_type = OLD.game_type;
    END IF;


    --- Update stats for new status
    IF NEW.status IN ('won', 'lost') THEN
      UPDATE leaderboard_records
      SET
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'won' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = COALESCE(total_profit_loss + CASE
          WHEN NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END, 0),
        profit_loss_daily = profit_loss_daily + CASE
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'won' THEN NEW.final_multiplier * NEW.bet_amount::NUMERIC - NEW.bet_amount::NUMERIC
          WHEN is_in_current_day(NEW.updated_at) AND NEW.status = 'lost' THEN -NEW.bet_amount::NUMERIC
          ELSE 0
        END
      WHERE user_id = (SELECT id FROM users WHERE wallet_address = NEW.wallet_address)
        AND game_type = NEW.game_type;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Re-create the trigger
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats();


-- backfill leaderboard_records in batches
DO $$
DECLARE
    batch_size INT := 1000;
    offset_val INT := 0;
    rows_processed INT;
BEGIN
    LOOP
        INSERT INTO leaderboard_records (user_id, game_type, total_games, games_won, games_lost, total_profit_loss, profit_loss_daily)
        SELECT 
          id,
          'death_race',
          total_games,
          games_won,
          games_lost,
          total_profit_loss,
          profit_loss_daily
        FROM users 
        ORDER BY id 
        LIMIT batch_size OFFSET offset_val
        ON CONFLICT (user_id, game_type) DO NOTHING;
        
        GET DIAGNOSTICS rows_processed = ROW_COUNT;
        EXIT WHEN rows_processed = 0;
        
        offset_val := offset_val + batch_size;
        
        -- Optional: Add small delay to reduce load
        PERFORM pg_sleep(0.1);
    END LOOP;
END $$;


-- adjust the distribute_and_reset_daily_leaderboard function to sync to leaderboard_records
DROP FUNCTION IF EXISTS distribute_and_reset_daily_leaderboard(TEXT[], INT[]);

-- Distribute points to top N users and reset daily leaderboard in a single transaction
CREATE OR REPLACE FUNCTION distribute_and_reset_daily_leaderboard(wallet_addresses TEXT[], points_to_add INT[], game_type TEXT)
RETURNS void AS $$
DECLARE
  i integer;
  wallet_count integer;
  points_count integer;
  updated_rows integer;
  successful_updates integer := 0;
  skipped_updates integer := 0;
BEGIN
  -- Validate input arrays
  wallet_count := array_length(wallet_addresses, 1);
  points_count := array_length(points_to_add, 1);
  
  -- Check for null or empty arrays - if so, just do the reset
  IF wallet_count IS NULL OR points_count IS NULL THEN
    RAISE NOTICE 'Input arrays are null or empty, skipping point distribution';
    -- Still do the daily reset
    UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = distribute_and_reset_daily_leaderboard.game_type;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % leaderboard records (game_type: %)', updated_rows, game_type;
    RETURN;
  END IF;
  
  -- Check that arrays have the same length - if not, skip point distribution
  IF wallet_count != points_count THEN
    RAISE NOTICE 'Array length mismatch (wallets: %, points: %), skipping point distribution', wallet_count, points_count;
    -- Still do the daily reset
    UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = distribute_and_reset_daily_leaderboard.game_type;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RAISE NOTICE 'Reset profit_loss_daily for % leaderboard records (game_type: %)', updated_rows, game_type;
    RETURN;
  END IF;

  -- Update each winner's points (skip problematic ones, continue with others)
  FOR i IN 1..wallet_count LOOP
    BEGIN
      -- Skip negative points but log it
      IF points_to_add[i] < 0 THEN
        RAISE NOTICE 'Skipping negative points (%) for wallet %', points_to_add[i], wallet_addresses[i];
        skipped_updates := skipped_updates + 1;
        CONTINUE;
      END IF;
      
      UPDATE users
      SET points = points + points_to_add[i]
      WHERE wallet_address = wallet_addresses[i];
      
      -- Check if the update succeeded
      GET DIAGNOSTICS updated_rows = ROW_COUNT;
      IF updated_rows = 1 THEN
        successful_updates := successful_updates + 1;
      ELSE
        RAISE NOTICE 'Wallet address not found, skipping: %', wallet_addresses[i];
        skipped_updates := skipped_updates + 1;
      END IF;
      
    EXCEPTION
      WHEN OTHERS THEN
        -- Log the error but continue processing
        RAISE NOTICE 'Error updating wallet %, skipping: %', wallet_addresses[i], SQLERRM;
        skipped_updates := skipped_updates + 1;
    END;
  END LOOP;

  -- Always do the daily reset regardless of point distribution success
  UPDATE leaderboard_records SET profit_loss_daily = 0 WHERE profit_loss_daily != 0 AND game_type = distribute_and_reset_daily_leaderboard.game_type;
  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  
  -- Log final results
  RAISE NOTICE 'Point distribution complete: % successful, % skipped. Reset profit_loss_daily for % leaderboard records (game_type: %)', 
    successful_updates, skipped_updates, updated_rows, game_type;
END;
$$ LANGUAGE plpgsql; 