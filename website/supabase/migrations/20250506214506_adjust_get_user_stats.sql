CREATE OR REPLACE FUNCTION get_user_stats(wallet TEXT)
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  total_games INT;
  total_bet_amount BIGINT;
  total_won_amount BIGINT;
  total_profit_loss BIGINT;
  profit_loss_7d BIGINT;
  profit_loss_30d BIGINT;
BEGIN
  -- Get total number of completed games
  SELECT COUNT(*) INTO total_games
  FROM games
  WHERE wallet_address = wallet
  AND status IN ('won', 'lost');
  
  -- Get total bet amount from all completed games
  SELECT COALESCE(SUM(bet_amount::BIGINT), 0) INTO total_bet_amount
  FROM games
  WHERE wallet_address = wallet
  AND status IN ('won', 'lost');
  
  -- Get total won amount (from completed games with positive multiplier)
  SELECT COALESCE(SUM(bet_amount::BIGINT * final_multiplier), 0) INTO total_won_amount
  FROM games
  WHERE wallet_address = wallet
  AND status = 'won'
  AND final_multiplier > 0;
  
  -- Get profit/loss values from users table
  SELECT u.total_profit_loss, u.profit_loss_7d, u.profit_loss_30d 
  INTO total_profit_loss, profit_loss_7d, profit_loss_30d
  FROM users u
  WHERE u.wallet_address = wallet;
  
  -- Return as JSON
  RETURN json_build_object(
    'total_games', total_games,
    'total_bet_amount', total_bet_amount,
    'total_won_amount', total_won_amount,
    'total_profit_loss', total_profit_loss,
    'profit_loss_7d', profit_loss_7d,
    'profit_loss_30d', profit_loss_30d
  );
END;
$$;