DROP FUNCTION IF EXISTS get_challenges_with_user_status(user_id uuid, mode TEXT);
DROP FUNCTION IF EXISTS process_challenge(challenge_id INTEGER, user_id uuid);
DROP TABLE IF EXISTS challenge_submissions;
DROP TABLE IF EXISTS challenges;

CREATE TABLE IF NOT EXISTS challenges (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  link TEXT NOT NULL,
  type TEXT NOT NULL,
  points INTEGER NOT NULL,
  published BOOLEAN NOT NULL DEFAULT FALSE,
  icon TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS challenge_submissions (
  challenge_id INTEGER NOT NULL,
  user_id uuid NOT NULL,
  status TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE,
  constraint fk_challenge foreign key (challenge_id) references challenges(id),
  constraint fk_user foreign key (user_id) references users(id),
  constraint challenge_submissions_pkey primary key (challenge_id, user_id)
);


CREATE OR REPLACE FUNCTION process_challenge(challenge_id INTEGER, user_id uuid)
RETURNS void AS $$
DECLARE
  challenge_points INTEGER;
BEGIN
  -- Get challenge points
  SELECT points INTO challenge_points
  FROM challenges
  WHERE id = process_challenge.challenge_id;

  -- Update challenge submission
  UPDATE challenge_submissions
  SET status = 'completed'
  WHERE challenge_submissions.challenge_id = process_challenge.challenge_id
  AND challenge_submissions.user_id = process_challenge.user_id;

  -- Update user points
  UPDATE users
  SET points = COALESCE(points, 0) + challenge_points
  WHERE id = process_challenge.user_id;

EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$ LANGUAGE plpgsql;
