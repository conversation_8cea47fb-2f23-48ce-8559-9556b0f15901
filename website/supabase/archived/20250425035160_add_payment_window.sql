-- Add payment window support and update game statuses

-- Add payment_expires_at column to track payment window
ALTER TABLE games ADD COLUMN IF NOT EXISTS payment_expires_at TIMESTAMPTZ;

-- Update status enum to include new statuses
ALTER TABLE games 
  DROP CONSTRAINT IF EXISTS games_status_check,
  ADD CONSTRAINT games_status_check 
  CHECK (status IN (
    'pending_payment',  -- Initial state when game is created but payment not confirmed
    'payment_failed',   -- Payment verification failed or expired
    'active',          -- Payment confirmed, game in progress
    'won',             -- Player won (cashed out)
    'lost',            -- Player lost (hit death tile)
    'payout_pending',  -- Waiting for payout transaction
    'complete',        -- Game completed and payout sent
    'error'           -- Error state requiring manual intervention
  ));

-- Add unique constraint on transaction_signature
ALTER TABLE games 
  ADD CONSTRAINT unique_transaction_signature 
  UNIQUE (transaction_signature);

-- Add index for pending payments
CREATE INDEX IF NOT EXISTS idx_games_pending_payments 
  ON games(wallet_address, status, payment_expires_at) 
  WHERE status = 'pending_payment';

-- Add function to clean up expired pending payments
CREATE OR REPLACE FUNCTION cleanup_expired_payments()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE games
  SET 
    status = 'payment_failed',
    error_reason = 'Payment window expired'
  WHERE 
    status = 'pending_payment'
    AND payment_expires_at < NOW();
END;
$$;

-- Add comment for documentation
COMMENT ON COLUMN games.payment_expires_at IS 'Timestamp when the payment window expires for pending_payment games'; 