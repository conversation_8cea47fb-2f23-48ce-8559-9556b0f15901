-- Drop existing function first
DROP FUNCTION IF EXISTS get_leaderboard(TEXT, TEXT);

-- Create function to get leaderboard data
CREATE OR REPLACE FUNCTION get_leaderboard(
  time_filter TEXT,
  current_wallet TEXT
)
RETURNS TABLE (
  wallet_address TEXT,
  profit_loss BIGINT,
  rank BIGINT,
  is_current_user BOOLEAN
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH user_stats AS (
    SELECT 
      u.wallet_address,
      CASE time_filter
        WHEN '7d' THEN u.profit_loss_7d
        WHEN '30d' THEN u.profit_loss_30d
        ELSE u.total_profit_loss
      END as profit_loss
    FROM users u
    WHERE CASE time_filter
      WHEN '7d' THEN u.profit_loss_7d != 0
      WHEN '30d' THEN u.profit_loss_30d != 0
      ELSE u.total_profit_loss != 0
    END
  ),
  ranked_stats AS (
    SELECT 
      us.wallet_address,
      us.profit_loss,
      ROW_NUMBER() OVER (ORDER BY us.profit_loss DESC)::BIGINT as rank
    FROM user_stats us
  )
  SELECT 
    rs.wallet_address,
    rs.profit_loss,
    rs.rank,
    rs.wallet_address = current_wallet as is_current_user
  FROM ranked_stats rs
  ORDER BY rs.profit_loss DESC
  LIMIT 50;
END;
$$; 