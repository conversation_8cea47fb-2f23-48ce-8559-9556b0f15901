-- Add provably fair verification to games table
-- Migration 03: Add hash-based commitment scheme for game verification

-- Add verification fields to the games table
ALTER TABLE games ADD COLUMN IF NOT EXISTS game_seed TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS commitment_hash TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE;

-- Create index for finding games that need verification
CREATE INDEX IF NOT EXISTS idx_games_verification_status ON games(id, status, is_verified) 
WHERE status IN ('won', 'lost') AND is_verified = FALSE;

COMMENT ON COLUMN games.game_seed IS 'Random seed used for game verification (revealed after game ends)';
COMMENT ON COLUMN games.commitment_hash IS 'SHA-256 hash of the game state + seed for provable fairness';
COMMENT ON COLUMN games.is_verified IS 'Whether the game has been verified by revealing the seed'; 