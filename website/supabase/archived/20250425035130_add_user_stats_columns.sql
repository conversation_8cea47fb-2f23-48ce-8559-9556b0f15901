-- Add user stats columns
ALTER TABLE users
ADD COLUMN IF NOT EXISTS total_profit_loss BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS profit_loss_7d BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS profit_loss_30d BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_games INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS games_won INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS games_lost INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();

-- Create function to update user stats
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- If the game status changed to or from complete/lost
  IF (NEW.status IN ('complete', 'lost') AND OLD.status NOT IN ('complete', 'lost')) OR
     (OLD.status IN ('complete', 'lost') AND NEW.status NOT IN ('complete', 'lost')) OR
     (NEW.status IN ('complete', 'lost') AND OLD.status IN ('complete', 'lost') AND NEW.status != OLD.status) THEN
    
    -- First, subtract the old game's contribution if it was complete/lost
    IF OLD.status IN ('complete', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games - 1,
        games_won = games_won - CASE WHEN OLD.status = 'complete' THEN 1 ELSE 0 END,
        games_lost = games_lost - CASE WHEN OLD.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss - CASE 
          WHEN OLD.status = 'complete' THEN OLD.final_multiplier * OLD.bet_amount - OLD.bet_amount
          ELSE -OLD.bet_amount
        END,
        profit_loss_7d = profit_loss_7d - CASE 
          WHEN OLD.status = 'complete' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN OLD.final_multiplier * OLD.bet_amount - OLD.bet_amount
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '7 days'
          THEN -OLD.bet_amount
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d - CASE 
          WHEN OLD.status = 'complete' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN OLD.final_multiplier * OLD.bet_amount - OLD.bet_amount
          WHEN OLD.status = 'lost' AND OLD.updated_at >= NOW() - INTERVAL '30 days'
          THEN -OLD.bet_amount
          ELSE 0
        END
      WHERE wallet_address = OLD.wallet_address;
    END IF;

    -- Then, add the new game's contribution if it's complete/lost
    IF NEW.status IN ('complete', 'lost') THEN
      UPDATE users
      SET 
        total_games = total_games + 1,
        games_won = games_won + CASE WHEN NEW.status = 'complete' THEN 1 ELSE 0 END,
        games_lost = games_lost + CASE WHEN NEW.status = 'lost' THEN 1 ELSE 0 END,
        total_profit_loss = total_profit_loss + CASE 
          WHEN NEW.status = 'complete' THEN NEW.final_multiplier * NEW.bet_amount - NEW.bet_amount
          ELSE -NEW.bet_amount
        END,
        profit_loss_7d = profit_loss_7d + CASE 
          WHEN NEW.status = 'complete' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN NEW.final_multiplier * NEW.bet_amount - NEW.bet_amount
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '7 days'
          THEN -NEW.bet_amount
          ELSE 0
        END,
        profit_loss_30d = profit_loss_30d + CASE 
          WHEN NEW.status = 'complete' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN NEW.final_multiplier * NEW.bet_amount - NEW.bet_amount
          WHEN NEW.status = 'lost' AND NEW.updated_at >= NOW() - INTERVAL '30 days'
          THEN -NEW.bet_amount
          ELSE 0
        END,
        last_updated = NOW()
      WHERE wallet_address = NEW.wallet_address;
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user stats
DROP TRIGGER IF EXISTS update_user_stats_trigger ON games;
CREATE TRIGGER update_user_stats_trigger
  AFTER UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats(); 