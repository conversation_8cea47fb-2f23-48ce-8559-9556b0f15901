-- Add additional fields for Solana integration if needed
-- Note: transaction_signature field already exists in the games table

-- Add index for faster transaction signature lookups
CREATE INDEX IF NOT EXISTS idx_games_transaction_signature ON games(transaction_signature);

-- Add index for faster payout transaction signature lookups
CREATE INDEX IF NOT EXISTS idx_games_payout_tx_signature ON games(payout_tx_signature);

-- Add index for faster transaction lookups by signature
CREATE INDEX IF NOT EXISTS idx_transactions_tx_signature ON transactions(tx_signature);

-- Add a function to verify a transaction is unique
CREATE OR REPLACE FUNCTION is_transaction_unique(tx_sig TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM games WHERE transaction_signature = tx_sig
  );
END;
$$; 