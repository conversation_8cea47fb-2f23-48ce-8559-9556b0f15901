-- Remove multiplier storage from games table
ALTER TABLE games
DROP COLUMN IF EXISTS final_multiplier;

-- Update the rows JSONB structure to only store essential data
CREATE OR REPLACE FUNCTION update_rows_structure()
RETURNS void AS $$
BEGIN
  UPDATE games
  SET rows = (
    SELECT jsonb_agg(
      jsonb_build_object(
        'tiles', (row->>'tiles')::int,
        'deathTileIndex', (row->>'deathTileIndex')::int,
        'chosenTileIndex', (row->>'chosenTileIndex')::int
      )
    )
    FROM jsonb_array_elements(rows) AS row
  );
END;
$$ LANGUAGE plpgsql;

-- Execute the update
SELECT update_rows_structure();

-- Drop the temporary function
DROP FUNCTION update_rows_structure(); 