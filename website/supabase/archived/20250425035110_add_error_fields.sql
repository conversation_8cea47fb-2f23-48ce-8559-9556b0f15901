-- Add error_reason and requires_manual_action columns to games table
ALTER TABLE games ADD COLUMN IF NOT EXISTS error_reason TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS requires_manual_action BOOLEAN DEFAULT FALSE;

-- Update status enum to include 'error'
-- Note: 'complete' status is now deprecated, use 'won' instead
ALTER TABLE games 
  DROP CONSTRAINT IF EXISTS games_status_check,
  ADD CONSTRAINT games_status_check 
  CHECK (status IN ('active', 'won', 'lost', 'payout_pending', 'error')); 