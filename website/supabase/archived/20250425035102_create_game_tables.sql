-- Create games table for storing game state
CREATE TABLE games (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  wallet_address TEXT NOT NULL,
  bet_amount BIGINT NOT NULL,
  status TEXT NOT NULL, -- 'active', 'won', 'lost', 'payout_pending', 'complete'
  rows JSONB NOT NULL, -- Store the entire game layout
  current_row_index INTEGER NOT NULL DEFAULT -1,
  selected_tiles INTEGER[] DEFAULT ARRAY[]::INTEGER[],
  final_multiplier DECIMAL(10,2),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  transaction_signature TEXT, -- Signature of deposit transaction
  payout_tx_signature TEXT, -- Signature of payout transaction 
  payout_attempts INTEGER DEFAULT 0, -- Track number of payout attempts
  payout_error TEXT -- Store error details for debugging
);

-- Add index for faster wallet queries
CREATE INDEX idx_games_wallet_address ON games(wallet_address);

-- Create transactions table for tracking payments
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID NOT NULL REFERENCES games(id),
  wallet_address TEXT NOT NULL,
  amount BIGINT NOT NULL,
  direction TEXT NOT NULL, -- 'in' or 'out'
  status TEXT NOT NULL, -- 'pending', 'success', 'failed'
  tx_signature TEXT,
  error TEXT,
  attempt_count INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create function for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatically updating timestamp on games table
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON games
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Create trigger for automatically updating timestamp on transactions table
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Create helper function to increment a numeric column value
CREATE OR REPLACE FUNCTION increment(row_id UUID, column_name TEXT)
RETURNS INTEGER 
LANGUAGE plpgsql
AS $$
DECLARE
  current_value INTEGER;
  updated_value INTEGER;
BEGIN
  EXECUTE format('SELECT %I FROM games WHERE id = $1', column_name)
  INTO current_value
  USING row_id;
  
  updated_value := current_value + 1;
  
  RETURN updated_value;
END;
$$; 