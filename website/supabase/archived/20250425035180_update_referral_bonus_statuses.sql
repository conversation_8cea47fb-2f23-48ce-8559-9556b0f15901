-- Update referral_bonuses status enum to include all three states
ALTER TABLE referral_bonuses 
  DROP CONSTRAINT IF EXISTS referral_bonuses_status_check,
  ADD CONSTRAINT referral_bonuses_status_check 
  CHECK (status IN ('unpaid', 'pending', 'paid'));

-- Update existing records to use the new statuses
UPDATE referral_bonuses 
SET status = 'unpaid' 
WHERE status = 'pending' AND paid_at IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN referral_bonuses.status IS 'Status of the referral bonus: unpaid (initial state), pending (processing), paid (completed)'; 