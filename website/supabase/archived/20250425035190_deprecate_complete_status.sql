-- Migration to remove the 'complete' status

-- Add comment to games table
COMMENT ON TABLE games IS 'Stores the state of Death Race games. Note: "complete" status is deprecated and "won" should be used instead.';

-- Update any existing games that use the 'complete' status to 'won'
UPDATE games SET status = 'won' WHERE status = 'complete';

-- Update CHECK constraint to remove 'complete' from the list of valid statuses
ALTER TABLE games DROP CONSTRAINT games_status_check;
ALTER TABLE games ADD CONSTRAINT games_status_check 
CHECK (status IN (
  'active',         -- Game in progress
  'upcoming',       -- Game created but waiting for active game to complete
  'won',            -- Player won (cashed out)
  'lost',           -- Player hit a death tile
  'payout_pending', -- Payout is being processed
  'error'           -- Error state requiring manual intervention
));

-- Update column comment
COMMENT ON COLUMN games.status IS 'Game status: active, upcoming, won, lost, payout_pending, error'; 