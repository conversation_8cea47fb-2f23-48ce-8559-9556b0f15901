-- Function to get user statistics (total games, total bet amount, total won amount, profit/loss)
CREATE OR REPLACE FUNCTION get_user_stats(wallet TEXT)
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  total_games INT;
  total_bet_amount BIGINT;
  total_won_amount BIGINT;
  total_profit_amount BIGINT;
  profit_loss BIGINT;
BEGIN
  -- Get total number of completed games
  SELECT COUNT(*) INTO total_games
  FROM games
  WHERE wallet_address = wallet
  AND status != 'active';
  
  -- Get total bet amount
  SELECT COALESCE(SUM(bet_amount), 0) INTO total_bet_amount
  FROM games
  WHERE wallet_address = wallet
  AND status != 'active';
  
  -- Get total won amount (only count games with a status of 'won')
  SELECT COALESCE(SUM(bet_amount * final_multiplier), 0) INTO total_won_amount
  FROM games
  WHERE wallet_address = wallet
  AND status = 'won';
  
  -- Calculate profit from won games (winnings minus original bet)
  SELECT COALESCE(SUM(bet_amount * (final_multiplier - 1)), 0) INTO total_profit_amount
  FROM games
  WHERE wallet_address = wallet
  AND status = 'won';
  
  -- Calculate final profit/loss (profit from won games minus lost bets)
  SELECT total_profit_amount - COALESCE(SUM(bet_amount), 0) INTO profit_loss
  FROM games
  WHERE wallet_address = wallet
  AND status = 'lost';
  
  -- Return as JSON
  RETURN json_build_object(
    'total_games', total_games,
    'total_bet_amount', total_bet_amount,
    'total_won_amount', total_won_amount,
    'profit_loss', profit_loss
  );
END;
$$; 