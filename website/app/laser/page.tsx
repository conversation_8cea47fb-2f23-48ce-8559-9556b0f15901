'use client';

import { Challenges } from '@/components/Challenges';
import { LaserPartyGame } from '@/components/laser-party/components/LaserPartyGame';
import { Leaderboard } from '@/components/Leaderboard';
import { LASER_PARTY_GAME_TYPE } from '@/lib/constants';
import { useView } from '@/lib/context/ViewContext';
import { useAvailableGames } from '@/lib/hooks/flags/useAvailableGames';
import { useNoFunds } from '@/lib/hooks/useNoFunds';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function HomePage() {
  const router = useRouter();
  const { activeView } = useView();
  const availableGames = useAvailableGames();
  useNoFunds(LASER_PARTY_GAME_TYPE);

  useEffect(() => {
    if (availableGames && !availableGames.find((game) => game === LASER_PARTY_GAME_TYPE)) {
      router.push('/');
    }
  }, [availableGames, router]);

  if (!availableGames) {
    return null;
  }

  return (
    <div className='flex flex-1 h-full overflow-auto'>
      {/* On desktop: side-by-side view, On mobile: only show active view */}
      <div className={`w-full flex-1 ${activeView === 'game' ? 'block' : 'hidden sm:block'}`}>
        <LaserPartyGame />
      </div>
      <div
        className={`w-full sm:w-auto flex-col ${activeView === 'leaderboard' ? 'flex' : 'hidden lg:flex lg:w-[22rem]'}`}
      >
        <Leaderboard gameType={LASER_PARTY_GAME_TYPE} />
        <Challenges gameType={LASER_PARTY_GAME_TYPE} />
      </div>
    </div>
  );
}
