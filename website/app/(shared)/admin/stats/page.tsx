'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatNumber } from '@/lib/utils';
import { useRouter, useSearchParams } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, Minus, ChartLine } from '@phosphor-icons/react';
import {
  useGetAdminStats,
  useGetUser,
  useGetAllGamesAdmin,
  useEthPrice,
} from '@/lib/client/queries';
import { isAdmin } from '@/lib/utils';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { DEATH_RACE_HOUSE_EDGE, GAME_TYPES, GameType } from '@/lib/constants';
import { useState } from 'react';
import { GameHistory } from '@/components/GameHistory';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AbstractXPExport } from '@/components/admin/AbstractXPExport';
import { DateRangePicker } from '@/components/admin/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { GameTypeDropdown } from '@/components/common/GameTypeDropdown';

export default function AdminPage() {
  const { data: userData } = useGetUser();
  const { data: ethPrice } = useEthPrice();
  const searchParams = useSearchParams();
  const gameType = searchParams.get('gameType');

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const {
    data: stats,
    isPending: isAdminStatsLoading,
    error,
  } = useGetAdminStats(
    userData?.role ?? '',
    (gameType as GameType) ?? undefined,
    dateRange?.from?.toISOString(),
    dateRange?.to?.toISOString()
  );
  const { role } = userData ?? {};
  const admin = isAdmin(role);
  const router = useRouter();

  // Pagination and sorting state for all games
  const [gamesPage, setGamesPage] = useState(1);
  const [sortBy, setSortBy] = useState<'created_at' | 'profit' | 'final_multiplier'>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const { data: allGamesData, isLoading: allGamesLoading } = useGetAllGamesAdmin(
    gamesPage,
    15,
    admin,
    sortBy,
    sortDirection,
    (gameType as GameType) ?? undefined
  );

  const handleSortChange = (column: 'created_at' | 'profit' | 'final_multiplier') => {
    if (sortBy === column) {
      setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortBy(column);
      setSortDirection('desc');
    }
    setGamesPage(1);
  };

  if (userData && !admin) {
    router.push('/');
  }

  return (
    <div className='container mx-auto p-4 pb-20 text-white space-y-10 flex-1 overflow-auto'>
      <div className='flex flex-col sm:flex-row gap-4 items-center justify-between sm:items-start mt-2 -mb-3'>
        <AdminHeader />
        <div className='flex items-center gap-4 w-full justify-end'>
          <GameTypeDropdown />
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
        </div>
      </div>
      {error ? (
        <div className='text-red-500'>{error.message}</div>
      ) : (
        <div className='grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6'>
          <StatCard
            title='Profit'
            value={stats ? `${formatNumber(Number(stats.treasuryDelta))} ETH` : '0 ETH'}
            secondaryValue={
              <div className='flex items-center gap-6'>
                {stats?.treasuryDelta && ethPrice && (
                  <span
                    className={`text-sm ${Number(stats.treasuryDelta) > 0 ? 'text-gc-400' : 'text-red-500'}`}
                  >
                    {Number(stats.treasuryDelta) > 0 ? '+' : ''}$
                    {formatNumber(Number(stats.treasuryDelta) * ethPrice)}
                  </span>
                )}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className='flex items-center gap-1 cursor-help'>
                        <ChartLine className='w-3 h-3' />
                        {`${formatNumber(Number(stats?.totalVolume ?? 0) * DEATH_RACE_HOUSE_EDGE)} ETH`}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Expected profit based on {DEATH_RACE_HOUSE_EDGE * 100}% house edge</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            }
            className={
              stats?.treasuryDelta && Number(stats.treasuryDelta) > 0
                ? 'text-gc-400'
                : stats?.treasuryDelta && Number(stats.treasuryDelta) < 0
                  ? 'text-red-500'
                  : ''
            }
            loading={isAdminStatsLoading}
          />
          <StatCard
            title='Return to Player'
            value={
              stats && parseFloat(stats.totalVolume) > 0
                ? `${((parseFloat(stats.totalPayouts) / parseFloat(stats.totalVolume)) * 100).toFixed(1)}%`
                : 'N/A'
            }
            secondaryValue={
              stats &&
              typeof stats.averageWinMultiplier === 'number' &&
              typeof stats.bustPercentage === 'number'
                ? `Expected: ${((stats.averageWinMultiplier / (1 / (1 - stats.bustPercentage / 100))) * 100).toFixed(1)}%`
                : 'Expected: N/A'
            }
            loading={isAdminStatsLoading}
            className={
              stats &&
              typeof stats.averageWinMultiplier === 'number' &&
              typeof stats.bustPercentage === 'number'
                ? stats.averageWinMultiplier > 1 / (1 - stats.bustPercentage / 100)
                  ? 'text-lime-400'
                  : 'text-red-500'
                : 'text-lime-400'
            }
          />
          <StatCard
            title='Total Volume (ETH)'
            value={stats ? `${formatNumber(Number(stats.totalVolume))} ETH` : '0 ETH'}
            secondaryValue={
              stats?.totalVolume &&
              ethPrice && (
                <span className='text-sm text-white/70'>
                  ${formatNumber(Number(stats.totalVolume) * ethPrice)}
                </span>
              )
            }
            loading={isAdminStatsLoading}
          />
          <StatCard
            title='Total Games'
            value={stats ? formatNumber(stats.totalGames) : '0'}
            loading={isAdminStatsLoading}
          />
          <StatCard
            title='Total Users'
            value={stats ? formatNumber(stats.totalUsers) : '0'}
            loading={isAdminStatsLoading}
          />
          <StatCard
            title='Bust Percentage'
            value={
              stats && typeof stats.bustPercentage === 'number' ? `${stats.bustPercentage}%` : 'N/A'
            }
            loading={isAdminStatsLoading}
            className='text-white'
          />
          <StatCard
            title='Avg Win Multiplier'
            value={
              stats && typeof stats.averageWinMultiplier === 'number'
                ? `${stats.averageWinMultiplier}x`
                : 'N/A'
            }
            secondaryValue={
              stats && typeof stats.bustPercentage === 'number'
                ? `Fair: ${(1 / (1 - stats.bustPercentage / 100)).toFixed(2)}x`
                : 'Fair: N/A'
            }
            loading={isAdminStatsLoading}
            className='text-white'
          />
          <StatCard
            title='Average Row Reached'
            value={
              stats && typeof stats.averageRowReached === 'number'
                ? `${stats.averageRowReached.toFixed(1)}`
                : 'N/A'
            }
            loading={isAdminStatsLoading}
            className='text-white'
          />
        </div>
      )}
      {/* All Games Table */}
      <div>
        <GameHistory
          games={allGamesData?.games || []}
          totalPages={allGamesData?.totalPages || 1}
          currentPage={gamesPage}
          loading={allGamesLoading}
          onPageChange={setGamesPage}
          showUsername={true}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onSortChange={handleSortChange}
          pageSize={15}
        />
      </div>

      {/* Abstract XP Export */}
      <div className='flex justify-center'>
        <AbstractXPExport />
      </div>
    </div>
  );
}

// StatCard component
function StatCard({
  title,
  value,
  secondaryValue,
  loading,
  className = '',
  icon: Icon,
}: {
  title: string;
  value: string;
  secondaryValue?: string | React.ReactNode;
  loading: boolean;
  className?: string;
  icon?: React.ComponentType<{ className?: string }>;
}) {
  return (
    <Card className='bg-gc-900/20 border-gc-400/20 min-h-[9rem]'>
      <CardHeader className='pb-1 flex flex-col items-center'>
        <CardTitle className='text-sm font-bold text-white text-center'>{title}</CardTitle>
      </CardHeader>
      <CardContent className='pt-2 flex flex-col items-center'>
        {loading ? (
          <Skeleton className='h-9 w-24' />
        ) : (
          <div className='flex flex-col items-center'>
            <div
              className={`text-2xl lg:text-3xl font-bold flex items-center justify-center ${className || 'text-white'}`}
            >
              {Icon && <Icon className='w-4 h-4 mr-1' />}
              {value}
            </div>
            {secondaryValue && (
              <div className='text-sm text-white/70 mt-2 flex items-center gap-1'>
                {secondaryValue}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
