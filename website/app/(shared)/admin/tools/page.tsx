'use client';

import { useState, useEffect } from 'react';
import { useReadContract } from 'wagmi';
import { Button } from '@/components/ui/button/Button';
import DeathRaceGameAbiData from '@/lib/blockchain/abi/DeathFun.json';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { type Abi, isAddress } from 'viem';
import { useGetUser } from '@/lib/client/queries';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { useRouter } from 'next/navigation';
import { isSuperAdmin } from '@/lib/utils';
import { ChallengesAdmin } from '@/components/admin/challenges/ChallengesAdmin';
import DistributeDailyRewardsButton from '@/components/DistributeDailyRewardsButton';
import { TextInput } from '@/components/ui/input/TextInput';
import { NumberInput } from '@/components/ui/input/NumberInput';

// Define the structure matching the Solidity Game struct
// Use string for uint256/address, number for enum
interface GameDetails {
  player: string;
  betAmount: string;
  commitmentHash: string;
  status: number; // 0: Active, 1: Won, 2: Lost
  payoutAmount: string;
  selectedTilesHash: string;
  gameSeed: string; // bytes32 represented as hex string
  createdAt: string; // uint256 timestamp
}

// Correct the environment variable name to match .env.local
const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS as `0x${string}`;
const contractAbi = DeathRaceGameAbiData.abi as Abi;

export default function DebugPage() {
  const [isClient, setIsClient] = useState(false);
  const [gameId, setGameId] = useState<string>('');
  const [debouncedGameId, setDebouncedGameId] = useState<bigint | undefined>(undefined);
  const [formattedDetails, setFormattedDetails] = useState<GameDetails | null>(null);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [recipientAddress, setRecipientAddress] = useState('');
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const { data: userData } = useGetUser();
  const { walletAddress: connectedWalletAddress, role } = userData ?? {};
  const router = useRouter();

  const { data, error, isLoading, refetch, isError, isSuccess } = useReadContract({
    address: contractAddress,
    abi: contractAbi,
    functionName: 'getGameDetails',
    args: debouncedGameId ? [debouncedGameId] : undefined,
    query: {
      enabled: !!debouncedGameId && isClient,
    },
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (connectedWalletAddress) {
      setRecipientAddress(connectedWalletAddress);
    }
  }, [connectedWalletAddress]);

  // Redirect if not super-admin
  useEffect(() => {
    if (role && !isSuperAdmin(role)) {
      router.replace('/');
    }
  }, [role, router]);

  const handleFetchClick = () => {
    if (!isClient) return;

    if (gameId) {
      try {
        const idAsBigInt = BigInt(gameId);
        setDebouncedGameId(idAsBigInt);
      } catch (e) {
        toast.error('Invalid Game ID format.');
        setDebouncedGameId(undefined);
      }
    } else {
      toast.warning('Please enter a Game ID.');
      setDebouncedGameId(undefined);
    }
  };

  useEffect(() => {
    if (isSuccess && data && isClient) {
      const result = data as any;
      const details: GameDetails = {
        player: result.player,
        betAmount: result.betAmount.toString(),
        commitmentHash: result.commitmentHash,
        status: Number(result.status),
        payoutAmount: result.payoutAmount.toString(),
        selectedTilesHash: result.selectedTilesHash,
        gameSeed: result.gameSeed,
        createdAt: result.createdAt.toString(),
      };
      setFormattedDetails(details);
    } else if (isError && error && isClient) {
      console.error('Error fetching game details:', error);
      if (error.message.includes('GameDoesNotExist')) {
        toast.error(`Game with ID ${debouncedGameId} does not exist.`);
      } else {
        const message = error.shortMessage || error.message || 'Failed to fetch game details.';
        toast.error(`Error: ${message}`);
      }
      setFormattedDetails(null);
    }
  }, [data, isSuccess, isError, error, debouncedGameId, isClient]);

  const replacer = (key: string, value: any) =>
    typeof value === 'bigint' ? value.toString() : value;

  const getStatusText = (status: number): string => {
    switch (status) {
      case 0:
        return 'Active';
      case 1:
        return 'Won';
      case 2:
        return 'Lost';
      default:
        return 'Unknown';
    }
  };

  // Withdraw Funds handler
  const handleWithdraw = async () => {
    if (!withdrawAmount || isNaN(Number(withdrawAmount)) || Number(withdrawAmount) <= 0) {
      toast.error('Enter a valid ETH amount');
      return;
    }
    if (!recipientAddress || !isAddress(recipientAddress)) {
      toast.error('Enter a valid recipient address');
      return;
    }

    setIsWithdrawing(true);
    try {
      const res = await fetch('/api/abstract/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amountEth: withdrawAmount,
          recipientAddress: recipientAddress,
        }),
      });
      const data = await res.json();
      if (res.ok && data.success) {
        toast.success(`Withdrawn! Tx: ${data.txHash}`);
        setWithdrawAmount('');
      } else {
        toast.error(data.error || 'Withdraw failed');
      }
    } catch (err: any) {
      toast.error(err.message || 'Withdraw failed');
    } finally {
      setIsWithdrawing(false);
    }
  };

  if (role && !isSuperAdmin(role)) {
    return null;
  }

  return (
    <div className='mx-auto p-4 pb-20 text-white space-y-10 flex-1 overflow-auto'>
      <div className='max-w-5xl mx-auto'>
        <div className='flex justify-center mb-6'>
          <AdminHeader />
        </div>

        {isClient ? (
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <ChallengesAdmin />
            </div>
            <div>
              {/* Withdraw Funds Tool */}
              <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
                <CardHeader>
                  <CardTitle className='text-white'>Withdraw from Pot</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='flex space-x-2 mb-2'>
                    <NumberInput
                      placeholder='Amount in ETH'
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      disabled={isWithdrawing}
                      className='w-full'
                    />
                  </div>
                  <div className='flex space-x-2 mb-4'>
                    <TextInput
                      placeholder='Recipient Address'
                      value={recipientAddress}
                      onChange={(e) => setRecipientAddress(e.target.value)}
                      disabled={isWithdrawing}
                      className='w-full'
                    />
                    <Button
                      onClick={handleWithdraw}
                      disabled={
                        isWithdrawing ||
                        !withdrawAmount ||
                        !recipientAddress ||
                        !isAddress(recipientAddress)
                      }
                      variant='primary-outline'
                      className=' min-w-[8rem]'
                    >
                      {isWithdrawing ? 'Withdrawing...' : 'Withdraw'}
                    </Button>
                  </div>
                  <p className='text-xs text-muted-foreground'>
                    Withdraws ETH from contract pot to the specified address.
                  </p>
                </CardContent>
              </Card>

              {/* Fetch Game Details Tool */}
              <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
                <CardHeader>
                  <CardTitle className='text-white'>Get On-Chain Game Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='flex space-x-2 mb-4'>
                    <NumberInput
                      placeholder='Enter On-Chain Game ID (e.g., 1)'
                      value={gameId}
                      onChange={(e) => setGameId(e.target.value)}
                      disabled={isLoading}
                      min='1'
                      step='1'
                      className='w-full'
                    />
                    <Button
                      onClick={handleFetchClick}
                      disabled={isLoading || !gameId}
                      variant='primary-outline'
                      className='min-w-[8rem]'
                    >
                      {isLoading ? 'Loading...' : 'Fetch'}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Distribute Daily Rewards Tool */}
              <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
                <CardHeader>
                  <CardTitle className='text-white'>Power Tools</CardTitle>
                </CardHeader>
                <CardContent>
                  <DistributeDailyRewardsButton />
                </CardContent>
              </Card>

              {/* Loading State Card */}
              {isLoading && (
                <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
                  <CardHeader>
                    <CardTitle className='text-white'>Loading...</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className='text-muted-foreground'>
                      Fetching details for Game ID: {debouncedGameId?.toString()}...
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Game Details Display Card */}
              {!isLoading && formattedDetails && (
                <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
                  <CardHeader>
                    <CardTitle className='text-white'>
                      Game Details (ID: {debouncedGameId?.toString()})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className='bg-black p-4 rounded-md overflow-x-auto text-sm text-neutral-400 border border-neutral-800'>
                      {JSON.stringify(
                        {
                          ...formattedDetails,
                          status: `${formattedDetails.status} (${getStatusText(formattedDetails.status)})`,
                        },
                        replacer,
                        2
                      )}
                    </pre>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        ) : (
          <p className='text-muted-foreground'>Loading debug tools...</p>
        )}
      </div>
    </div>
  );
}
