'use client';

import { GameTypeDropdown } from '@/components/common/GameTypeDropdown';
import { GameHistory } from '@/components/GameHistory';
import { Button } from '@/components/ui/button/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useGetGameHistory, useGetUser, UserDataResponse } from '@/lib/client/queries';
import { GAME_TYPES } from '@/lib/constants';
import { isAdmin as isAdminRole } from '@/lib/utils';
import { Wallet } from '@phosphor-icons/react';
import { usePrivy } from '@privy-io/react-auth';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { formatEther } from 'viem';
import { Icon } from '@/components/ui/icons/Icon';

const isAllowed = (
  userData: UserDataResponse | undefined,
  walletParam: string | null,
  usernameParam: string | null
) => {
  if (!userData) return false;

  const isAdmin = isAdminRole(userData.role);
  if (isAdmin) return true;

  if (walletParam && walletParam !== userData.walletAddress) return false;
  if (usernameParam && usernameParam !== userData.username) return false;

  return true;
};

// Main component that handles the stats page logic
function StatsPageClient() {
  // ------------------------------------------------ STATE
  const [page, setPage] = useState(1);

  // ------------------------------------------------ HOOKS
  const searchParams = useSearchParams();
  const walletParam = searchParams.get('wallet');
  const usernameParam = searchParams.get('username');
  const gameIdParam = searchParams.get('gameId');
  const gameTypeParam = searchParams.get('gameType');
  const { data: userData, isPending: userDataLoading } = useGetUser();
  const { walletAddress, role } = userData ?? {};
  const admin = isAdminRole(role);

  // Determine which user to show: logged-in user or username param (if allowed)
  const viewedUsername = admin && usernameParam ? usernameParam : userData?.username;
  const { data: gameHistory, isLoading: gameHistoryLoading } = useGetGameHistory(
    isAllowed(userData, walletParam, usernameParam),
    page,
    walletParam || walletAddress || '',
    viewedUsername || '',
    gameIdParam || undefined,
    gameTypeParam as (typeof GAME_TYPES)[number] | undefined
  );
  const { authenticated, login } = usePrivy();
  const router = useRouter();

  // After gameHistory is available, determine displayWallet for admin/usernameParam
  let displayWallet = undefined;
  if (admin && usernameParam) {
    displayWallet = gameHistory?.viewedWalletAddress || '';
  } else {
    displayWallet = walletParam || walletAddress || '';
  }

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    if (userData && !isAllowed(userData, walletParam, usernameParam)) {
      router.push('/stats');
    }
  }, [userData, walletParam, usernameParam]);

  // ------------------------------------------------ RENDER
  // If no wallet is connected and not viewing another wallet as admin
  if (!userDataLoading && !userData) {
    return (
      <div className='flex-1 overflow-auto'>
        <div className='container mx-auto py-6 sm:py-12 px-4 text-white'>
          <div className='text-center'>
            <h1 className='text-3xl font-bold mb-6'>Stats</h1>
            <p className='mb-6'>Connect your wallet to view your game history and stats.</p>
            <Button
              onClick={() => {
                if (!authenticated) {
                  login();
                }
              }}
              variant='primary'
            >
              <Wallet className='h-4 w-4' /> Sign in
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 overflow-auto'>
      <div className='container mx-auto py-6 sm:py-10 px-4 text-white'>
        {/* User Info */}
        <div className='flex flex-grow items-center gap-2 mb-6 justify-end'>
          {usernameParam && (
            <div
              className={`flex-grow transition-opacity duration-300 ${!viewedUsername ? 'opacity-0' : 'opacity-100'}`}
            >
              <div className='text-lg font-bold text-gc-400'>
                <span>@{viewedUsername || '--'}</span>
              </div>
              <div className='text-xs text-white/80 font-mono break-all'>
                {displayWallet || '--'}
              </div>
            </div>
          )}

          <GameTypeDropdown />
        </div>

        {/* Stats Cards */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'>
          <StatCard
            title='Profit/Loss'
            value={
              gameHistory ? (
                <span className='flex items-center gap-0.5'>
                  <Icon id="eth" className='w-5 h-5 inline align-middle mr-1' />
                  {`${BigInt(gameHistory.stats.totalProfitLoss) > 0 ? '+' : ''}${formatEther(BigInt(gameHistory.stats.totalProfitLoss))}`}
                </span>
              ) : (
                <span className='flex items-center gap-0.5'>
                  <Icon id="eth" className='w-5 h-5 inline align-middle mr-1' />0
                </span>
              )
            }
            className={
              gameHistory?.stats.totalProfitLoss && BigInt(gameHistory.stats.totalProfitLoss) > 0
                ? 'text-gc-400'
                : gameHistory?.stats.totalProfitLoss &&
                    BigInt(gameHistory.stats.totalProfitLoss) < 0
                  ? 'text-red-500'
                  : ''
            }
            loading={gameHistoryLoading}
          />
          <StatCard
            title='Total Wagered'
            value={
              gameHistory ? (
                <span className='flex items-center gap-0.5'>
                  <Icon id='eth' className='w-5 h-5 inline align-middle mr-1' />
                  {formatEther(BigInt(gameHistory.stats.totalBetAmount))}
                </span>
              ) : (
                <span className='flex items-center gap-0.5'>
                  <Icon id='eth' className='w-5 h-5 inline align-middle mr-1' />0
                </span>
              )
            }
            loading={gameHistoryLoading}
          />
          <StatCard
            title='Total Games'
            value={gameHistory?.stats.totalGames.toString() || '0'}
            loading={gameHistoryLoading}
          />
        </div>

        {/* Game History Table */}
        <GameHistory
          games={gameHistory?.games || []}
          totalPages={gameHistory?.totalPages || 1}
          currentPage={page}
          loading={gameHistoryLoading}
          onPageChange={setPage}
        />
      </div>
    </div>
  );
}

// Main page component
export default function StatsPage() {
  return <StatsPageClient />;
}

// StatCard component
function StatCard({
  title,
  value,
  loading,
  className = '',
}: {
  title: string;
  value: React.ReactNode;
  loading: boolean;
  className?: string;
}) {
  return (
    <Card>
      <CardHeader className='pb-1'>
        <CardTitle className='text-sm font-medium text-white'>{title}</CardTitle>
      </CardHeader>
      <CardContent className='pt-2'>
        {loading ? (
          <Skeleton className='h-9 w-24' />
        ) : (
          <div className={`text-2xl font-bold ${className || 'text-white'}`}>{value}</div>
        )}
      </CardContent>
    </Card>
  );
}
