import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { GAME_TYPES } from '@/lib/constants';

// Protect this endpoint with a secret (set in Vercel env)
const CRON_SECRET = process.env.CRON_SECRET;

export async function GET(request: Request) {
  try {
    // Check for CRON_SECRET authentication
    const authHeader = request.headers.get('authorization');

    if (!authHeader || authHeader !== `Bearer ${CRON_SECRET}`) {
      return NextResponse.json(
        {
          error: 'Unauthorized',
          details: 'Cron secret authentication failed.',
        },
        { status: 401 }
      );
    }

    // 1. Find the current published abstractUpvote challenge
    const { data: currentChallenges, error: findError } = await supabaseServerClient
      .from('challenges')
      .select('*')
      .eq('type', 'abstractUpvote')
      .eq('published', true)
      .in('game_type', GAME_TYPES);

    if (findError) {
      if (findError.code === 'PGRST116') {
        // No published abstractUpvote challenge found
        return NextResponse.json({
          message: 'No published abstractUpvote challenge found to reset',
          reset: false,
        });
      }

      console.error('[CRON] Failed to find current abstractUpvote challenge:', findError);
      return NextResponse.json(
        { error: 'Failed to find current abstractUpvote challenge', details: findError },
        { status: 500 }
      );
    }

    const newChallenges: { id: number; title: string }[] = [];
    for (const currentChallenge of currentChallenges) {
      // 2. Unpublish the current challenge
      const { error: unpublishError } = await supabaseServerClient
        .from('challenges')
        .update({ published: false })
        .eq('id', currentChallenge.id);

      if (unpublishError) {
        console.error('[CRON] Failed to unpublish current challenge:', unpublishError);
        return NextResponse.json(
          { error: 'Failed to unpublish current challenge', details: unpublishError },
          { status: 500 }
        );
      }

      // 3. Create a new challenge with the same data (excluding id and created_at)
      const { data: newChallenge, error: createError } = await supabaseServerClient
        .from('challenges')
        .insert({
          title: currentChallenge.title,
          description: currentChallenge.description,
          link: currentChallenge.link,
          type: currentChallenge.type,
          points: currentChallenge.points,
          published: true,
          icon: currentChallenge.icon,
          game_type: currentChallenge.game_type,
        })
        .select()
        .single();

      if (createError) {
        console.error('[CRON] Failed to create new challenge:', createError);
        return NextResponse.json(
          { error: 'Failed to create new challenge', details: createError },
          { status: 500 }
        );
      }

      newChallenges.push({
        id: newChallenge.id,
        title: newChallenge.title,
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Abstract upvote challenge reset successfully',
      unpublishedChallenges: currentChallenges.map((c) => ({
        id: c.id,
        title: c.title,
      })),
      newChallenges,
      reset: true,
    });
  } catch (err) {
    console.error('[CRON] Unexpected error in weekly challenge reset:', err);
    return NextResponse.json({ error: 'Unexpected error', details: String(err) }, { status: 500 });
  }
}
