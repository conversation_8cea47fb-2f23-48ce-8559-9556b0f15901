import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import {
  DAILY_LEADERBOARD_TOTAL_POINTS,
  DAILY_LEADERBOARD_NUM_WINNERS,
  DAILY_LEADERBOARD_ENABLED,
  DAILY_LEADERBOARD_RESET_TIME_ET,
  GAME_TYPES,
} from '@/lib/constants';
import { verifyToken } from '@/lib/server/auth';
import { isAdmin } from '@/lib/utils';
import { isResetTime, getDeathPointsDistribution } from '@/lib/utils/daily-leaderboard';

// Protect this endpoint with a secret (set in Vercel env)
const CRON_SECRET = process.env.CRON_SECRET;

async function handleDailyLeaderboard(request: Request) {
  try {
    // Check for either CRON_SECRET or admin authentication
    const authHeader = request.headers.get('authorization');
    let isAuthorized = false;
    let isAdminUser = false;

    // First, check if CRON_SECRET is provided
    if (authHeader && authHeader === `Bearer ${CRON_SECRET}`) {
      isAuthorized = true;
    } else {
      // If no CRON_SECRET, check for admin authentication
      const { walletAddress, error: verifyError } = await verifyToken();
      if (!verifyError && walletAddress) {
        const { data: userData, error: userError } = await supabaseServerClient
          .from('users')
          .select('role')
          .eq('wallet_address', walletAddress)
          .single();

        if (!userError && isAdmin(userData?.role)) {
          isAuthorized = true;
          isAdminUser = true;
        }
      }
    }

    if (!isAuthorized) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only check reset time and feature enabled for non-admin users
    if (!isAdminUser) {
      // Check if it's the configured reset time, return early if not
      if (!isResetTime()) {
        return NextResponse.json({
          message: `Not ${DAILY_LEADERBOARD_RESET_TIME_ET} ET, skipping daily leaderboard distribution`,
          distributed: 0,
          reset: false,
        });
      }

      // Check if daily leaderboard feature is enabled
      if (!DAILY_LEADERBOARD_ENABLED) {
        return NextResponse.json({
          message: 'Daily leaderboard feature is disabled',
          distributed: 0,
          reset: false,
        });
      }
    }

    const gamesStats: {
      [key: string]: {
        topUsers: {
          user_id: string | null;
          profit_loss_daily: number;
        }[];
        distribution: number[];
        rpcData: any;
        error: any | null;
      };
    } = {};

    for (const gameType of GAME_TYPES) {
      // 1. Get top N users by profit_loss_daily (exclude null and zero/negative values)
      let query = supabaseServerClient
        .from('leaderboard_records')
        .select('user_id, profit_loss_daily')
        .eq('game_type', gameType)
        .not('profit_loss_daily', 'is', null)
        .gt('profit_loss_daily', 0);

      const { data: topUsers, error: leaderboardError } = await query
        .order('profit_loss_daily', { ascending: false })
        .limit(DAILY_LEADERBOARD_NUM_WINNERS);

      if (leaderboardError) {
        console.error(
          '[CRON] Failed to fetch leaderboard for game type:',
          gameType,
          leaderboardError
        );
        gamesStats[gameType] = {
          topUsers: [],
          distribution: [],
          rpcData: null,
          error: leaderboardError,
        };
        continue;
      }
      if (!topUsers || topUsers.length === 0) {
        console.error('[CRON] No top users found for distribution for game type:', gameType);
        gamesStats[gameType] = {
          topUsers: [],
          distribution: [],
          rpcData: null,
          error: new Error('No top users found for distribution.'),
        };
        continue;
      }

      const totalPoints = DAILY_LEADERBOARD_TOTAL_POINTS;
      const winners = DAILY_LEADERBOARD_NUM_WINNERS;
      const decay = 0.93;
      const distribution = getDeathPointsDistribution(totalPoints, winners, decay);

      const cleanedTopUsers = topUsers.map((u) => {
        return {
          ...u,
          profit_loss_daily: u.profit_loss_daily || 0,
        };
      });

      // Prepare arrays for the RPC
      const userIds = cleanedTopUsers.map((u) => u.user_id).filter((id) => id !== null);
      const pointsToAdd = distribution.slice(0, cleanedTopUsers.length);

      // 2. Call a Postgres function to do the distribution and reset in a transaction
      const { error: rpcError, data: rpcData } = await supabaseServerClient.rpc(
        'distribute_and_reset_daily_leaderboard',
        {
          p_user_ids: userIds,
          points_to_add: pointsToAdd,
          p_game_type: gameType,
        }
      );

      if (rpcError) {
        console.error(
          '[CRON] RPC error in distribute_and_reset_daily_leaderboard for game type:',
          gameType,
          rpcError,
          {
            userIds,
            pointsToAdd,
            topUsers,
            distribution,
          }
        );
        gamesStats[gameType] = {
          topUsers: cleanedTopUsers,
          distribution,
          rpcData: null,
          error: rpcError,
        };
        continue;
      }

      gamesStats[gameType] = {
        topUsers: cleanedTopUsers,
        distribution,
        rpcData,
        error: null,
      };
    }

    return NextResponse.json({ success: true, gamesStats });
  } catch (err) {
    console.error('[CRON] Unexpected error in daily leaderboard cron:', err);
    return NextResponse.json({ error: 'Unexpected error', details: String(err) }, { status: 500 });
  }
}

export async function GET(request: Request) {
  return handleDailyLeaderboard(request);
}

export async function POST(request: Request) {
  return handleDailyLeaderboard(request);
}
