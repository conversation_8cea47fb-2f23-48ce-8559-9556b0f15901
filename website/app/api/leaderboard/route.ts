import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { LeaderboardEntry } from '@/lib/types/api';
import { DAILY_LEADERBOARD_ENABLED } from '@/lib/constants';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const timeFilter = searchParams.get('timeFilter') || 'daily';
    const currentWallet = searchParams.get('currentWallet');
    const gameType = searchParams.get('gameType') || 'all';
    const appId = process.env.APP_ID; // Get App ID from environment

    if (!appId) {
      console.error('[Leaderboard] APP_ID environment variable is not set.');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Declare processedData variable
    let processedData: LeaderboardEntry[] = [];

    // Determine which profit/loss column to use based on timeFilter
    let profitLossColumn = 'total_profit_loss';
    let isPointsLeaderboard = false;
    if (timeFilter === 'points') {
      isPointsLeaderboard = true;
    } else if (timeFilter === 'daily') {
      profitLossColumn = 'profit_loss_daily';
    }

    if (isPointsLeaderboard) {
      // Points leaderboard: order by points desc, filter > 0
      let query = supabaseServerClient
        .from('users')
        .select(
          `
        wallet_address,
        username,
        points
      `
        )
        .eq('app', appId)
        .not('points', 'is', null)
        .gt('points', 0)
        .order('points', { ascending: false })
        .limit(50);
      if (gameType !== 'all') {
        query = query.eq('game_type', gameType);
      }
      const { data: leaderboardData, error } = await query;

      if (error) {
        console.error('[Leaderboard] Error fetching points leaderboard:', error);
        return NextResponse.json({ error: 'Failed to fetch leaderboard' }, { status: 500 });
      }

      const processedData = (leaderboardData || []).map((user, index) => ({
        username: user.username || undefined,
        profit_loss: 0, // Not used for points leaderboard
        points: user.points || 0,
        rank: index + 1,
        is_current_user: user.wallet_address === currentWallet,
      }));

      // If current user not in results, fetch and add
      if (currentWallet && !processedData.some((entry) => entry.is_current_user)) {
        let currentUserQuery = supabaseServerClient
          .from('users')
          .select(`wallet_address, username, points`)
          .eq('wallet_address', currentWallet)
          .eq('app', appId);
        if (gameType !== 'all') {
          currentUserQuery = currentUserQuery.eq('game_type', gameType);
        }
        const { data: currentUserData, error: currentUserError } = await currentUserQuery.single();
        if (!currentUserError && currentUserData) {
          // Get rank
          let rankQuery = supabaseServerClient
            .from('users')
            .select('*', { count: 'exact', head: true })
            .eq('app', appId)
            .not('points', 'is', null)
            .gt('points', 0)
            .gt('points', currentUserData.points || 0);
          if (gameType !== 'all') {
            rankQuery = rankQuery.eq('game_type', gameType);
          }
          const { count: higherRankedUsers, error: rankError } = await rankQuery;

          if (rankError) {
            console.error('Error getting user rank (points):', rankError);
          }
          processedData.push({
            username: currentUserData.username || undefined,
            profit_loss: 0,
            points: currentUserData.points || 0,
            rank: (higherRankedUsers ?? 0) + 1,
            is_current_user: true,
          });
          processedData.sort((a, b) => (b.points ?? 0) - (a.points ?? 0));
          processedData.forEach((entry, index) => {
            if (!entry.is_current_user) {
              entry.rank = index + 1;
            }
          });
        }
      }
      return NextResponse.json(processedData);
    } else {
      // Regular profit/loss leaderboard
      let query = supabaseServerClient
        .from('leaderboard_records')
        .select(
          `
          total_profit_loss,
          profit_loss_daily,
          users(
            wallet_address,
            username
          )
        `
        )
        .not(profitLossColumn, 'is', null)
        .neq(profitLossColumn, 0);

      if (gameType !== 'all') {
        query = query.eq('game_type', gameType);
      }

      // Apply feature flag filtering for daily leaderboard
      if (timeFilter === 'daily') {
        if (DAILY_LEADERBOARD_ENABLED === 'admin') {
          // Only show admins in daily leaderboard when feature is set to admin-only
          query = query.in('role', ['admin', 'super-admin']);
        } else if (DAILY_LEADERBOARD_ENABLED === false) {
          // If feature is disabled, return empty results for daily
          processedData = [];
          return NextResponse.json(processedData);
        }
      }

      const { data: leaderboardData, error } = await query
        .order(profitLossColumn, { ascending: false })
        .limit(50);

      if (error) {
        console.error('[Leaderboard] Error fetching leaderboard:', error);
        return NextResponse.json({ error: 'Failed to fetch leaderboard' }, { status: 500 });
      }

      processedData = (leaderboardData || []).map((user, index) => {
        let profitLossValue = 0;
        if (timeFilter === 'daily') {
          profitLossValue = user.profit_loss_daily || 0;
        } else {
          profitLossValue = user.total_profit_loss || 0;
        }

        const userData = user.users;
        return {
          username: userData?.username || undefined,
          profit_loss: profitLossValue,
          rank: index + 1,
          is_current_user: userData?.wallet_address === currentWallet,
        };
      });

      // If we have a current wallet and it's not already in the results, fetch and add it
      if (currentWallet) {
        const currentUserInResults = processedData.some((entry) => entry.is_current_user);

        if (!currentUserInResults) {
          // Fetch current user's data
          let currentUserDataQuery = supabaseServerClient
            .from('leaderboard_records')
            .select(
              `
              total_profit_loss,
              profit_loss_daily,
              users!inner(
                wallet_address,
                username
              )
            `
            )
            .eq('users.wallet_address', currentWallet)
          if (gameType !== 'all') {
            currentUserDataQuery = currentUserDataQuery.eq('game_type', gameType);
          }
          const { data: currentUserData, error: currentUserError } =
            await currentUserDataQuery.single();

          if (!currentUserError && currentUserData) {
            let profitLossValue = 0;
            if (timeFilter === 'daily') {
              profitLossValue = currentUserData.profit_loss_daily || 0;
            } else {
              profitLossValue = currentUserData.total_profit_loss || 0;
            }

            // Get the user's actual rank by counting users with higher profit/loss
            let userRankQuery = supabaseServerClient
              .from('leaderboard_records')
              .select('*', { count: 'exact', head: true })
              .not(profitLossColumn, 'is', null)
              .neq(profitLossColumn, 0)
              .gt(profitLossColumn, profitLossValue);
            if (gameType !== 'all') {
              userRankQuery = userRankQuery.eq('game_type', gameType);
            }
            const { count: higherRankedUsers, error: rankError } = await userRankQuery;

            if (rankError) {
              console.error('Error getting user rank:', rankError);
            }

            // Add current user's entry with their actual rank
            processedData.push({
              username: currentUserData.users?.username || undefined,
              profit_loss: profitLossValue,
              rank: higherRankedUsers ?? 0,
              is_current_user: true,
            });

            // Re-sort the array by profit/loss
            processedData.sort((a, b) => b.profit_loss - a.profit_loss);

            // Update ranks for the displayed entries
            processedData.forEach((entry, index) => {
              if (!entry.is_current_user) {
                entry.rank = index + 1;
              }
            });
          }
        }
      }

      return NextResponse.json(processedData);
    }
  } catch (error) {
    console.error('Unexpected error in leaderboard API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
