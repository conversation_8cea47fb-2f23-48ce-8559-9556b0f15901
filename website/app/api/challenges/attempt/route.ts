import { verifyToken } from '@/lib/server/auth';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const attemptChallengeSchema = z.object({
  id: z.number().int().nonnegative(),
});

export async function POST(request: Request) {
  try {
    // --- validate
    const { walletAddress, error: tokenError } = await verifyToken();
    if (tokenError) {
      return NextResponse.json({ error: tokenError.message }, { status: 401 });
    }

    // --- validate body
    const body = await request.json();
    const attemptChallengeValidation = attemptChallengeSchema.safeParse(body);
    if (!attemptChallengeValidation.success) {
      return NextResponse.json(
        { error: attemptChallengeValidation.error.message },
        { status: 400 }
      );
    }
    const { id: challengeId } = attemptChallengeValidation.data;

    // --- get challenge details
    const { data: challenge, error: challengeError } = await supabaseServerClient
      .from('challenges')
      .select('*')
      .eq('id', challengeId)
      .single();
    if (challengeError) {
      return NextResponse.json({ error: 'Challenge not found' }, { status: 404 });
    }

    // --- reject permanent challenges
    if (challenge.type === 'permanent') {
      return NextResponse.json(
        { error: 'Permanent challenges cannot be attempted via API' },
        { status: 400 }
      );
    }

    if (!challenge.published) {
      return NextResponse.json({ error: 'Challenge is not published' }, { status: 400 });
    }

    // --- get user id
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    const userId = user.id;

    // --- create challenge submission
    const expiresAt3SecondsFromNow = new Date(Date.now() + 3000);
    const { error: submissionError } = await supabaseServerClient
      .from('challenge_submissions')
      .insert({
        challenge_id: challengeId,
        user_id: userId,
        status: 'pending',
        expires_at: expiresAt3SecondsFromNow.toISOString(),
      });
    if (submissionError) {
      return NextResponse.json({ error: submissionError.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Challenge submission created' }, { status: 200 });
  } catch (error) {
    console.error('Error in POST /api/challenges/attempt:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
