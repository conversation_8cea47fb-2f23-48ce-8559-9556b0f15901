import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import camelcaseKeys from 'camelcase-keys';
import { z } from 'zod';
import snakecaseKeys from 'snakecase-keys';
import { isAdmin } from '@/lib/utils';

export async function GET(request: Request) {
  try {
    // --- validate mode
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('mode') || 'user';
    if (mode !== 'user' && mode !== 'admin') {
      return NextResponse.json({ error: 'Invalid mode' }, { status: 400 });
    }

    // --- verify token (optional for basic challenge info)
    const { walletAddress, error: tokenError } = await verifyToken();
    const isAuthenticated = !tokenError && walletAddress;

    // --- if not authenticated, return basic challenge info without user status
    if (!isAuthenticated) {
      if (mode === 'admin') {
        return NextResponse.json(
          { error: 'Authentication required for admin mode' },
          { status: 401 }
        );
      }

      // Return basic published challenges without user status
      const { data: challenges, error: dbError } = await supabaseServerClient
        .from('challenges')
        .select('*')
        .eq('published', true);

      if (dbError) {
        console.error('Error fetching challenges:', dbError);
        return NextResponse.json({ error: 'Failed to fetch challenges' }, { status: 500 });
      }

      const basicChallenges = challenges.map((challenge) => ({
        ...challenge,
        status: 'incomplete', // Default status for unauthenticated users
      }));

      return NextResponse.json(camelcaseKeys(basicChallenges, { deep: true }), {
        status: 200,
      });
    }

    // --- get user id for authenticated users
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id, role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
    }
    const userId = user.id;

    // --- validate mode authorization
    if (mode === 'admin' && !isAdmin(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // --- get published challenges with user status
    const query = supabaseServerClient
      .from('challenges')
      .select('*, challenge_submissions(status)')
      .eq('challenge_submissions.user_id', userId);
    if (mode === 'user') {
      query.eq('published', true);
    }
    const { data: challenges, error: dbError } = await query;
    if (dbError) {
      console.error('Error fetching challenges:', dbError);
      return NextResponse.json({ error: 'Failed to fetch challenges' }, { status: 500 });
    }
    const flattenedChallenges = challenges.map((challenge) => {
      return {
        ...challenge,
        status: challenge.challenge_submissions[0]?.status || 'incomplete',
      };
    });

    // Ensure challenges is not null before passing to camelcaseKeys, or provide a default
    const responseData = flattenedChallenges
      ? camelcaseKeys(flattenedChallenges, { deep: true })
      : [];

    return NextResponse.json(responseData, {
      status: 200,
    });
  } catch (error) {
    console.error('Error in GET /api/challenges:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

const createChallengeSchema = z.array(
  z.object({
    title: z.string(),
    description: z.string().optional(),
    points: z.number(),
    published: z.boolean(),
    link: z.string().optional(),
    type: z.enum(['standard', 'permanent', 'abstractUpvote']),
    icon: z.string().optional(),
    gameType: z.string().nullable().optional(), // null ⇒ challenge appears in all games
  })
);

export async function POST(request: Request) {
  try {
    // --- validate
    const { walletAddress, error: tokenError } = await verifyToken();
    if (tokenError) {
      return NextResponse.json({ error: tokenError.message }, { status: 401 });
    }

    // --- validate body
    const body = await request.json();
    const challengesValidation = createChallengeSchema.safeParse(body);
    if (!challengesValidation.success) {
      return NextResponse.json({ error: challengesValidation.error.message }, { status: 400 });
    }
    let challenges = challengesValidation.data;

    // Validate that non-abstractUpvote challenges have links
    for (const challenge of challenges) {
      if (challenge.type !== 'abstractUpvote' && !challenge.link) {
        return NextResponse.json(
          { error: `Link is required for ${challenge.type} challenges` },
          { status: 400 }
        );
      }
    }

    // Auto-populate link for abstractUpvote challenges
    const processedChallenges = challenges.map((challenge) => {
      if (challenge.type === 'abstractUpvote') {
        return { ...challenge, link: 'https://portal.abs.xyz/vote' };
      }
      return challenge as typeof challenge & { link: string };
    });

    // --- validate user is admin
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
    }
    if (!isAdmin(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // --- convert challenges to snake case for database insertion
    const snakeCaseChallenges = processedChallenges.map((challenge) =>
      snakecaseKeys(challenge, { deep: true })
    );

    // --- create challenges
    const { data: createdChallenges, error: createError } = await supabaseServerClient
      .from('challenges')
      .insert(snakeCaseChallenges)
      .select();
    if (createError) {
      console.error('Error creating challenges:', createError);
      return NextResponse.json({ error: 'Failed to create challenges' }, { status: 500 });
    }

    return NextResponse.json(camelcaseKeys(createdChallenges, { deep: true }));
  } catch (error) {
    console.error('Error in POST /api/challenges:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

const updateChallengeSchema = z.object({
  id: z.number().nonnegative(),
  title: z.string().optional(),
  description: z.string().optional(),
  points: z.number().optional(),
  published: z.boolean().optional(),
  link: z.string().optional(),
  type: z.enum(['standard', 'permanent', 'abstractUpvote']).optional(),
  icon: z.string().optional(),
  gameType: z.string().nullable().optional(), // null ⇒ challenge appears in all games
});

export async function PATCH(request: Request) {
  try {
    // --- validate
    const { walletAddress, error: tokenError } = await verifyToken();
    if (tokenError) {
      return NextResponse.json({ error: tokenError.message }, { status: 401 });
    }

    // --- validate body
    const body = await request.json();
    const challengeValidation = updateChallengeSchema.safeParse(body);
    if (!challengeValidation.success) {
      return NextResponse.json({ error: challengeValidation.error.message }, { status: 400 });
    }
    let challenge = challengeValidation.data;

    // Auto-populate link for abstractUpvote challenges
    if (challenge.type === 'abstractUpvote') {
      challenge = { ...challenge, link: 'https://portal.abs.xyz/vote' };
    }

    // --- validate user is admin
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
    }
    if (!isAdmin(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // --- convert challenge to snake case and remove undefined values
    const snakeCaseChallenge = snakecaseKeys(challenge, { deep: true });
    Object.keys(snakeCaseChallenge).forEach((key) => {
      if (
        key === 'id' ||
        snakeCaseChallenge[key as keyof typeof snakeCaseChallenge] === undefined ||
        snakeCaseChallenge[key as keyof typeof snakeCaseChallenge] === null
      ) {
        delete snakeCaseChallenge[key as keyof typeof snakeCaseChallenge];
      }
    });

    const { data: updatedChallenge, error: updateError } = await supabaseServerClient
      .from('challenges')
      .update(snakeCaseChallenge)
      .eq('id', challenge.id)
      .select()
      .single();
    if (updateError) {
      console.error('Error updating challenge:', updateError);
      return NextResponse.json({ error: 'Failed to update challenge' }, { status: 500 });
    }

    return NextResponse.json(camelcaseKeys(updatedChallenge, { deep: true }));
  } catch (error) {
    console.error('Error in PATCH /api/challenges:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

const deleteChallengeSchema = z.object({
  id: z.number().nonnegative(),
});
export async function DELETE(request: Request) {
  try {
    // --- validate
    const { walletAddress, error: tokenError } = await verifyToken();
    if (tokenError) {
      return NextResponse.json({ error: tokenError.message }, { status: 401 });
    }

    // --- validate body
    const body = await request.json();
    const challengeValidation = deleteChallengeSchema.safeParse(body);
    if (!challengeValidation.success) {
      return NextResponse.json({ error: challengeValidation.error.message }, { status: 400 });
    }
    const challenge = challengeValidation.data;

    // --- validate user is admin
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
    }
    if (!isAdmin(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // --- delete challenge
    const { error: deleteError } = await supabaseServerClient
      .from('challenges')
      .delete()
      .eq('id', challenge.id);
    if (deleteError) {
      console.error('Error deleting challenge:', deleteError);
      return NextResponse.json({ error: 'Failed to delete challenge' }, { status: 500 });
    }

    return NextResponse.json({});
  } catch (error) {
    console.error('Error in DELETE /api/challenges:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
