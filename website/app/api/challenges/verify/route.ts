import { verifyToken } from '@/lib/server/auth';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const verifyChallengeSchema = z.object({
  id: z.number().int().nonnegative(),
});

export async function POST(request: Request) {
  let userId: string = '';
  try {
    // --- validate token
    const { walletAddress, error: tokenError } = await verifyToken();
    if (tokenError) {
      return NextResponse.json({ error: tokenError.message }, { status: 401 });
    }

    // --- validate body
    const body = await request.json();
    const verifyChallengeValidation = verifyChallengeSchema.safeParse(body);
    if (!verifyChallengeValidation.success) {
      return NextResponse.json({ error: verifyChallengeValidation.error.message }, { status: 400 });
    }
    const { id: challengeId } = verifyChallengeValidation.data;

    // --- get user id
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    userId = user.id;

    // --- get challenge with submission status using join
    const { data: challengeData, error: challengeError } = await supabaseServerClient
      .from('challenges')
      .select('*, challenge_submissions(status, expires_at)')
      .eq('id', challengeId)
      .eq('challenge_submissions.user_id', userId)
      .single();

    if (challengeError) {
      return NextResponse.json({ error: 'Challenge not found' }, { status: 404 });
    }
    const status = challengeData.challenge_submissions[0]?.status || 'incomplete';
    if (status === 'completed') {
      return NextResponse.json({ error: 'Challenge already completed' }, { status: 400 });
    }

    // --- get lock
    const { error: lockError } = await supabaseServerClient.rpc('get_user_lock', {
      p_user_id: userId,
      p_type: 'challenge_verify',
    });
    if (lockError) {
      console.error('Error getting lock:', lockError);
      return NextResponse.json(
        { error: 'verifying challenge already in progress' },
        { status: 400 }
      );
    }

    // --- verify challenge
    let completed = false;
    switch (challengeData.type) {
      case 'standard': {
        const currentTime = new Date().getTime();
        const expiresAt = challengeData.challenge_submissions?.[0]?.expires_at;
        if (!expiresAt || new Date(expiresAt).getTime() > currentTime) {
          return handleCleanup(
            userId,
            NextResponse.json({ error: 'Challenge not verified yet' }, { status: 400 })
          );
        }
        completed = true;
        break;
      }
      case 'abstractUpvote': {
        const provider = await getAbstractProvider(walletAddress, null);
        const { hasVoted, error: hasVotedError } = await provider.hasVotedForApp(
          walletAddress,
          (challengeData as any).game_type
        );
        if (hasVotedError) {
          return handleCleanup(
            userId,
            NextResponse.json({ error: hasVotedError.message }, { status: 500 })
          );
        }
        if (!hasVoted) {
          return handleCleanup(
            userId,
            NextResponse.json({ error: 'User has not voted for app' }, { status: 400 })
          );
        }
        completed = true;
        break;
      }
      case 'permanent':
        break;
      default:
        return NextResponse.json({ error: 'Invalid challenge type' }, { status: 400 });
    }

    // --- update user points
    if (completed) {
      const { error: updateError } = await supabaseServerClient.rpc('process_challenge', {
        challenge_id: challengeData.id,
        user_id: userId,
      });
      if (updateError) {
        return handleCleanup(
          userId,
          NextResponse.json({ error: updateError.message }, { status: 500 })
        );
      }
    }

    return handleCleanup(userId, NextResponse.json({ completed }));
  } catch (error) {
    console.error('Error in POST /api/challenges/verify:', error);
    return handleCleanup(
      userId,
      NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    );
  }
}

const handleCleanup = async (
  userId: string,
  response: NextResponse<{ completed: boolean } | { error: string }>
) => {
  if (userId) {
    const { error: unlockError } = await supabaseServerClient
      .from('locks')
      .delete()
      .eq('user_id', userId)
      .eq('type', 'challenge_verify');
    if (unlockError) {
      console.error('Error unlocking:', unlockError);
    }
  }
  return response;
};
