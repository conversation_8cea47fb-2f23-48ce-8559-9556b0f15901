import { NextResponse } from 'next/server';
import { getEffectiveBalance } from '@/lib/utils/gameBalance';
import { GAME_TYPES, GameType } from '@/lib/constants';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';

type Params = Promise<{ gameType: string }>;

export async function GET(request: Request, { params }: { params: Params }) {
  try {
    const { gameType } = await params;

    // --- validate game type
    if (!GAME_TYPES.includes(gameType as GameType)) {
      return NextResponse.json({ error: 'Invalid game type' }, { status: 400 });
    }

    // --- get effective balance
    const provider = await getAbstractProvider('', null);
    const { effectiveBalance, hasMaxBalance, error } = await getEffectiveBalance(
      gameType as GameType,
      provider
    );
    if (error) {
      console.error('Error getting effective balance:', error);
      return NextResponse.json({ error: 'Failed to get game balance' }, { status: 500 });
    }

    return NextResponse.json({
      effectiveBalance: effectiveBalance.toString(),
      hasMaxBalance,
      gameType,
    });
  } catch (error) {
    console.error('Error in GET /api/game-balance/[gameType]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
