import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { IS_MAINNET, TESTNET_CLAIM_AMOUNT, TESTNET_CLAIM_THRESHOLD } from '@/lib/constants';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';

export async function POST() {
  try {
    // Only allow on testnet
    if (IS_MAINNET) {
      return NextResponse.json({ error: 'Testnet claim not enabled' }, { status: 403 });
    }
    const { walletAddress, error } = await verifyToken();
    if (error || !walletAddress) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Fetch user
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id, testnet_claim_status, app')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    if (user.testnet_claim_status) {
      return NextResponse.json({ error: 'Already claimed' }, { status: 403 });
    }
    // Check on-chain balance
    const provider = await getAbstractProvider(walletAddress, null);
    const { balance, error: balanceError } = await provider.getWalletBalance(
      walletAddress as `0x${string}`
    );
    if (balanceError) {
      return NextResponse.json({ error: balanceError.message }, { status: 500 });
    }
    if (balance >= TESTNET_CLAIM_THRESHOLD) {
      return NextResponse.json(
        { error: `Wallet balance must be less than ${TESTNET_CLAIM_THRESHOLD} ETH` },
        { status: 403 }
      );
    }

    const { txHash, error: txHashError } = await provider.withdrawFunds(
      walletAddress as `0x${string}`,
      TESTNET_CLAIM_AMOUNT
    );
    if (txHashError) {
      return NextResponse.json({ error: txHashError.message }, { status: 500 });
    }

    const { receipt, error: receiptError } = await provider.waitForTransactionReceipt(
      txHash as `0x${string}`
    );
    if (receiptError) {
      return NextResponse.json({ error: receiptError.message }, { status: 500 });
    }
    if (receipt?.status !== 'success') {
      return NextResponse.json({ error: 'Transaction failed' }, { status: 500 });
    }

    // Update user
    const { error: updateError } = await supabaseServerClient
      .from('users')
      .update({ testnet_claim_status: true })
      .eq('id', user.id);
    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, txHash });
  } catch (err: any) {
    return NextResponse.json({ error: err.message || 'Internal server error' }, { status: 500 });
  }
}
