import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { keccak256, Hex, toBytes, formatEther, Address } from 'viem';
import { z } from 'zod';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { createCommitmentHash } from '@/lib/utils/provably-fair';
import { generateRowsFromSeedAndConfig } from '@/lib/utils/game';
import { verifyToken } from '@/lib/server/auth';
import superjson from 'superjson';
import {
  DEATH_RACE_MAX_BET_PERCENTAGE,
  DEATH_FUN_MIN_BET_ETH,
  DEATH_RACE_MIN_TILES,
  DEATH_RACE_MAX_TILES,
  ALGO_VERSION,
  REFERRAL_PERCENTAGE,
  DEATH_RACE_TOTAL_ROWS,
  LASER_PARTY_GAME_TYPE,
  DEATH_RACE_GAME_TYPE,
  GAME_TYPES,
  LP_MIN_GRID_SIZE,
  LP_MAX_GRID_SIZE,
} from '@/lib/constants';
import { generateLaserPartyRowConfig } from '@/lib/utils/game';
import { decimalToFraction } from '@/lib/utils';
import { analytics, setUserProfile } from '@/lib/server/analytics';
import { waitUntil } from '@vercel/functions';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { SessionConfig } from '@abstract-foundation/agw-client/sessions';
import { ABSTRACT_OLD_CONTRACT_ADDRESS } from '@/lib/server/constants';
import { InsufficientBalanceError } from '@/lib/server/errors';
import { getEffectiveBalance } from '@/lib/utils/gameBalance';

// --- Helper Functions ---

// Helper function to handle referral bonus creation with error handling
async function createReferralBonus(
  gameId: string,
  user: { id: string; referred_by: string | null },
  betAmount: bigint
) {
  // Try to get custom referral percentage, fall back to default if it fails
  let referralPercentage = REFERRAL_PERCENTAGE;
  try {
    const { data: referrerData } = await supabaseServerClient
      .from('users')
      .select('custom_referral_percentage')
      .eq('id', user.referred_by!)
      .single();

    // Use custom percentage if set, otherwise keep default
    referralPercentage = referrerData?.custom_referral_percentage ?? REFERRAL_PERCENTAGE;
  } catch (error) {
    console.error('Error querying custom referral percentage, using default:', error);
  }

  // Calculate bonus amount and insert referral bonus
  const bonusAmount = (betAmount * BigInt(Math.floor(referralPercentage * 1000))) / BigInt(1000);
  const { error: referralError } = await supabaseServerClient.from('referral_bonuses').insert({
    game_id: gameId,
    referred_id: user.id,
    referrer_id: user.referred_by!,
    bonus_amount: bonusAmount.toString(),
    status: 'unpaid',
  });

  if (referralError) {
    console.error('Error inserting referral bonus:', referralError);
  }
}

// --- Input Validation ---
const createAbstractGameSchema = z.discriminatedUnion('gameType', [
  z.object({
    gameType: z.literal(DEATH_RACE_GAME_TYPE),
    betAmount: z.bigint().min(DEATH_FUN_MIN_BET_ETH, {
      message: `Bet is below the minimum allowed bet of ${formatEther(DEATH_FUN_MIN_BET_ETH)} ETH.`,
    }),
    rowConfig: z
      .array(
        z
          .number()
          .int()
          .nonnegative()
          .min(DEATH_RACE_MIN_TILES, {
            message: `Each row must have at least ${DEATH_RACE_MIN_TILES} tiles.`,
          })
          .max(DEATH_RACE_MAX_TILES, {
            message: `Each row must have at most ${DEATH_RACE_MAX_TILES} tiles.`,
          })
      )
      .length(DEATH_RACE_TOTAL_ROWS, {
        message: `Must have exactly ${DEATH_RACE_TOTAL_ROWS} rows.`,
      }),
  }),
  z.object({
    gameType: z.literal(LASER_PARTY_GAME_TYPE),
    betAmount: z.bigint().min(DEATH_FUN_MIN_BET_ETH, {
      message: `Bet is below the minimum allowed bet of ${formatEther(DEATH_FUN_MIN_BET_ETH)} ETH.`,
    }),
    gridSize: z
      .number()
      .int()
      .min(LP_MIN_GRID_SIZE, {
        message: `Grid size must be at least ${LP_MIN_GRID_SIZE}.`,
      })
      .max(LP_MAX_GRID_SIZE, {
        message: `Grid size must be at most ${LP_MAX_GRID_SIZE}.`,
      })
      .optional()
      .default(10),
  }),
]);
type CreateAbstractGameBody = z.infer<typeof createAbstractGameSchema>;

export async function POST(
  request: Request
): Promise<NextResponse<{ hash: string } | { error: string }>> {
  let userId = '';
  let gameId = '';
  try {
    // --- verify token
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // --- parse game type from query paramsq
    const url = new URL(request.url);
    const gameTypeParam = url.searchParams.get('gameType') as (typeof GAME_TYPES)[number];
    if (!gameTypeParam || !GAME_TYPES.includes(gameTypeParam)) {
      return NextResponse.json({ error: 'gameType is required' }, { status: 400 });
    }

    // --- get user session config (fetch id and referred_by for referral logic)
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id, session_config, referred_by, username')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 401 });
    }
    const sessionConfig = user?.session_config;
    if (!sessionConfig) {
      return NextResponse.json({ error: 'No session config found' }, { status: 401 });
    }
    const sessionConfigParsed = superjson.parse<SessionConfig>(sessionConfig.toString());
    if (sessionConfigParsed.callPolicies[0].target === ABSTRACT_OLD_CONTRACT_ADDRESS) {
      return NextResponse.json({ error: 'new session config required' }, { status: 400 });
    }
    userId = user.id;

    // --- get user lock
    const { error: lockError } = await supabaseServerClient.rpc('get_user_lock', {
      p_user_id: user.id,
      p_type: 'create',
    });
    if (lockError) {
      console.error('Error locking game:', lockError);
      return NextResponse.json({ error: 'game creation in progress' }, { status: 400 });
    }

    // --- validate no pending_onchain, active, cashout_pending, cashout_failed games
    const { data: pendingGame, error: pendingGameError } = await supabaseServerClient
      .from('games')
      .select('id, status, bet_amount, rows, transaction_signature, release_version, game_type', {
        count: 'exact',
        // head: true,
      })
      .order('created_at', { ascending: false })
      .eq('wallet_address', walletAddress)
      .eq('game_type', gameTypeParam)
      .in('status', [
        'pending_onchain',
        'creation_failed',
        'active',
        'cashout_pending',
        'cashout_failed',
      ])
      .single();
    if (pendingGameError && pendingGameError.code !== 'PGRST116' && pendingGameError.message) {
      console.error('Error fetching pending games:', pendingGameError);
    }
    if (pendingGame && pendingGame.status !== 'creation_failed') {
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json(
          { error: `cannot create game while a game is in ${pendingGame.status} state` },
          { status: 400 }
        )
      );
    }

    let betAmount = BigInt(0);
    let rowConfig: { tiles: number; dimension?: 'col' | 'row' }[] = [];
    let gameType = '';

    if (pendingGame && pendingGame.status === 'creation_failed') {
      const {
        id: preliminaryGameId,
        bet_amount,
        rows,
        transaction_signature,
        release_version,
      } = pendingGame;
      betAmount = BigInt(bet_amount);
      gameId = preliminaryGameId;
      gameType = pendingGame.game_type;
      rowConfig = (rows as unknown as { tiles: number; dimension?: 'col' | 'row' }[]).map((r) => ({
        tiles: r.tiles,
        ...(gameType === LASER_PARTY_GAME_TYPE && r.dimension ? { dimension: r.dimension } : {}),
      }));

      console.log(`retrying creation of game ${preliminaryGameId}`);

      // --- update game record with status
      const { error: updateStatusError } = await supabaseServerClient
        .from('games')
        .update({ status: 'pending_onchain' })
        .eq('id', preliminaryGameId);
      if (updateStatusError) {
        console.error('Error updating game record:', updateStatusError);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: 'failed to update game record' }, { status: 500 })
        );
      }

      // --- enforce max bet ---
      const provider = await getAbstractProvider(walletAddress, sessionConfigParsed);
      const { effectiveBalance: potBalance, error: effectiveBalanceError } =
        await getEffectiveBalance(gameType as (typeof GAME_TYPES)[number], provider);
      if (effectiveBalanceError) {
        console.error('Error getting balance:', effectiveBalanceError);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: 'failed to get game balance' }, { status: 500 })
        );
      }
      const { numerator, denominator } = decimalToFraction(DEATH_RACE_MAX_BET_PERCENTAGE);
      const maxBetAmount = (potBalance * numerator) / denominator;
      if (betAmount > maxBetAmount) {
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json(
            { error: 'Bet exceeds the maximum allowed bet for the current pot.' },
            { status: 400 }
          )
        );
      }

      // --- update game record with pot balance
      const { error: updatePotBalanceError } = await supabaseServerClient
        .from('games')
        .update({
          pot_balance: potBalance.toString(),
          balance_expires_at: new Date(Date.now() + 30 * 1000).toISOString(),
        })
        .eq('id', preliminaryGameId);
      if (updatePotBalanceError) {
        console.error('Error updating pot balance:', updatePotBalanceError);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: 'failed to update pot balance' }, { status: 500 })
        );
      }

      // --- check if tx has been mined
      if (transaction_signature) {
        const { receipt, error: receiptError } = await provider.waitForTransactionReceipt(
          transaction_signature as `0x${string}`
        );
        if (!receiptError && receipt?.status === 'success') {
          // --- update game record
          const { onChainGameId, error: onChainGameIdError } = await provider.getOnChainGameId(
            preliminaryGameId,
            release_version
          );
          if (onChainGameIdError) {
            console.error('Error getting onchain game id:', onChainGameIdError);
            return handleCleanup(
              userId,
              gameId,
              NextResponse.json({ error: 'failed to get onchain game id' }, { status: 500 })
            );
          }
          const { error: updateError } = await supabaseServerClient
            .from('games')
            .update({
              onchain_game_id: onChainGameId.toString(),
              status: 'active',
            })
            .eq('id', preliminaryGameId);
          if (updateError) {
            console.error('Database error updating game record:', updateError);
            return handleCleanup(
              userId,
              gameId,
              NextResponse.json({ error: 'failed to update game record' }, { status: 500 })
            );
          }

          // --- update user streak (when game becomes active)
          waitUntil(
            (async () => {
              try {
                const { error: streakError } = await supabaseServerClient.rpc(
                  'update_user_streak',
                  {
                    p_wallet_address: walletAddress,
                  }
                );
                if (streakError) {
                  console.error('Error updating user streak:', streakError);
                }
              } catch (err) {
                console.error('Failed to update user streak:', err);
              }
            })()
          );

          // --- referral bonus logic ---
          if (user?.referred_by) {
            waitUntil(createReferralBonus(gameId, user, betAmount));
          }

          // --- analytics: send GAME_STARTED event ---
          waitUntil(
            (async () => {
              try {
                const { balance: walletBalance } = await provider.getWalletBalance(
                  walletAddress as Address
                );
                analytics.gameStarted(user.id, {
                  game_name: gameTypeParam,
                  betAmount: Number(formatEther(betAmount)),
                  wallet_balance: Number(formatEther(walletBalance)),
                  rowConfig,
                  preliminaryGameId,
                });
                if (user.username) {
                  setUserProfile(user.id, user.username, walletAddress);
                }
              } catch (err) {
                console.error('Failed to send GAME_STARTED analytics event:', err);
              }
            })()
          );

          return handleCleanup(
            userId,
            gameId,
            NextResponse.json({
              preliminaryGameId,
              hash: transaction_signature as `0x${string}`,
            })
          );
        }
      }

      // --- if tx is not mined, delete game record
      const { error: deleteError } = await supabaseServerClient
        .from('games')
        .delete()
        .eq('id', preliminaryGameId);
      if (deleteError) {
        console.error('Error deleting game record:', deleteError);
      }
      console.log(`tx not mined, deleted game ${preliminaryGameId}`);
    }

    // --- parse and validate body
    if (!betAmount || !rowConfig) {
      const rawBody = await request.text();
      const parsedBody = superjson.parse<CreateAbstractGameBody>(rawBody);
      const validation = createAbstractGameSchema.safeParse({
        ...parsedBody,
        gameType: gameTypeParam,
      });
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid input', details: validation.error.format() },
          { status: 400 }
        );
      }
      ({ betAmount, gameType } = validation.data);
      switch (validation.data.gameType) {
        case DEATH_RACE_GAME_TYPE: {
          const deathRaceBody = validation.data as Extract<
            CreateAbstractGameBody,
            { gameType: typeof DEATH_RACE_GAME_TYPE }
          >;
          rowConfig = deathRaceBody.rowConfig.map((tiles) => ({ tiles }));
          break;
        }
        case LASER_PARTY_GAME_TYPE: {
          const laserPartyBody = validation.data as Extract<
            CreateAbstractGameBody,
            { gameType: typeof LASER_PARTY_GAME_TYPE }
          >;
          const gridSize = laserPartyBody.gridSize || 10;
          rowConfig = generateLaserPartyRowConfig(gridSize);
          break;
        }
        default:
          return NextResponse.json({ error: 'Invalid game type' }, { status: 400 });
      }
    }

    // --- enforce max bet ---
    const provider = await getAbstractProvider(walletAddress, sessionConfigParsed);
    const { effectiveBalance: potBalance, error: effectiveBalanceError } =
      await getEffectiveBalance(gameType as (typeof GAME_TYPES)[number], provider);
    if (effectiveBalanceError) {
      console.error('Error getting balance:', effectiveBalanceError);
      return NextResponse.json({ error: 'failed to get game balance' }, { status: 500 });
    }
    const { numerator, denominator } = decimalToFraction(DEATH_RACE_MAX_BET_PERCENTAGE);
    const maxBetAmount = (potBalance * numerator) / denominator;
    if (betAmount > maxBetAmount) {
      return NextResponse.json(
        { error: 'Bet exceeds the maximum allowed bet for the current game balance.' },
        { status: 400 }
      );
    }

    // --- generate game id, seed, and hash
    const preliminaryGameId = uuidv4();
    const gameSeed = keccak256(toBytes(uuidv4())) as Hex;

    // --- generate rows & commitment hash
    const finalRowsWithDeathTiles = generateRowsFromSeedAndConfig(gameSeed, rowConfig);
    const commitmentHash = createCommitmentHash('v1', finalRowsWithDeathTiles, gameSeed);
    const rowsForStorage = finalRowsWithDeathTiles.map((row) => ({
      ...row,
      deathTileIndex: null, // Hide death tile index for storage
    }));

    // --- create game record
    const { error: dbError } = await supabaseServerClient.from('games').insert({
      id: preliminaryGameId,
      wallet_address: walletAddress,
      bet_amount: betAmount.toString(),
      status: 'pending_onchain',
      rows: rowsForStorage,
      commitment_hash: commitmentHash,
      game_seed: gameSeed,
      current_row_index: 0,
      pot_balance: potBalance.toString(),
      balance_expires_at: new Date(Date.now() + 30 * 1000).toISOString(), // 30 seconds from now
      algo_version: ALGO_VERSION,
      release_version: 'v2',
      game_type: gameType,
    });
    if (dbError) {
      console.error('Database error creating preliminary game record:', dbError);
      const userErrorMessage = dbError.message.includes(
        'duplicate key value violates unique constraint "games_pkey"'
      )
        ? 'Game ID conflict. Please try again.'
        : 'Failed to prepare game data.';
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json({ error: userErrorMessage }, { status: 500 })
      );
    }
    gameId = preliminaryGameId;

    // // --- fake failure
    // return handleCleanup(
    //   userId,
    //   gameId,
    //   NextResponse.json({ error: 'Transaction failed' }, { status: 500 })
    // );

    // await new Promise((resolve) => setTimeout(resolve, 3000)); // fake delay

    const { txHash, error: createGameError } = await provider.createGame({
      walletAddress,
      sessionConfig: sessionConfigParsed,
      preliminaryGameId,
      rowConfig: rowConfig.map(({ tiles }) => tiles),
      betAmount,
      commitmentHash,
      releaseVersion: 'v2',
    });
    if (createGameError || !txHash) {
      if (createGameError instanceof InsufficientBalanceError) {
        const { error: deleteError } = await supabaseServerClient
          .from('games')
          .delete()
          .eq('id', preliminaryGameId);
        if (deleteError) {
          console.error('Error deleting game record:', deleteError);
        }
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: 'Insufficient balance' }, { status: 400 })
        );
      }

      console.error('Error creating game:', createGameError);
      await handleKnownBlockchainErrors(userId, createGameError);
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json({ error: 'failed to create game' }, { status: 500 })
      );
    }

    // --- save tx hash to game record
    const { error: updateTxHashError } = await supabaseServerClient
      .from('games')
      .update({ transaction_signature: txHash })
      .eq('id', preliminaryGameId);
    if (updateTxHashError) {
      console.error('Database error updating game record:', updateTxHashError);
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json({ error: 'failed to save tx hash to game record' }, { status: 500 })
      );
    }

    // console.error('Transaction failed:', { receipt: 'fake reciept timeout' });
    // return handleCleanup(
    //   userId,
    //   gameId,
    //   NextResponse.json({ error: 'Transaction failed' }, { status: 500 })
    // );

    const { receipt, error: receiptError } = await provider.waitForTransactionReceipt(txHash);
    if (receiptError || !receipt || receipt?.status !== 'success') {
      console.error('Transaction failed:', receiptError);
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json({ error: 'Transaction failed' }, { status: 500 })
      );
    }

    // --- update game record
    const { onChainGameId, error: onChainGameIdError } = await provider.getOnChainGameId(
      preliminaryGameId,
      'v2'
    );
    if (onChainGameIdError) {
      console.error('Error getting onchain game id:', onChainGameIdError);
    }
    const { error: updateError } = await supabaseServerClient
      .from('games')
      .update({
        onchain_game_id: onChainGameId.toString(),
        transaction_signature: txHash,
        status: 'active',
      })
      .eq('id', preliminaryGameId);
    if (updateError) {
      console.error('Database error updating game record:', updateError);
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json({ error: 'failed to update game record' }, { status: 500 })
      );
    }

    // --- update user streak (when game becomes active)
    waitUntil(
      (async () => {
        try {
          const { error: streakError } = await supabaseServerClient.rpc('update_user_streak', {
            p_wallet_address: walletAddress,
          });
          if (streakError) {
            console.error('Error updating user streak:', streakError);
          }
        } catch (err) {
          console.error('Failed to update user streak:', err);
        }
      })()
    );

    // --- referral bonus logic ---
    if (user?.referred_by) {
      waitUntil(createReferralBonus(gameId, user, betAmount));
    }

    // --- analytics: send GAME_STARTED event ---
    waitUntil(
      (async () => {
        try {
          const { balance: walletBalance } = await provider.getWalletBalance(
            walletAddress as Address
          );
          analytics.gameStarted(user.id, {
            game_name: gameTypeParam,
            betAmount: Number(formatEther(betAmount)),
            wallet_balance: Number(formatEther(walletBalance)),
            rowConfig,
            preliminaryGameId,
          });
          if (user.username) {
            setUserProfile(user.id, user.username, walletAddress);
          }
        } catch (err) {
          console.error('Failed to send GAME_STARTED analytics event:', err);
        }
      })()
    );

    return handleCleanup(
      userId,
      gameId,
      NextResponse.json({
        preliminaryGameId,
        hash: txHash,
      })
    );
  } catch (error) {
    console.error('Error preparing Abstract game:', error);
    return handleCleanup(
      userId,
      gameId,
      NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    );
  }
}

const statusCodeToNOTUpdateGameStatus = [200, 400];

// --- helpers
const handleCleanup = async (
  userId: string,
  gameId: string,
  response: NextResponse<{ hash: string } | { error: string }>
) => {
  if (gameId && !statusCodeToNOTUpdateGameStatus.includes(response.status)) {
    const { error: updateError } = await supabaseServerClient
      .from('games')
      .update({ status: 'creation_failed' })
      .eq('id', gameId);
    if (updateError) {
      console.error('Error updating game record:', updateError);
    }
  }

  if (userId) {
    const { error: unlockError } = await supabaseServerClient
      .from('locks')
      .delete()
      .eq('user_id', userId)
      .eq('type', 'create');
    if (unlockError) {
      console.error('Error unlocking game', unlockError);
    }
  }

  return response;
};

const handleKnownBlockchainErrors = async (userId: string, error: Error | null) => {
  if (!error) return;
  if (error.message.includes('Session key validation failed')) {
    const { error: updateError } = await supabaseServerClient
      .from('users')
      .update({ session_config: null })
      .eq('id', userId);
    if (updateError) {
      console.error('Error updating user record:', updateError);
    }
  }
};
