import { NextResponse } from 'next/server';
import { parseEther } from 'viem';
import { isAddress } from 'viem';
import { verifyToken } from '@/lib/server/auth';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { isSuperAdmin } from '@/lib/utils';

export async function POST(request: Request) {
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error }, { status: 401 });
    }

    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    if (!isSuperAdmin(userData?.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { amountEth, recipientAddress } = body;

    // Validate recipient address
    if (!recipientAddress || !isAddress(recipientAddress)) {
      return NextResponse.json({ error: 'Invalid recipient address' }, { status: 400 });
    }

    // Validate amount
    if (!amountEth || isNaN(Number(amountEth)) || Number(amountEth) <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 });
    }

    // Convert ETH to wei
    const amountWei = parseEther(amountEth);
    const provider = await getAbstractProvider(walletAddress, null);

    const { txHash, error: withdrawError } = await provider.withdrawFunds(
      recipientAddress, // Pass recipient address
      amountWei
    );
    if (withdrawError) {
      return NextResponse.json({ error: withdrawError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, txHash: txHash });
  } catch (error: any) {
    console.error('[API] Error in /api/abstract/withdraw:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}
