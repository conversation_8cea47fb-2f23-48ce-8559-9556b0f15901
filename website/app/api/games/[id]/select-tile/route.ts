import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { calculateRowMultipliers, setDeathTile, calculatePoints } from '@/lib/utils/game';
import { DisplayRow, LPDisplayRow } from '@/components/death-race/types';
import { SelectTileResponse } from '@/lib/types/api';
import { verifyToken } from '@/lib/server/auth';
import { z } from 'zod';
import { CurrentGameServer, CurrentLaserPartyGameServer } from '@/lib/types/game';
import {
  LASER_PARTY_GAME_TYPE,
  DEATH_RACE_GAME_TYPE,
  DEATH_RACE_MAX_PROFIT_PERCENTAGE,
  GAME_TYPES,
} from '@/lib/constants';
import { decimalToFraction } from '@/lib/utils';
import { formatEther, Address } from 'viem';
import { waitUntil } from '@vercel/functions';
import { analytics } from '@/lib/server/analytics';
import { J<PERSON> } from '@/lib/types/database';
import superjson from 'superjson';
import { SessionConfig } from '@abstract-foundation/agw-client/sessions';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { getEffectiveBalance } from '@/lib/utils/gameBalance';

// --- Input Validation ---
const deathRaceSchema = z.object({
  game_type: z.literal(DEATH_RACE_GAME_TYPE),
  tileIndex: z.number().int().nonnegative(),
  version: z.number().int().nonnegative(),
});

const laserPartySchema = z.object({
  game_type: z.literal(LASER_PARTY_GAME_TYPE),
  cell: z.tuple([z.number().int().nonnegative(), z.number().int().nonnegative()]),
  version: z.number().int().nonnegative(),
});

const selectTileSchema = z.discriminatedUnion('game_type', [deathRaceSchema, laserPartySchema]);

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<SelectTileResponse | { error: string }>> {
  try {
    // --- verify token
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // --- validate request body
    const body = await request.json();
    const validation = selectTileSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Missing required fields', details: validation.error.format() },
        { status: 400 }
      );
    }
    const { game_type, version } = validation.data;

    // --- get game state
    const { id: gameId } = await params;
    const { data: games, error: fetchError } = await supabaseServerClient.rpc<
      'get_game_with_lock',
      { Args: { game_id: string }; Returns: CurrentGameServer[] }
    >('get_game_with_lock', { game_id: gameId });
    if (fetchError || !games || games.length === 0) {
      return NextResponse.json({ error: 'Game not found' }, { status: 404 });
    }
    const game = games[0];

    let tileIndex: number;
    let updatedSelectedTiles: number[] | number[][];
    switch (game_type) {
      case DEATH_RACE_GAME_TYPE: {
        tileIndex = validation.data.tileIndex;
        updatedSelectedTiles = [...((game.selected_tiles as number[]) || []), tileIndex];
        break;
      }
      case LASER_PARTY_GAME_TYPE: {
        const { cell } = validation.data;
        const currentDimension = (game as CurrentLaserPartyGameServer).rows[game.current_row_index]
          .dimension;
        tileIndex = currentDimension === 'row' ? cell[0] : cell[1];
        updatedSelectedTiles = [...((game.selected_tiles as number[][]) || []), cell];
        break;
      }
      default: {
        return NextResponse.json({ error: 'Invalid game type' }, { status: 400 });
      }
    }

    // --- calculate multipliers and set death tile if necessary
    const multipliers = calculateRowMultipliers(game.rows);
    let currentRow = game.rows[game.current_row_index];
    if (currentRow.deathTileIndex === null) {
      currentRow = setDeathTile(currentRow, game.game_seed!, game.current_row_index);
      game.rows[game.current_row_index] = currentRow;
    }

    // --- validate
    if (game.wallet_address !== walletAddress) {
      return NextResponse.json(
        { error: 'Unauthorized: wallet does not own this game' },
        { status: 403 }
      );
    }
    if (game.status !== 'active') {
      return NextResponse.json({ error: 'Game is not active' }, { status: 400 });
    }
    if (tileIndex < 0 || tileIndex >= currentRow.tiles) {
      return NextResponse.json({ error: 'Invalid tile index' }, { status: 400 });
    }
    if (game.payout_tx_signature) {
      return NextResponse.json(
        { error: 'Cannot select tile after cashout has been initiated' },
        { status: 400 }
      );
    }

    // --- max profit enforcement ---
    let potBalance = game.pot_balance ? BigInt(game.pot_balance) : null;
    const balanceExpiresAt = game.balance_expires_at ? new Date(game.balance_expires_at) : null;
    const currentTime = new Date();
    if (!potBalance || !balanceExpiresAt || balanceExpiresAt < currentTime) {
      const provider = await getAbstractProvider(walletAddress, null);
      const { effectiveBalance, error: effectiveBalanceError } = await getEffectiveBalance(
        game.game_type as (typeof GAME_TYPES)[number],
        provider
      );
      if (effectiveBalanceError) {
        console.error('Error getting effective balance:', effectiveBalanceError);
        return NextResponse.json({ error: 'Failed to get game balance' }, { status: 500 });
      }
      potBalance = effectiveBalance;

      waitUntil(
        (async (balance: bigint) => {
          const { error: updateError } = await supabaseServerClient
            .from('games')
            .update({
              pot_balance: balance.toString(),
              balance_expires_at: new Date(Date.now() + 30 * 1000).toISOString(),
            })
            .eq('id', gameId);
          if (updateError) {
            console.error('Error updating game:', updateError);
          }
        })(potBalance)
      );
    }
    const { numerator, denominator } = decimalToFraction(DEATH_RACE_MAX_PROFIT_PERCENTAGE);
    const maxPayoutAmount = (potBalance * numerator) / denominator;
    const nextMultiplier = multipliers[game.current_row_index];
    const potentialPayout = BigInt(Math.floor(Number(game.bet_amount) * nextMultiplier));
    if (maxPayoutAmount && potentialPayout > maxPayoutAmount) {
      return NextResponse.json(
        { error: 'Selecting this tile would exceed the max profit limit. Please cash out.' },
        { status: 400 }
      );
    }

    // --- check for death tile
    const isDeathTile = currentRow.deathTileIndex === tileIndex;
    if (isDeathTile) {
      const updatedRows = game.rows.map((row, idx) =>
        setDeathTile(row, game.game_seed!, idx)
      ) as Json & DisplayRow[];
      const { count, error: updateError } = await supabaseServerClient
        .from('games')
        .update(
          {
            status: 'lost',
            selected_tiles: updatedSelectedTiles,
            rows: updatedRows,
            final_multiplier: null,
            version: version + 1,
          },
          {
            count: 'exact',
          }
        )
        .eq('id', gameId)
        .eq('version', version);
      if (updateError) {
        console.error('Error updating game:', updateError);
        return NextResponse.json({ error: 'Failed to update game' }, { status: 500 });
      }
      if (!count) {
        return NextResponse.json({ error: 'Game already updated' }, { status: 404 });
      }

      // --- award points for bust (last completed row)
      const lastMultiplier = multipliers[game.current_row_index];
      const basePoints = calculatePoints(game.bet_amount, lastMultiplier);

      const { error: pointsError } = await supabaseServerClient.rpc(
        'increment_user_points_with_multiplier',
        {
          p_wallet_address: walletAddress,
          p_base_points: basePoints,
          p_source: 'gameplay',
          p_context: {
            game_id: gameId,
          },
        }
      );
      if (pointsError) {
        console.error('Error incrementing user points with multiplier:', pointsError);
      }

      if (game.release_version === 'v2') {
        waitUntil(
          (async () => {
            // Fetch user by wallet address to get user ID
            const { data: user } = await supabaseServerClient
              .from('users')
              .select('id, session_config')
              .eq('wallet_address', walletAddress)
              .single();
            if (!user) {
              console.error('User not found');
              return;
            }
            const { session_config } = user;
            if (!session_config) {
              console.error('Session config not found');
              return;
            }
            const sessionConfigParsed = superjson.parse<SessionConfig>(session_config as string);
            const provider = await getAbstractProvider(walletAddress, sessionConfigParsed);
            let currentOnchainGameId = BigInt(game.onchain_game_id!);
            if (currentOnchainGameId === BigInt(0)) {
              const { onChainGameId, error: onChainGameIdError } = await provider.getOnChainGameId(
                game.id,
                game.release_version
              );
              if (onChainGameIdError) {
                console.error('Error getting onchain game id:', onChainGameIdError);
                return;
              }
              currentOnchainGameId = onChainGameId;
              const { error: updateError } = await supabaseServerClient
                .from('games')
                .update({ onchain_game_id: currentOnchainGameId.toString() })
                .eq('id', gameId);
              if (updateError) {
                console.error('Error updating game:', updateError);
              }
            }
            const { error: markGameAsLostError } = await provider.markGameAsLost({
              walletAddress,
              sessionConfig: sessionConfigParsed,
              onChainGameId: currentOnchainGameId,
              selectedTiles: updatedSelectedTiles,
              gameSeed: game.game_seed!,
              releaseVersion: game.release_version,
              isServer: true,
            });
            if (markGameAsLostError) {
              console.error('Error marking game as lost:', markGameAsLostError);
            }
          })()
        );
      }

      // --- analytics: send GAME_BUSTED event ---
      waitUntil(
        (async () => {
          try {
            // Fetch user by wallet address to get user ID
            const { data: user } = await supabaseServerClient
              .from('users')
              .select('id')
              .eq('wallet_address', walletAddress)
              .single();
            const fallbackWallet = typeof walletAddress === 'string' ? walletAddress : '';
            const userId = typeof user?.id === 'string' ? user.id : fallbackWallet;
            const provider = await getAbstractProvider(walletAddress, null);
            const { balance: walletBalance } = await provider.getWalletBalance(
              walletAddress as Address
            );
            analytics.gameBusted(userId, {
              gameId,
              game_name: game.game_type,
              betAmount: Number(formatEther(BigInt(game.bet_amount))),
              wallet_balance: Number(formatEther(walletBalance)),
              rowNumber: Number(game.current_row_index),
              selectedTiles: updatedSelectedTiles,
              onchainGameId: Number(game.onchain_game_id),
              busted_on_row: Number(game.current_row_index),
            });
          } catch (err) {
            console.error('Failed to send GAME_BUSTED analytics event:', err);
          }
        })()
      );

      let nextRow: DisplayRow | null = null;
      if (game.current_row_index + 1 < game.rows.length) {
        nextRow = game.rows[game.current_row_index + 1];
      }
      return NextResponse.json({
        isDeathTile,
        currentRowIndex: game.current_row_index,
        finalMultiplier: null,
        status: 'lost',
        currentRow: currentRow,
        nextRow: nextRow,
      });
    }

    // --- check for game completion
    if (game.current_row_index === game.rows.length - 1) {
      const finalMultiplier = multipliers[game.current_row_index + 1];
      const updatedRows = game.rows.map((row, idx) =>
        setDeathTile(row, game.game_seed!, idx)
      ) as Json & DisplayRow[];
      const { count, error: updateError } = await supabaseServerClient
        .from('games')
        .update(
          {
            status: 'won',
            selected_tiles: updatedSelectedTiles,
            final_multiplier: finalMultiplier,
            rows: updatedRows,
            version: version + 1,
          },
          {
            count: 'exact',
          }
        )
        .eq('id', gameId)
        .eq('version', version);
      if (updateError) {
        console.error('Error updating game:', updateError);
        return NextResponse.json({ error: 'Failed to update game' }, { status: 500 });
      }
      if (!count) {
        return NextResponse.json({ error: 'Game already updated' }, { status: 404 });
      }

      return NextResponse.json({
        isDeathTile,
        currentRowIndex: game.current_row_index,
        finalMultiplier,
        status: 'won',
        currentRow: currentRow,
        nextRow: null,
      });
    }

    // --- proceed to next row
    const nextRowIndex = game.current_row_index + 1;
    const nextRow = game.rows[nextRowIndex];
    const { count, error: updateError } = await supabaseServerClient
      .from('games')
      .update(
        {
          current_row_index: nextRowIndex,
          rows: game.rows,
          selected_tiles: updatedSelectedTiles,
          final_multiplier: currentRow.multiplier,
          version: version + 1,
        },
        {
          count: 'exact',
        }
      )
      .eq('id', gameId)
      .eq('version', version);
    if (updateError) {
      console.error('Error updating game:', updateError);
      return NextResponse.json({ error: 'Failed to update game' }, { status: 500 });
    }
    if (!count) {
      return NextResponse.json({ error: 'Game already updated' }, { status: 404 });
    }

    return NextResponse.json({
      isDeathTile,
      currentRowIndex: nextRowIndex,
      finalMultiplier: currentRow.multiplier,
      status: 'active',
      currentRow: currentRow,
      nextRow: nextRow,
      version: version + 1,
    });
  } catch (error) {
    console.error('Error in POST /api/games/[id]/select-tile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
