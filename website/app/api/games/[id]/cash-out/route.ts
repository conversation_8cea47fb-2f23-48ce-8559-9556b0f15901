import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { calculateRowMultipliers, calculatePoints, setDeathTile } from '@/lib/utils/game';
import { CurrentGameServer } from '@/lib/types/game';
import { verifyToken } from '@/lib/server/auth';
import { Address, formatEther, Hash } from 'viem';
import { waitUntil } from '@vercel/functions';
import { analytics } from '@/lib/server/analytics';
import { getLogs } from '@/lib/utils/abstract/helpers';
import SuperJSON from 'superjson';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { SessionConfig } from '@abstract-foundation/agw-client/sessions';

type Params = Promise<{ id: string }>;

const handleCleanup = async (userId: string, gameId: string, response: NextResponse) => {
  if (gameId && response.status !== 200) {
    const { error: updateError } = await supabaseServerClient
      .from('games')
      .update({ status: 'cashout_failed' })
      .eq('id', gameId);
    if (updateError) {
      console.error('Error updating game status', updateError);
    }
  }

  if (userId) {
    const { error: unlockError } = await supabaseServerClient
      .from('locks')
      .delete()
      .eq('user_id', userId)
      .eq('type', 'cashout');
    if (unlockError) {
      console.error('Error unlocking game', unlockError);
    }
  }

  return response;
};

export async function POST(request: Request, { params }: { params: Params }) {
  let userWalletAddress = '';
  let userId = '';
  let gameId = '';
  try {
    // --- verify token
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }
    userWalletAddress = walletAddress;

    // --- get game
    const { id } = await params;
    gameId = id;
    const { data: game, error: fetchError } = await supabaseServerClient.rpc<
      'get_game_with_lock',
      {
        Args: { game_id: string };
        Returns: CurrentGameServer[];
      }
    >('get_game_with_lock', {
      game_id: gameId,
    });
    if (fetchError) {
      return NextResponse.json({ error: 'Game not found' }, { status: 404 });
    }
    const gameData = game[0];

    // --- validations
    if (gameData.wallet_address !== walletAddress) {
      return NextResponse.json(
        { error: 'Unauthorized: Mismatch between authenticated user and game owner' },
        { status: 403 }
      );
    }
    if (!['active', 'cashout_pending', 'cashout_failed'].includes(gameData.status)) {
      return NextResponse.json(
        { error: 'Game is not in a valid state for cashout' },
        { status: 400 }
      );
    }
    if (gameData.current_row_index === 0) {
      return NextResponse.json(
        { error: 'Cannot cash out before selecting the first tile' },
        { status: 400 }
      );
    }
    if (!gameData.rows || !Array.isArray(gameData.rows)) {
      return NextResponse.json({ error: 'Game data is corrupted: invalid rows' }, { status: 500 });
    }
    if (!gameData.onchain_game_id) {
      return NextResponse.json(
        { error: 'Game is missing onchain_game_id. Cannot cash out.' },
        { status: 400 }
      );
    }
    if (!gameData.selected_tiles || !Array.isArray(gameData.selected_tiles)) {
      return NextResponse.json(
        { error: 'Game is missing selected_tiles. Cannot cash out.' },
        { status: 400 }
      );
    }

    // --- get user
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id, session_config')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      if (userError.code === 'PGRST106') {
        console.error('User not found', userError);
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      console.error('Error fetching user', userError);
      return NextResponse.json({ error: 'Error fetching user' }, { status: 500 });
    }
    userId = user?.id;
    let sessionConfigParsed: SessionConfig | null = null;
    if (gameData.release_version === 'v2') {
      const sessionConfig = user?.session_config;
      if (!sessionConfig) {
        return NextResponse.json({ error: 'User has no session config' }, { status: 400 });
      }
      sessionConfigParsed = SuperJSON.parse<SessionConfig>(sessionConfig as string);
    }

    // --- lock game
    const { error: lockError } = await supabaseServerClient.rpc('get_lock', {
      p_user_id: userId,
      p_game_id: gameId,
      p_type: 'cashout',
      p_status: 'cashout_pending',
    });
    if (lockError) {
      console.error('Error locking game', lockError);
      return NextResponse.json({ error: 'cash out already in progress' }, { status: 400 });
    }

    // await new Promise((resolve) => setTimeout(resolve, 10000)); // fake delay

    // --- calculate payout amount
    const multipliers = calculateRowMultipliers(gameData.rows || []);
    const multiplierIndex = gameData.current_row_index - 1; // Cash out based on the *last completed* row
    const finalMultiplier = multipliers[multiplierIndex];
    const betAmountSmallestUnit = BigInt(gameData.bet_amount);
    const multiplierScaled = BigInt(Math.round(finalMultiplier * 10000)); // Scale multiplier for precision
    const scaleFactor = BigInt(10000);
    const payoutAmountSmallestUnit = (betAmountSmallestUnit * multiplierScaled) / scaleFactor;

    // --- cash out on chain
    const provider = await getAbstractProvider(walletAddress as Address, sessionConfigParsed);
    let currentOnchainGameId = BigInt(gameData.onchain_game_id);
    if (currentOnchainGameId === BigInt(0)) {
      // --- get onchain game id if missing
      const { onChainGameId, error: onChainGameIdError } = await provider.getOnChainGameId(
        gameData.id,
        gameData.release_version
      );
      if (onChainGameIdError) {
        console.error(`failed to get onchain game id: ${onChainGameIdError.message}`);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: 'Error getting onchain game id' }, { status: 500 })
        );
      }
      currentOnchainGameId = onChainGameId;
    }
    const { txHash, error: cashOutError } = await provider.cashOut({
      walletAddress: walletAddress as Address,
      sessionConfig: sessionConfigParsed,
      onChainGameId: currentOnchainGameId,
      payoutAmountWei: payoutAmountSmallestUnit,
      selectedTiles: gameData.selected_tiles,
      gameSeed: gameData.game_seed as string,
      releaseVersion: gameData.release_version,
      isServer: false,
    });
    if (cashOutError) {
      if (cashOutError.message.includes('GameNotActive')) {
        console.log(
          'game already cashed out, and tx signature exists, updating game status to won'
        );
      } else {
        console.error(`failed cash out: ${cashOutError.message}`);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json({ error: cashOutError.message }, { status: 500 })
        );
      }
    }

    let payoutTxSignature = gameData.payout_tx_signature;
    if (!payoutTxSignature) {
      const { txHash: localPayoutTxHash, error: payoutTxSignatureError } = await getLogs(
        currentOnchainGameId,
        new Date(gameData.created_at)
      );
      if (payoutTxSignatureError) {
        console.error(`failed to get payout tx signature: ${payoutTxSignatureError.message}`);
      }
      payoutTxSignature = localPayoutTxHash;
    }

    // return handleCleanup(
    //   userId,
    //   gameId,
    //   NextResponse.json({ error: 'cash out failed' }, { status: 500 })
    // );

    if (!cashOutError) {
      // --- update game with payout tx hash
      payoutTxSignature = txHash;

      // Wait for confirmation
      const { receipt, error: receiptError } = await provider.waitForTransactionReceipt(
        txHash as Hash
      );
      if (receiptError) {
        console.error(`failed waiting for tx receipt: ${receiptError.message}`);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json(
            { error: 'Error waiting for tx receipt: ' + receiptError.message },
            { status: 500 }
          )
        );
      }
      if (receipt && receipt.status !== 'success') {
        console.error(`[Abstract Service] cashOut tx failed or not confirmed:`, receipt);
        return handleCleanup(
          userId,
          gameId,
          NextResponse.json(
            { error: 'cashOut transaction failed or not confirmed.' },
            { status: 500 }
          )
        );
      }
    }

    // --- update game
    const updatedRows = gameData.rows.map((row, idx) =>
      setDeathTile(row, gameData.game_seed!, idx)
    );
    const { error: updateError } = await supabaseServerClient
      .from('games')
      .update({
        status: 'won',
        final_multiplier: finalMultiplier,
        updated_at: new Date().toISOString(),
        rows: JSON.parse(JSON.stringify(updatedRows)),
        onchain_game_id: currentOnchainGameId.toString(),
        payout_tx_signature: payoutTxSignature,
      })
      .eq('id', gameId);
    if (updateError) {
      console.error('Error in PATCH /api/games/[id]/cash-out:', updateError);
      return handleCleanup(
        userId,
        gameId,
        NextResponse.json(
          { error: 'Error in PATCH /api/games/[id]/cash-out: ' + updateError.message },
          { status: 500 }
        )
      );
    }

    // --- analytics: send GAME_CASHED_OUT event ---
    waitUntil(
      (async () => {
        try {
          // Fetch user by wallet address to get user ID
          const { data: user } = await supabaseServerClient
            .from('users')
            .select('id')
            .eq('wallet_address', walletAddress)
            .single();
          const fallbackWallet = typeof walletAddress === 'string' ? walletAddress : '';
          const userId = typeof user?.id === 'string' ? user.id : fallbackWallet;
          const { balance: walletBalance } = await provider.getWalletBalance(
            walletAddress as Address
          );
          analytics.gameCashedOut(userId, {
            gameId,
            game_name: gameData.game_type,
            payoutAmount: Number(formatEther(payoutAmountSmallestUnit)),
            betAmount: Number(formatEther(betAmountSmallestUnit)),
            wallet_balance: Number(formatEther(walletBalance)),
            finalMultiplier: Number(finalMultiplier),
            rowNumber: Number(gameData.current_row_index),
            onchainGameId: Number(gameData.onchain_game_id),
            payoutTxSignature: txHash,
            profit: Number(formatEther(payoutAmountSmallestUnit - betAmountSmallestUnit)),
          });
        } catch (err) {
          console.error('Failed to send GAME_CASHED_OUT analytics event:', err);
        }
      })()
    );

    // --- award points
    const basePoints = calculatePoints(gameData.bet_amount, finalMultiplier);
    const { error: incrementError } = await supabaseServerClient.rpc(
      'increment_user_points_with_multiplier',
      {
        p_user_id: userId,
        p_base_points: basePoints,
        p_source: 'gameplay',
        p_context: {
          game_id: gameId,
        },
      }
    );
    if (incrementError) {
      console.error('Error incrementing user points', incrementError);
    }

    return handleCleanup(
      userId,
      gameId,
      NextResponse.json({
        status: 'won',
        finalMultiplier,
        payoutAmount: payoutAmountSmallestUnit.toString(),
        payoutTxSignature: txHash,
      })
    );
  } catch (error) {
    console.error(`user: ${userWalletAddress} Error in PATCH /api/games/[id]/cash-out:`, error);
    return handleCleanup(
      userId,
      gameId,
      NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    );
  }
}
