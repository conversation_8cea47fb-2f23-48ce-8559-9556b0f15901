import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { <PERSON><PERSON> } from '@/lib/types/database';
import camelcaseKeys from 'camelcase-keys';
import { z } from 'zod';
import { GAME_TYPES } from '@/lib/constants';

const getActiveGameSchema = z.object({
  gameType: z.enum(GAME_TYPES),
});

export async function GET(request: Request) {
  try {
    // --- verify token
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // --- parse query params
    const { searchParams } = new URL(request.url);
    const gameTypeParam = searchParams.get('gameType');
    if (!gameTypeParam) {
      return NextResponse.json({ error: 'Game type is required' }, { status: 400 });
    }
    const { data: validation, error: validationError } = getActiveGameSchema.safeParse({
      gameType: gameTypeParam,
    });
    if (validationError) {
      return NextResponse.json({ error: validationError.message }, { status: 400 });
    }
    const { gameType } = validation;

    // Get most recent completed game for this app
    const { data: mostRecentGames, error: mostRecentError } = await supabaseServerClient
      .from('games')
      .select('*')
      .eq('wallet_address', walletAddress)
      .eq('game_type', gameType)
      .order('created_at', { ascending: false })
      .limit(2);

    if (mostRecentError) {
      console.error('Error fetching most recent games:', mostRecentError);
      return NextResponse.json({ error: 'Failed to fetch games' }, { status: 500 });
    }

    const currentGame = mostRecentGames?.[0] || undefined;
    const previousGame = mostRecentGames?.[1] || undefined;

    // Sanitize the current game's rows and game seed if it exists
    if (currentGame) {
      const isCompleted = ['won', 'lost', 'error'].includes(currentGame.status);

      // For active games, only show death tile for completed rows
      // For completed games, show all death tiles
      currentGame.rows = (currentGame.rows as Json[])?.map((row: any, index: number) => {
        if (isCompleted || index < currentGame.current_row_index) {
          return row; // Keep death tile for completed rows
        } else {
          return {
            ...row,
            deathTileIndex: null, // Hide death tile for future rows
          };
        }
      });

      // Only include game seed for completed games
      if (!isCompleted) {
        currentGame.game_seed = null;
      }

      // always hide pot balance
      currentGame.pot_balance = null;
      currentGame.balance_expires_at = null;
    }

    if (previousGame) {
      previousGame.pot_balance = null;
      previousGame.balance_expires_at = null;
    }

    const resp = camelcaseKeys(
      {
        currentGame,
        previousGame,
      },
      { deep: true }
    );

    return NextResponse.json(resp);
  } catch (error) {
    console.error('Error in GET /api/games/active:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
