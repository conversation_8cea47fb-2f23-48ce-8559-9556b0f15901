import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { z } from 'zod';
import superjson from 'superjson';
import camelcaseKeys from 'camelcase-keys';
import { verifyToken } from '@/lib/server/auth';
import { isAdmin } from '@/lib/utils';
import { GAME_TYPES } from '@/lib/constants';

const gameHistorySchema = z.object({
  walletAddress: z.string().optional().nullable(),
  username: z.string().optional().nullable(),
  page: z.number(),
  pageSize: z.number(),
  gameType: z.enum(GAME_TYPES).optional(),
});

const isAllowed = (
  userData: {
    username: string | null;
    wallet_address: string;
    role: string;
  },
  walletParam: string | null,
  usernameParam: string | null
) => {
  if (!userData) return false;

  if (isAdmin(userData.role)) return true;

  if (walletParam && walletParam !== userData.wallet_address) return false;
  if (usernameParam && usernameParam !== userData.username) return false;

  return true;
};

export async function GET(request: Request) {
  try {
    const { walletAddress: currentUserWalletAddress, error: currentUserError } =
      await verifyToken();
    if (currentUserError) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const walletAddressParam = searchParams.get('walletAddress');
    const usernameParam = searchParams.get('username');
    const pageParam = parseInt(searchParams.get('page') ?? '1');
    const pageSizeParam = parseInt(searchParams.get('pageSize') ?? '10');
    const gameIdParam = searchParams.get('gameId');
    const gameTypeParam = searchParams.get('gameType') ?? undefined;

    const { data, error: validationError } = gameHistorySchema.safeParse({
      walletAddress: walletAddressParam,
      username: usernameParam,
      page: pageParam,
      pageSize: pageSizeParam,
      gameType: gameTypeParam,
    });
    if (validationError) {
      return NextResponse.json({ error: validationError.message }, { status: 400 });
    }
    const {
      walletAddress: requestedWalletAddress,
      username: requestedUsername,
      page,
      pageSize,
      gameType,
    } = data;

    // --- get user record
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id,username, wallet_address, role')
      .eq('wallet_address', currentUserWalletAddress)
      .single();
    if (userError) {
      // Handle the case where the user doesn't exist yet
      if (userError.code === 'PGRST116') {
        // Return a 404 with a clear message instead of a 500 error
        return NextResponse.json(
          { error: 'User not found', message: 'User needs to be created first' },
          { status: 404 }
        );
      }

      console.error('Error fetching user record:', userError);
      return NextResponse.json({ error: 'Failed to fetch user record' }, { status: 500 });
    }
    if (!isAllowed(user, walletAddressParam, usernameParam)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let walletAddressToUse = requestedWalletAddress ?? currentUserWalletAddress;
    let userIdToUse = user.id;
    if (requestedUsername && requestedUsername !== user.username) {
      // --- get requested user record
      const { data: requestedUser, error: requestedUserError } = await supabaseServerClient
        .from('users')
        .select('id, wallet_address')
        .eq('username', requestedUsername)
        .single();
      if (requestedUserError) {
        console.error('Error fetching requested user record:', requestedUserError);
        return NextResponse.json(
          { error: 'Failed to fetch requested user record' },
          { status: 500 }
        );
      }
      walletAddressToUse = requestedUser.wallet_address;
      userIdToUse = requestedUser.id;
    }

    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Query only the needed fields for games, join username directly
    let gamesQuery = supabaseServerClient
      .from('games')
      .select(
        `
        id,
        wallet_address,
        bet_amount,
        status,
        rows,
        current_row_index,
        selected_tiles,
        final_multiplier,
        created_at,
        updated_at,
        payout_tx_signature,
        refund_tx_signature,
        game_seed,
        commitment_hash,
        onchain_game_id,
        algo_version,
        game_type,
        users:wallet_address(username)
      `,
        { count: 'exact' }
      )
      .eq('wallet_address', walletAddressToUse)
      .order('created_at', { ascending: false })
      .range(from, to);
    if (gameIdParam) {
      gamesQuery = gamesQuery.eq('onchain_game_id', gameIdParam);
    }
    if (gameType) {
      gamesQuery = gamesQuery.eq('game_type', gameType);
    }
    const { data: games, error, count } = await gamesQuery;
    if (error) {
      console.error('Error fetching game history:', error);
      return NextResponse.json({ error: 'Failed to fetch game history' }, { status: 500 });
    }

    const { data: stats, error: statsError } = await supabaseServerClient.rpc<
      'get_user_stats',
      {
        Args: { p_user_id: string; p_game_type: string | undefined };
        Returns: { total_games: number; total_bet_amount: string; total_profit_loss: string };
      }
    >('get_user_stats', {
      p_user_id: userIdToUse,
      p_game_type: gameType,
    });
    if (statsError) {
      console.error('Error fetching user stats:', statsError);
      return NextResponse.json({ error: 'Failed to fetch user stats' }, { status: 500 });
    }

    // Transform games to include username and conditionally remove game_seed
    const gamesWithUsername = (games || []).map((g) => {
      // Remove the nested users object and add username at top level
      const { users, game_seed, ...gameWithoutUsers } = g;
      const gameData = {
        ...gameWithoutUsers,
        username: users?.username || null,
        // Only include game_seed for completed games (won or lost)
        ...(g.status === 'won' || g.status === 'lost' ? { game_seed } : {}),
      };

      return gameData;
    });

    // Get the viewed user's username and wallet address
    let viewedUsername = null;
    let viewedWalletAddress = null;
    if (gamesWithUsername.length > 0) {
      viewedUsername = gamesWithUsername[0].username;
      viewedWalletAddress = gamesWithUsername[0].wallet_address;
    }

    const respBody = superjson.stringify(
      camelcaseKeys(
        {
          games: gamesWithUsername,
          totalGames: count ?? 0,
          totalPages: Math.ceil((count ?? 0) / pageSize),
          currentPage: page,
          stats: {
            totalGames: stats.total_games || 0,
            totalBetAmount: stats.total_bet_amount || '0',
            totalProfitLoss: stats.total_profit_loss || '0',
          },
          viewedUsername,
          viewedWalletAddress,
        },
        { deep: true }
      )
    );

    return new Response(respBody, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error in GET /api/games/history:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
