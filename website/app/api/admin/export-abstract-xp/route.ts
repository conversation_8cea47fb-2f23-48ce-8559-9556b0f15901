import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { isSuperAdmin } from '@/lib/utils';

interface GameStats {
  wallet_address: string;
  transactions: number;
  total_eth_bet: number;
}

export async function GET() {
  try {
    const { walletAddress, error: verifyError } = await verifyToken();
    if (verifyError) {
      return NextResponse.json({ error: verifyError.message }, { status: 401 });
    }

    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    if (!isSuperAdmin(userData?.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use the database function to get aggregated stats
    const { data: stats, error } = await supabaseServerClient.rpc('get_weekly_game_stats', {
      days_ago: 7,
    });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!stats) {
      return NextResponse.json({ error: 'No data found' }, { status: 404 });
    }

    // Create CSV content
    const csvRows = ['wallet_address,transactions,total_eth_bet'];

    (stats as unknown as GameStats[]).forEach((stat) => {
      csvRows.push(`${stat.wallet_address},${stat.transactions},${stat.total_eth_bet}`);
    });

    const csvContent = csvRows.join('\n');

    // Return CSV file
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="abstract-xp-export.csv"',
      },
    });
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json({ error: 'Failed to export data' }, { status: 500 });
  }
}
