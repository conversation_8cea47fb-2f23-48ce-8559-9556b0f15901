import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { formatEther } from 'viem';
import { AdminStats } from '@/lib/types/api';
import { isAdmin } from '@/lib/utils';
import { z } from 'zod';
import { GAME_TYPES } from '@/lib/constants';

const adminStatsSchema = z.object({
  gameType: z.enum(GAME_TYPES).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

export async function GET(request: Request): Promise<NextResponse<AdminStats | { error: string }>> {
  try {
    const { searchParams } = new URL(request.url);
    const { data: validationData, error: validationError } = adminStatsSchema.safeParse({
      gameType: searchParams.get('gameType') ?? undefined,
      startDate: searchParams.get('startDate') ?? undefined,
      endDate: searchParams.get('endDate') ?? undefined,
    });
    if (validationError) {
      return NextResponse.json({ error: validationError.message }, { status: 400 });
    }
    const { gameType, startDate, endDate } = validationData;

    const { walletAddress, error: verifyError } = await verifyToken();
    if (verifyError) {
      return NextResponse.json({ error: verifyError.message }, { status: 401 });
    }

    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    if (!isAdmin(userData?.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Call the get_admin_stats function to get all aggregated statistics
    const { data, error } = (await supabaseServerClient.rpc('get_admin_stats', {
      game_type: gameType,
      start_date: startDate ? new Date(startDate).toISOString() : undefined,
      end_date: endDate ? new Date(endDate).toISOString() : undefined,
    })) as {
      data: any;
      error: Error;
    };
    if (error) {
      console.error('Error fetching admin stats:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    const response = {
      totalGames: data.totalGames,
      totalVolume: formatEther(data.totalVolume),
      totalPayouts: formatEther(data.totalPayouts),
      totalUsers: data.totalUsers,
      treasuryDelta: formatEther(data.treasuryDelta),
      bustPercentage: data.bustPercentage,
      averageWinMultiplier: data.averageWinMultiplier,
      averageRowReached: data.averageRowReached,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json({ error: 'Failed to fetch admin stats' }, { status: 500 });
  }
}
