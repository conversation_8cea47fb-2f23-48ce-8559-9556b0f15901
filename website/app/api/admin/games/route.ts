import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import superjson from 'superjson';
import camelcaseKeys from 'camelcase-keys';
import { isAdmin } from '@/lib/utils';
import { z } from 'zod';
import { GAME_TYPES } from '@/lib/constants';

const getAdminGamesSchema = z.object({
  gameType: z.enum(GAME_TYPES).optional(), // optional
});

export async function GET(request: Request) {
  try {
    const { walletAddress, error: verifyError } = await verifyToken();
    if (verifyError) {
      console.error('verifyToken error:', verifyError);
      return NextResponse.json({ error: verifyError.message }, { status: 401 });
    }

    // Check admin role
    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('role')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      console.error('userError:', userError);
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }
    if (!isAdmin(userData?.role)) {
      console.error('Unauthorized: not admin');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Pagination and sorting
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') ?? '1');
    const pageSize = parseInt(searchParams.get('pageSize') ?? '15');
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortDirection = searchParams.get('sortDirection') === 'asc' ? 'asc' : 'desc';
    const gameTypeParam = searchParams.get('gameType') ?? undefined;

    // --- validate params
    const { data, error: validationError } = getAdminGamesSchema.safeParse({
      gameType: gameTypeParam,
    });
    if (validationError) {
      return NextResponse.json({ error: validationError.message }, { status: 400 });
    }
    const { gameType } = data;

    // Only allow sorting by created_at or profit (net)
    const validSortBy = ['created_at', 'profit', 'final_multiplier'];
    const sortKey = validSortBy.includes(sortBy) ? sortBy : 'created_at';

    // Query all games with username join
    let query = supabaseServerClient
      .from('games')
      .select('*, users:wallet_address(username)', { count: 'exact' })
      .not('status', 'eq', 'active');

    // Apply sorting at database level
    if (sortKey === 'created_at') {
      query = query.order('created_at', { ascending: sortDirection === 'asc' });
    } else if (sortKey === 'profit') {
      // Sort by 'net' field which represents house profit/loss
      query = query.order('net', { ascending: sortDirection === 'asc' });
    } else if (sortKey === 'final_multiplier') {
      // Sort by final_multiplier, with nulls last
      query = query.order('final_multiplier', {
        ascending: sortDirection === 'asc',
        nullsFirst: false,
      });
    }

    if (gameType) {
      query = query.eq('game_type', gameType);
    }

    const { data: games, error, count } = await query.range(from, to).returns<any[]>();

    if (error) {
      console.error('supabase games query error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Attach username to each game
    const gamesWithUsername = (games || []).map((game) => ({
      ...game,
      username: game.users?.username || undefined,
    }));

    const respBody = superjson.stringify(
      camelcaseKeys(
        {
          games: gamesWithUsername,
          totalGames: count ?? 0,
          totalPages: Math.ceil((count ?? 0) / pageSize),
          currentPage: page,
        },
        { deep: true }
      )
    );

    return new Response(respBody, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Unexpected error in /api/admin/games:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
