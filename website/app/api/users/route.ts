import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { APP_ID } from '@/lib/server/constants';
import camelcaseKeys from 'camelcase-keys';
import superjson from 'superjson';
import type { SessionConfig } from '@abstract-foundation/agw-client/sessions';
import { Database } from '@/lib/types/database';
import { z } from 'zod';
import { analytics, setUserProfile, setUserReferralInfo } from '@/lib/server/analytics';
import { waitUntil } from '@vercel/functions';
import { isHateSpeech } from '@/lib/utils/server/openaiModeration';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { formatEther, Address } from 'viem';

const validateUsername = (username: string, ctx: z.RefinementCtx): boolean => {
  if (!username || !username.trim()) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Username is required',
    });
    return false;
  }
  if (username.length > 12) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Username must be 12 characters or less',
    });
    return false;
  }
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Username can only contain letters, numbers, underscores, and dashes',
    });
    return false;
  }
  return true;
};

const userUpdateBodySchema = z.object({
  username: z.string().superRefine(validateUsername).optional(),
  sessionConfig: z.any(),
});

type UserUpdateBody = z.infer<typeof userUpdateBodySchema> & {
  sessionConfig?: SessionConfig;
};

export async function PATCH(request: Request) {
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    const rawBody = await request.text();
    const parsedBody = superjson.parse<UserUpdateBody>(rawBody);
    const body = userUpdateBodySchema.safeParse(parsedBody);
    if (!body.success) {
      return NextResponse.json({ error: body.error.message }, { status: 400 });
    }
    const { username, sessionConfig }: UserUpdateBody = body.data;

    if (username && (await isHateSpeech(username))) {
      return NextResponse.json({ error: 'Invalid username' }, { status: 400 });
    }

    const updatePayload: Database['public']['Tables']['users']['Update'] = {
      username: username,
    };
    if (sessionConfig) {
      updatePayload.session_config = superjson.stringify(sessionConfig);
    }
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .update(updatePayload)
      .eq('wallet_address', walletAddress)
      .eq('app', APP_ID)
      .select()
      .single();

    if (userError) {
      if (userError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      console.error('Error updating user:', userError);
      return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
    }

    // --- analytics: set Mixpanel user profile and send SAVED_USERNAME event ---
    waitUntil(
      (async () => {
        try {
          if (username) {
            setUserProfile(user.id, username, walletAddress);
          }
          analytics.savedUsername(user.id);
          if (sessionConfig) {
            analytics.createdSessionKey(user.id);
          }
        } catch (err) {
          console.error('Failed to send analytics events:', err);
        }
      })()
    );

    const resp = superjson.stringify(camelcaseKeys(user, { deep: true }));

    return new Response(resp, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error in PATCH /api/users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

const userCreateBodySchema = z.object({
  username: z.string().superRefine(validateUsername),
  referralCode: z.string().optional(),
  sessionConfig: z.any(),
});
type UserCreateBody = z.infer<typeof userCreateBodySchema> & {
  sessionConfig?: SessionConfig;
};

export async function POST(request: Request) {
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    const rawBody = await request.text();
    const parsedBody = superjson.parse<UserCreateBody>(rawBody);
    const body = userCreateBodySchema.safeParse(parsedBody);
    if (!body.success) {
      return NextResponse.json({ error: body.error.message }, { status: 400 });
    }
    const { referralCode, username, sessionConfig }: UserCreateBody = body.data;

    if (username && (await isHateSpeech(username))) {
      return NextResponse.json({ error: 'Invalid username' }, { status: 400 });
    }

    // Check if user exists for this specific app
    const { data: existingUser, error: fetchError } = await supabaseServerClient
      .from('users')
      .select('id, role')
      .eq('wallet_address', walletAddress)
      .eq('app', APP_ID) // Add app filter
      .single();

    if (existingUser) {
      return NextResponse.json(
        {
          error: 'User already exists',
        },
        { status: 400 }
      );
    }

    let userData: any = null;
    let userId: string | null = null;
    let userAction: string = existingUser ? 'Updated' : 'Created';

    // Generate referral code first
    const newReferralCode = await generateReferralCode();

    // Get referrer ID if referral code was provided
    let referrerId = null;
    let referrerUsername = null;
    if (referralCode) {
      const { data: referrer, error: referrerError } = await supabaseServerClient
        .from('users')
        .select('id, username')
        .eq('referral_code', referralCode)
        .eq('app', APP_ID) // Add app filter
        .single();

      if (referrerError) {
        console.error('Error looking up referrer:', referrerError);
      } else if (referrer) {
        referrerId = referrer.id;
        referrerUsername = referrer.username;
      }
    }

    const newUserPayload = {
      wallet_address: walletAddress,
      last_login_at: new Date().toISOString(),
      referral_code: newReferralCode,
      referred_by: referrerId,
      role: 'user' as const, // Default role for new users
      app: APP_ID, // Include app ID
      username: username,
    } as Database['public']['Tables']['users']['Insert'];

    if (sessionConfig) {
      newUserPayload.session_config = superjson.stringify(sessionConfig);
    }

    const { data: newUser, error: insertError } = await supabaseServerClient
      .from('users')
      .insert([newUserPayload]) // Use payload variable
      .select()
      .single();

    if (insertError) {
      console.error('[API Users POST] Error inserting new user:', insertError);
      // Rethrow the specific DB error
      throw new Error(`Database insert failed: ${insertError.message}`);
    }

    // Store the new user data
    userData = newUser;
    userId = newUser.id;
    userAction = 'Created';

    // --- analytics: send ACCOUNT_CREATED event ---
    waitUntil(
      (async () => {
        try {
          const provider = await getAbstractProvider(walletAddress, null);
          const { balance: walletBalance } = await provider.getWalletBalance(
            walletAddress as Address
          );
          analytics.accountCreated(newUser.id, {
            username,
            referred_by: referrerId,
            referrer_username: referrerUsername,
            wallet_balance: Number(formatEther(walletBalance)),
          });
          setUserProfile(newUser.id, username, walletAddress);
          // Set referral info only once when user is created (if they have a referrer)
          if (referrerId) {
            setUserReferralInfo(newUser.id, referrerId);
          }
          if (sessionConfig) {
            analytics.createdSessionKey(newUser.id, { username });
          }
        } catch (err) {
          console.error('Failed to send analytics events:', err);
        }
      })()
    );

    const resp = superjson.stringify(camelcaseKeys(userData, { deep: true }));

    return new Response(resp, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('--- POST /api/users Request End (Error) ---');
    // Log the specific error that was thrown
    console.error('[API Users POST] Caught Error:', error instanceof Error ? error.message : error);
    // Return a more specific error message if possible
    const errorMessage = error instanceof Error ? error.message : 'Failed to manage user account';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

async function generateReferralCode(): Promise<string> {
  const { data, error } = await supabaseServerClient.rpc('generate_referral_code');
  if (error) {
    console.error('Error generating referral code:', error);
    throw error;
  }
  return data;
}

export async function GET() {
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // Fetch user data for the specific app
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('*')
      .eq('wallet_address', walletAddress)
      .eq('app', APP_ID) // Add app filter
      .single();

    if (userError) {
      // Handle the case where the user doesn't exist yet
      if (userError.code === 'PGRST116') {
        // Return a 404 with a clear message instead of a 500 error
        return NextResponse.json(
          { error: 'User not found', message: 'User needs to be created first' },
          { status: 404 }
        );
      }

      return NextResponse.json({ error: 'Failed to fetch user data' }, { status: 500 });
    }

    const resp = superjson.stringify(camelcaseKeys(user, { deep: true }));

    return new Response(resp, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error in GET /api/users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
