import { NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { verifyToken } from '@/lib/server/auth';
import { formatEther } from 'viem';
import { MINIMUM_CLAIM_AMOUNT } from '@/lib/constants';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { SessionConfig } from '@abstract-foundation/agw-client/sessions';
import superjson from 'superjson';

// GET endpoint to fetch total unclaimed referral bonuses
export async function GET(request: Request) {
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // Get user's data for this app
    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('id')
      .eq('wallet_address', walletAddress)
      .single();

    if (userError) {
      if (userError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      console.error('Error fetching user data:', userError);
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }

    // Get total unclaimed bonuses for this app
    const { data: bonuses, error: bonusesError } = await supabaseServerClient
      .from('referral_bonuses')
      .select('bonus_amount, version')
      .eq('referrer_id', userData.id)
      .in('status', ['unpaid', 'failed', 'pending']);
    if (bonusesError) {
      console.error('Error fetching bonuses:', bonusesError);
      return NextResponse.json({ error: 'Failed to fetch bonuses' }, { status: 500 });
    }

    const total = bonuses.reduce((sum, bonus) => sum + BigInt(bonus.bonus_amount), BigInt(0));
    const version = bonuses.length > 0 ? bonuses[0].version : 0;

    return NextResponse.json({
      total: formatEther(total),
      version,
    });
  } catch (error) {
    console.error('Error in GET /api/users/referral-bonuses:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  let userId = '';
  try {
    const { walletAddress, error } = await verifyToken();
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    // Get user's data for this app
    const { data: userData, error: userError } = await supabaseServerClient
      .from('users')
      .select('id, session_config')
      .eq('wallet_address', walletAddress)
      .single();
    if (userError) {
      if (userError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      console.error('Error fetching user data:', userError);
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
    userId = userData.id;
    const { session_config: sessionConfig } = userData;
    let sessionConfigParsed = null;
    if (sessionConfig) {
      sessionConfigParsed = superjson.parse<SessionConfig>(sessionConfig as string);
    }

    // --- get lock
    const { error: lockError } = await supabaseServerClient.rpc('get_user_lock', {
      p_user_id: userData.id,
      p_type: 'referrals_claim',
    });
    if (lockError) {
      console.error('Error getting lock:', lockError);
      return NextResponse.json({ error: 'claiming bonuses already in progress' }, { status: 400 });
    }

    // Get pending bonuses for this app
    const { data: bonuses, error: bonusesError } = await supabaseServerClient
      .from('referral_bonuses')
      .select('id, bonus_amount, version')
      .eq('referrer_id', userData.id)
      .in('status', ['unpaid', 'pending', 'failed']);
    if (bonusesError) {
      console.error('Error fetching bonuses:', bonusesError);
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Failed to fetch bonuses' }, { status: 500 })
      );
    }
    if (!bonuses || bonuses.length === 0) {
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'No bonuses to claim' }, { status: 400 })
      );
    }
    const total = bonuses.reduce((sum, bonus) => sum + BigInt(bonus.bonus_amount), BigInt(0));
    if (total < MINIMUM_CLAIM_AMOUNT) {
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Bonuses are below minimum claim amount' }, { status: 400 })
      );
    }

    // mark bonuses as pending
    const { data: markPendingData, error: markPendingError } = await supabaseServerClient.rpc(
      'adjust_referral_bonuses_status',
      {
        p_ids: bonuses.map((b) => b.id),
        p_status: 'pending',
      }
    );
    if (markPendingError) {
      console.error('Error marking bonuses as pending:', markPendingError);
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Failed to mark bonuses as pending' }, { status: 500 })
      );
    }

    // -- send payout
    const provider = await getAbstractProvider(walletAddress, sessionConfigParsed);
    const { txHash, error: withdrawError } = await provider.payReferral(
      walletAddress as `0x${string}`,
      total
    );
    if (withdrawError) {
      console.error('Error paying referral:', withdrawError);

      const { error: updateError } = await supabaseServerClient
        .from('referral_bonuses')
        .update({ status: 'failed' })
        .in(
          'id',
          bonuses.map((b) => b.id)
        );
      if (updateError) {
        console.error('Error updating bonus status to failed', updateError);
      }

      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Failed to pay referral' }, { status: 500 })
      );
    }
    if (!txHash) {
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Failed to get transaction hash' }, { status: 500 })
      );
    }

    // mark bonuses as paid
    const { data: paidCount, error: markPaidError } = await supabaseServerClient.rpc(
      'adjust_referral_bonuses_status',
      {
        p_ids: bonuses.map((b) => b.id),
        p_status: 'paid',
        p_tx_hash: txHash,
      }
    );
    if (markPaidError) {
      console.error('Error marking bonuses as paid:', markPaidError);
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Failed to mark bonuses as paid' }, { status: 500 })
      );
    }
    if (paidCount !== bonuses.length) {
      return handleCleanup(
        userId,
        NextResponse.json({ error: 'Some bonuses were already paid' }, { status: 400 })
      );
    }

    return handleCleanup(
      userId,
      NextResponse.json({
        success: true,
        amount: formatEther(total),
        transactionSignature: txHash || '',
      })
    );
  } catch (error) {
    console.error('Error in POST /api/users/referral-bonuses:', error);
    return handleCleanup(
      userId,
      NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    );
  }
}

const handleCleanup = async (
  userId: string,
  response: NextResponse<
    { success: boolean; amount: string; transactionSignature: string } | { error: string }
  >
) => {
  if (userId) {
    const { error: unlockError } = await supabaseServerClient
      .from('locks')
      .delete()
      .eq('user_id', userId)
      .eq('type', 'referrals_claim');
    if (unlockError) {
      console.error('Error unlocking:', unlockError);
    }
  }
  return response;
};
