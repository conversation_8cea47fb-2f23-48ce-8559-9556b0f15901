'use client';

import { useSearchParamsContext } from '@/lib/context/ViewContext';
import { Suspense } from 'react';

import { Navigation } from '@/components/ui/Navigation';

function NotFoundContent() {
  const { getParam } = useSearchParamsContext();
  const from = getParam('from');

  return (
    <Navigation>
      <div className='flex-1 overflow-auto'>
        <div className='container mx-auto py-6 sm:py-12 px-4 text-white'>
          <div className='text-center'>
            <h1 className='text-3xl font-bold mb-6'>Page Not Found</h1>
            <p className='mb-6'>
              {from
                ? `The page "${from}" was not found.`
                : 'The page you are looking for does not exist.'}
            </p>
          </div>
        </div>
      </div>
    </Navigation>
  );
}

export default function NotFound() {
  return (
    <Suspense fallback={<div></div>}>
      <NotFoundContent />
    </Suspense>
  );
}
