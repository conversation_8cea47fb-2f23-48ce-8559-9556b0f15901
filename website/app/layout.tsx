import { ColorController } from '@/components/common/ColorController';
import PrivyRootProvider from '@/components/providers/PrivyRootProvider';
import { ReferralHandler } from '@/components/ReferralHandler';
import { Toaster } from '@/components/ui/sonner';
import '@/css/globals.css';
import { ViewProvider, SearchParamsProvider } from '@/lib/context/ViewContext';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { GeistMono } from 'geist/font/mono';
import { GeistSans } from 'geist/font/sans';
import { Silkscreen } from 'next/font/google';
import { Suspense } from 'react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { LaunchDarklyProvider } from '@/components/providers/LaunchDarklyProvider';
import { AnimatePresence } from 'framer-motion';
import { Navigation } from '@/components/ui/Navigation';
import { SideNav } from '@/components/layout/side-nav/SideNav';
import { getMetadata } from '@/lib/utils/meta-data/getMetadata';
import { GlobalStoreProvider } from '@/components/providers/StoreProvider';
import PlausibleProvider from 'next-plausible';

const silkscreen = Silkscreen({
  subsets: ['latin'],
  weight: ['400', '700'],
  variable: '--font-silkscreen',
});

export async function generateMetadata() {
  return getMetadata('default');
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang='en'
      className={`${GeistSans.variable} ${GeistMono.variable} ${silkscreen.variable} dark`}
      suppressHydrationWarning
    >
      <body
        className='min-h-[100dvh] bg-black text-white antialiased font-mono'
        suppressHydrationWarning
      >
        <PlausibleProvider domain='death.fun'>
          <PrivyRootProvider>
            <LaunchDarklyProvider>
              <ReactQueryDevtools initialIsOpen={false} />
              <SearchParamsProvider>
                <GlobalStoreProvider>
                  <ViewProvider>
                    <Suspense fallback={<div></div>}>
                      <ReferralHandler />
                    </Suspense>
                    <ColorController />
                    <AnimatePresence mode='wait'>
                      <div className='h-[100dvh] flex flex-col overflow-hidden'>
                        <Navigation>
                          <div className='flex flex-1 h-full overflow-auto'>
                            <SideNav />
                            {children}
                          </div>
                        </Navigation>
                      </div>
                    </AnimatePresence>
                    <Toaster theme='dark' position='top-right' closeButton />
                  </ViewProvider>
                </GlobalStoreProvider>
              </SearchParamsProvider>
            </LaunchDarklyProvider>
          </PrivyRootProvider>
        </PlausibleProvider>
        <SpeedInsights />
      </body>
    </html>
  );
}
