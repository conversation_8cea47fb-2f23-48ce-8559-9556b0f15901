'use client';

import { Challenges } from '@/components/Challenges';
import { DeathRaceGame } from '@/components/death-race/DeathRaceGame';
import { Leaderboard } from '@/components/Leaderboard';
import { DEATH_RACE_GAME_TYPE } from '@/lib/constants';
import { useView } from '@/lib/context/ViewContext';
import { useNoFunds } from '@/lib/hooks/useNoFunds';

export default function HomePage() {
  const { activeView } = useView();
  useNoFunds(DEATH_RACE_GAME_TYPE);

  return (
    <>
      {/* Main Game Area - On desktop: side-by-side view, On mobile: only show active view */}
      <div className={`w-full flex-1 ${activeView === 'game' ? 'block' : 'hidden sm:block'}`}>
        <DeathRaceGame />
      </div>

      {/* Right Sidebar - Leaderboard and Challenges */}
      <div
        className={`w-full sm:w-auto flex-col ${activeView === 'leaderboard' ? 'flex' : 'hidden lg:flex lg:w-[22rem]'}`}
      >
        <Leaderboard gameType={DEATH_RACE_GAME_TYPE} />
        <Challenges gameType={DEATH_RACE_GAME_TYPE} />
      </div>
    </>
  );
}
