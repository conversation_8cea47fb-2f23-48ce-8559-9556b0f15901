{"name": "temp", "version": "0.1.0", "private": true, "license": "UNLICENSED", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "next dev -p 3033 --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "test": "jest", "prepare": "husky", "ngrok": "ngrok http http://localhost:3033", "gen": "supabase gen types typescript --local --schema public > lib/types/database.ts", "create:migration": "supabase migration new", "migrate": "supabase migration up --include-all && bun gen"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md,css,scss}": ["prettier --write"]}, "dependencies": {"@abstract-foundation/agw-client": "^1.8.6", "@abstract-foundation/agw-react": "^1.8.6", "@bigmi/react": "^0.4.1", "@lifi/wallet-management": "^3.12.1", "@lifi/widget": "^3.24.3", "@mysten/dapp-kit": "^0.16.14", "@noble/ed25519": "^2.2.3", "@noble/hashes": "^1.7.1", "@openzeppelin/contracts": "4.9.0", "@phosphor-icons/react": "^2.1.7", "@privy-io/react-auth": "^2.9.1", "@privy-io/server-auth": "^1.20.2", "@privy-io/wagmi": "^1.0.3", "@privy-io/wagmi-connector": "^0.1.13", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@redux-devtools/extension": "^3.3.0", "@reown/appkit": "^1.7.13", "@reown/appkit-adapter-solana": "^1.7.13", "@reown/appkit-adapter-wagmi": "^1.7.13", "@reown/appkit-common": "^1.7.13", "@solana/wallet-adapter-react": "^0.15.39", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.81.5", "@vercel/functions": "^2.0.3", "@vercel/speed-insights": "^1.2.0", "camelcase-keys": "^9.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "core-js": "^3.44.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.4.1", "geist": "^1.3.1", "launchdarkly-react-client-sdk": "^3.8.1", "lucide-react": "^0.513.0", "millify": "^6.1.0", "mixpanel": "^0.18.1", "mixpanel-browser": "^2.64.0", "next": "^15.3.5", "next-plausible": "^3.12.4", "next-themes": "^0.4.4", "querystring-es3": "^0.2.1", "react": "^19.1.0", "react-countup": "^6.5.3", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "server-only": "^0.0.1", "snakecase-keys": "^8.0.1", "sonner": "^2.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tlock-js": "0.9.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "viem": "^2.31.7", "wagmi": "^2.15.6", "zksync-ethers": "^6.17.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@jest/globals": "^29.7.0", "@shadcn/ui": "^0.0.4", "@stdlib/stats-chi2test": "^0.2.1", "@tanstack/react-query-devtools": "^5.74.4", "@types/jest": "^29.5.14", "@types/mixpanel-browser": "^2.60.0", "@types/node": "^22.14.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "ascii-table3": "^1.0.0", "autoprefixer": "^10.0.1", "babel-plugin-react-compiler": "^19.0.0-beta-714736e-20250131", "eslint": "^8", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.0.0-beta-714736e-20250131", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "lint-staged": "^16.1.2", "papaparse": "^5.5.3", "postcss": "^8", "prettier": "^3.5.3", "shadcn-ui": "^0.9.4", "supabase": "^2.22.12", "tailwindcss": "^3.3.0", "tsx": "^4.19.4", "type-fest": "^4.40.0", "typescript": "^5.8.3"}}