'use client';

import { formatCurrency } from '@/lib/client/wallet';
import { GameType } from '@/lib/constants';
import { useGambling } from '@/lib/hooks/useGambling';
import { cn } from '@/lib/utils';
import { Bank } from '@phosphor-icons/react';

export function TotalPot(props: { gameType: GameType }) {
  const { potBalance, isPotLoading } = useGambling(props.gameType);

  if (isPotLoading || !potBalance) {
    return (
      <div className='text-sm'>
        <span className='text-white/60 flex items-center gap-1.5'>
          <Bank className='w-5 h-5' /> Loading
        </span>
      </div>
    );
  }

  const displayAmount = `${formatCurrency(potBalance)}`;

  return (
    <div className='text-sm'>
      <span
        className={cn(
          'text-gc-400 font-medium transition-all duration-1000 flex items-center gap-1.5',
          isPotLoading && 'animate-pulse'
        )}
      >
        <Bank className='w-5 h-5' /> {displayAmount}
      </span>
    </div>
  );
}
