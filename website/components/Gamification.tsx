'use client';

import { useGetUser } from '@/lib/client/queries';
import { formatMultiplier } from '@/lib/utils/streak';
import { MAX_STREAK_DAYS } from '@/lib/constants';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function Gamification() {
  const { data: userData } = useGetUser();
  const { streak = 0, multiplier = 1.0 } = userData ?? {};

  return (
    <TooltipProvider>
      <div className='p-3 border-b border-white/10 bg-gc-400/10'>
        <div className='space-x-3 flex justify-between'>
          {/* Play Streak */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className='flex items-center justify-between space-x-3 cursor-help'>
                <div className='text-sm text-white font-fancy uppercase tracking-wide'>Streak</div>
                <div className='text-sm font-bold text-gc-400'>
                  {streak}/{MAX_STREAK_DAYS}
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side='bottom' className='max-w-xs'>
              <p>
                Play at least one game each day to maintain your streak. Your multiplier increases
                by 0.02x per day. Max streak is 30 days.
              </p>
            </TooltipContent>
          </Tooltip>

          {/* Multiplier */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className='flex items-center justify-between space-x-3 cursor-help'>
                <div className='text-sm text-white font-fancy uppercase tracking-wide'>
                  DP Bonus
                </div>
                <div className='text-sm font-bold text-gc-400'>{formatMultiplier(multiplier)}</div>
              </div>
            </TooltipTrigger>
            <TooltipContent side='bottom' className='max-w-xs'>
              <p>
                A bonus applied anytime you earn Death Points. Eg. 100 DP x 1.05 bonus = 105 DP.
              </p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
}
