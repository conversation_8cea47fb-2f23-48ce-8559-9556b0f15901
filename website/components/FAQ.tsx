'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog/Dialog';
import { useFirstTimeUser } from '@/lib/hooks/useFirstTimeUser';
import { useState, useEffect } from 'react';
import { DEFAULT_GAME_NAME, DEATH_RACE_HOUSE_EDGE, GAME_TYPES } from '@/lib/constants';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import { analytics } from '@/lib/client/analytics';
import { useGetUser } from '@/lib/client/queries';
import { getDeathRaceFAQContent, getLazerPartyFAQContent } from '@/lib/faqContent';
import { Button } from './ui/button/Button';
import { useGlobalStore } from '@/lib/store';

interface FAQAccordionItem {
  value: string;
  title: string;
  content: React.ReactNode;
}

export interface FAQContent {
  howToPlay: string[];
  accordionItems: FAQAccordionItem[];
}

interface FAQProps {
  gameType: (typeof GAME_TYPES)[number];
}

export function FAQ({ gameType }: FAQProps) {
  // ------------------------------------------------ STATE
  const faqOpen = useGlobalStore((state) => state.faq);
  const toggleModals = useGlobalStore((state) => state.toggleModals);

  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { isFirstTimeUser, markFAQAsShown } = useFirstTimeUser(gameType);

  // Generate dynamic content based on user data
  const content =
    gameType === 'death_race'
      ? getDeathRaceFAQContent(userData)
      : getLazerPartyFAQContent(userData);

  // Effect to handle automatic opening for first-time users
  useEffect(() => {
    if (isFirstTimeUser) {
      toggleModals({ faq: true });
    }
    // Listen for custom event to open FAQ
    const handler = () => toggleModals({ faq: true });
    window.addEventListener('open-faq', handler);
    return () => {
      window.removeEventListener('open-faq', handler);
    };
  }, [isFirstTimeUser]);

  // Track FAQ_VIEWED when FAQ is opened
  useEffect(() => {
    if (faqOpen) {
      analytics.faqViewed();
    }
  }, [faqOpen]);

  const handleOpenChange = (open: boolean) => {
    toggleModals({ faq: open });
    if (!open && isFirstTimeUser) {
      markFAQAsShown();
    }
  };

  return (
    <Dialog open={faqOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant='link' size='none' className='text-sm'>
          FAQ
        </Button>
      </DialogTrigger>
      <DialogContent className='bg-black/95 border-white/10 backdrop-blur-md max-h-[95vh] flex flex-col'>
        <DialogHeader>
          <DialogTitle className='text-white text-2xl text-center'>
            {isFirstTimeUser ? `Welcome to ${DEFAULT_GAME_NAME} 😈` : 'How to Play'}
          </DialogTitle>
        </DialogHeader>
        <div className='space-y-10 text-sm text-white/80 pt-4 overflow-y-auto flex-1'>
          <div>
            <ol className='list-decimal pl-10 space-y-5 text-base text-gc-400 font-semibold leading-tight'>
              {content.howToPlay.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
          </div>
          <Accordion type='single' collapsible className='w-full'>
            {content.accordionItems.map((item) => (
              <AccordionItem key={item.value} value={item.value}>
                <AccordionTrigger className='text-base font-bold text-white'>
                  {item.title}
                </AccordionTrigger>
                <AccordionContent>{item.content}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </DialogContent>
    </Dialog>
  );
}
