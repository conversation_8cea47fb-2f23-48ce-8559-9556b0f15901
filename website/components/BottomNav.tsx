'use client';

import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { <PERSON><PERSON><PERSON>, GameController, Trophy } from '@phosphor-icons/react';
import { cn } from '@/lib/utils';
import { useView, ViewType } from '@/lib/context/ViewContext';
import { useRouter } from 'next/navigation';
import { GAMES_META } from '@/lib/constants';
import { useEffect, useState } from 'react';

export function BottomNav() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { activeView, setActiveView, isHomePage } = useView();

  // Check if we're on any game route and get the current game
  const currentGame = GAMES_META.find((game) => game.route === pathname);
  const isGameRoute = !!currentGame;

  // State for active game that's safe for SSR
  const [activeGame, setActiveGame] = useState(() => currentGame || GAMES_META[0]);

  // Update active game after hydration when search params are available
  useEffect(() => {
    if (currentGame) {
      setActiveGame(currentGame);
    } else {
      // If we're on stats page, determine game from URL parameter
      const gameTypeParam = searchParams.get('gameType');
      const gameFromParams = gameTypeParam
        ? GAMES_META.find((game) => game.gameType === gameTypeParam)
        : null;
      setActiveGame(gameFromParams || GAMES_META[0]);
    }
  }, [currentGame, searchParams]);

  // Generate dynamic stats URL based on active game
  const statsUrl = `/stats?gameType=${activeGame.gameType}`;

  // Function to handle view change with direct navigation
  const handleViewChange = (view: ViewType) => {
    setActiveView(view);
    // Navigate to the active game's route with the view parameter
    router.push(`${activeGame.route}?view=${view}`);
  };

  return (
    <nav className='sm:hidden bg-black border-t border-gc-400/20'>
      <div className='flex items-center justify-around h-16'>
        {/* Stats Link */}
        <Link
          href={statsUrl}
          className={cn(
            'flex flex-col items-center justify-center gap-1 w-full h-full text-xs font-bold uppercase',
            pathname === '/stats' ? 'text-gc-400' : 'text-white/60 hover:text-white/80'
          )}
        >
          <ChartLine className='w-5 h-5' />
          <span>Stats</span>
        </Link>

        {/* Game View Button - Use Link for better navigation */}
        <Link
          href={`${activeGame.route}?view=game`}
          className={cn(
            'flex flex-col items-center justify-center gap-1 w-full h-full text-xs font-bold uppercase',
            isGameRoute && activeView === 'game'
              ? 'text-gc-400'
              : 'text-white/60 hover:text-white/80'
          )}
          onClick={(e) => {
            // Use manual navigation to prevent multiple reloads
            if (isHomePage) {
              e.preventDefault();
              setActiveView('game');
            }
          }}
        >
          <GameController className='w-6 h-6' />
          <span>Play</span>
        </Link>

        {/* Leaderboard View Button - Use Link for better navigation */}
        <Link
          href={`${activeGame.route}?view=leaderboard`}
          className={cn(
            'flex flex-col items-center justify-center gap-1 w-full h-full text-xs font-bold uppercase',
            isGameRoute && activeView === 'leaderboard'
              ? 'text-gc-400'
              : 'text-white/60 hover:text-white/80'
          )}
          onClick={(e) => {
            // Use manual navigation to prevent multiple reloads
            if (isGameRoute) {
              e.preventDefault();
              setActiveView('leaderboard');
            }
          }}
        >
          <Trophy className='w-6 h-6' />
          <span>Leaderboard</span>
        </Link>
      </div>
    </nav>
  );
}
