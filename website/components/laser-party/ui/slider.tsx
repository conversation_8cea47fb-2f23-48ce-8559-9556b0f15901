'use client';

import * as React from 'react';
import * as SliderPrimitive from '@radix-ui/react-slider';

import { cn } from '@/lib/utils';

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => (
  <SliderPrimitive.Root
    ref={ref}
    className={cn('relative flex w-full touch-none select-none items-center py-5', className)}
    {...props}
  >
    <SliderPrimitive.Track className='relative h-2.5 w-full grow overflow-hidden rounded-full bg-primary/50'>
      <SliderPrimitive.Range className='absolute h-full bg-red-700' />
    </SliderPrimitive.Track>
    <SliderPrimitive.Thumb className='block h-8 w-8 rounded-full border bg-white shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50' />
  </SliderPrimitive.Root>
));
Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider };
