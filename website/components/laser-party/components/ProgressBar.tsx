'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { formatMultiplier, generateLaserPartyRowConfig } from '@/lib/utils/game';
import { LocalAnimationPhase } from '@/lib/types/laserParty';

export interface ProgressBarProps {
  currentTurn: number;
  maxTurns: number;
  currentBet: bigint;
  orientation?: 'vertical' | 'horizontal';
  localAnimationPhase: LocalAnimationPhase;
  currentGridSize: number;
}

export function ProgressBar({
  currentTurn,
  maxTurns,
  currentBet,
  orientation = 'vertical',
  localAnimationPhase,
  currentGridSize,
}: ProgressBarProps) {
  const [hoveredSection, setHoveredSection] = useState<number | null>(null);

  function Tooltip({ value, position }: { value: string | number; position: React.CSSProperties }) {
    return (
      <div
        className='pointer-events-none absolute z-50 px-2 py-1 rounded bg-black/90 text-xs text-white shadow-lg border border-red-800'
        style={position}
      >
        {value}
      </div>
    );
  }

  const rowConfig = generateLaserPartyRowConfig(currentGridSize);

  const calculateActualMultiplier = (turn: number): number => {
    if (turn <= 0) return 1;

    let multiplier = 1;
    for (let i = 0; i < Math.min(turn, rowConfig.length); i++) {
      const row = rowConfig[i];
      const baseMultiplier = 1 / (1 - 1 / row.tiles);
      multiplier *= baseMultiplier;
    }

    // Apply house edge (0.95)
    return multiplier * 0.95;
  };

  // --- Shared Bar Component ---
  function Bar({
    steps,
    hoverSteps,
    getEth,
    getLabel,
    filledIndex,
    orientation,
    barPercent,
    numSteps,
  }: {
    steps: { value: number; label: string | number; pos?: number }[];
    hoverSteps: { value: number; label: string | number }[];
    getEth: (value: number) => number;
    getLabel: (value: number) => string | number;
    filledIndex: number;
    orientation: 'vertical' | 'horizontal';
    barPercent: number;
    numSteps?: number;
  }) {
    // Fixed positioning logic - step 0 at 0%, step actualMaxTurns at 100%
    const getStepPosition = (stepValue: number) => {
      if (actualMaxTurns <= 1) return 0;
      return (stepValue / actualMaxTurns) * 100;
    };

    // Progress fill should go to the current step position
    const currentStepPosition = getStepPosition(currentTurn);
    // const shouldRender = localAnimationPhase === 'idle' || localAnimationPhase === 'laser';
    return orientation === 'horizontal' ? (
      <div className='flex flex-col w-full items-center'>
        <div className='relative w-full h-0.5 bg-gray-600 rounded-full'>
          {/* Progress fill line */}
          <div
            className='absolute left-0 top-0 h-full bg-gc-600 transition-all duration-300 rounded-full'
            style={{ width: `${currentStepPosition}%` }}
          />

          {/* All step notches with labels */}
          {hoverSteps.map((step, i) => {
            const isMainStep = steps.some((s) => s.value === step.value);
            const isPassed = step.value <= currentTurn;
            const position = getStepPosition(step.value);
            const showMoney = step.value !== 0;
            return (
              <div
                key={step.value}
                className='absolute top-1/2 -translate-y-1/2 cursor-pointer'
                style={{ left: `${position}%`, transform: 'translateY(-50%) translateX(-50%)' }}
                onMouseEnter={() => setHoveredSection(i + 1000)}
                onMouseLeave={() => setHoveredSection(null)}
              >
                <div
                  className={cn('w-0.5 h-3 transition-all duration-300', {
                    'opacity-50': !isMainStep && !isPassed,
                    'opacity-100': isMainStep || isPassed,
                    'bg-gc-600': isPassed,
                    'bg-gray-600': !isPassed,
                  })}
                />
                {showMoney && (
                  <div
                    className={cn(
                      'absolute -top-6 left-1/2 -translate-x-1/2 flex items-center transition-opacity duration-200',
                      {
                        'opacity-100': isMainStep || (!isMainStep && hoveredSection === i + 1000),
                        'opacity-0': !isMainStep && hoveredSection !== i + 1000,
                      }
                    )}
                  >
                    <span className='text-xs text-gray-400'>
                      {formatMultiplier(getEth(step.value))}
                    </span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    ) : (
      <div className='flex flex-col items-center h-full'>
        <div className='relative w-0.5 h-full bg-gray-600 rounded-full'>
          {/* Progress fill line */}
          <div
            className='absolute bottom-0 w-full transition-all duration-300 rounded-full bg-gc-600'
            style={{ height: `${currentStepPosition}%` }}
          />

          {/* All step notches with labels */}
          {hoverSteps.map((step, i) => {
            const isMainStep = steps.some((s) => s.value === step.value);
            const isPassed = step.value <= currentTurn;
            const position = getStepPosition(step.value);
            const showMoney = step.value !== 0;
            return (
              <div
                key={step.value}
                className='absolute left-1/2 -translate-x-1/2 cursor-pointer'
                style={{ bottom: `${position}%`, transform: 'translateX(-50%) translateY(50%)' }}
                onMouseEnter={() => setHoveredSection(i + 1000)}
                onMouseLeave={() => setHoveredSection(null)}
              >
                <div
                  className={cn('w-3 h-0.5 transition-all duration-300', {
                    'opacity-50': !isMainStep && !isPassed,
                    'opacity-100': isMainStep || isPassed,
                    'bg-gc-600': isPassed,
                    'bg-gray-600': !isPassed,
                  })}
                />
                {showMoney && (
                  <div
                    className={cn(
                      'absolute left-full ml-2.5 top-1/2 -translate-y-1/2 transition-opacity duration-200',
                      {
                        'opacity-100': isMainStep || (!isMainStep && hoveredSection === i + 1000),
                        'opacity-0': !isMainStep && hoveredSection !== i + 1000,
                      }
                    )}
                  >
                    <span className='text-xs text-gray-400'>
                      {formatMultiplier(getEth(step.value))}
                    </span>
                  </div>
                )}
                {step.value !== 0 && (
                  <div
                    className={cn(
                      'absolute right-full mr-2.5 top-1/2 -translate-y-1/2 transition-opacity duration-200',
                      {
                        'opacity-100': isMainStep || (!isMainStep && hoveredSection === i + 1000),
                        'opacity-0': !isMainStep && hoveredSection !== i + 1000,
                      }
                    )}
                  >
                    <span className='text-xs text-gray-400'>{getLabel(step.value)}</span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  const numSteps = 7;
  // Calculate actual max turns dynamically from the current grid size
  // The max turns is when we eliminate down to 1x1, so it's (currentGridSize * 2) - 2
  const actualMaxTurns = currentGridSize * 2 - 2;

  // Generate dynamic steps: always include the max turn, then every 4th step working backwards
  const generateDynamicSteps = () => {
    const steps = [actualMaxTurns]; // Always include the highest level

    // Work backwards from max, adding every 4th step
    for (let i = actualMaxTurns - 4; i > 0; i -= 4) {
      steps.unshift(i);
    }

    // Always include 0 if it's not already there
    if (steps[0] !== 0) {
      steps.unshift(0);
    }

    return steps.map((value) => ({ value, label: value, pos: value }));
  };

  const steps = generateDynamicSteps();
  const hoverSteps = Array.from({ length: actualMaxTurns }, (_, i) => ({
    value: i + 1,
    label: i + 1,
  }));

  const getMultiplier = (turn: number) => calculateActualMultiplier(turn);
  const getLabel = (turn: number) => turn;
  const filledIndex = currentTurn + 1;

  const barPercent = actualMaxTurns > 1 ? (currentTurn / actualMaxTurns) * 100 : 0;

  return (
    <Bar
      steps={steps}
      hoverSteps={hoverSteps}
      getEth={getMultiplier}
      getLabel={getLabel}
      filledIndex={filledIndex}
      orientation={orientation}
      barPercent={barPercent}
      numSteps={steps.length}
    />
  );
}
