import { LASER_DURATION } from '@/lib/constants';
import { useGameAnimation } from '../hooks/useGameAnimations';
import { LocalAnimationPhase, RoundOutcome } from '@/lib/types/laserParty';
import { cn } from '@/lib/utils';
import { Skull } from '@phosphor-icons/react';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { useSounds } from '../hooks/useSounds';
import { VictoryScreen } from './victory/VictoryScreen';

export interface GridProps {
  gameId: string;
  gameStatus: string;
  aliveRows: number[];
  aliveCols: number[];
  roundOutcome: RoundOutcome | null;
  isSelectingTile: boolean;
  isCashingOut: boolean;
  localAnimationPhase: LocalAnimationPhase;
  setLocalAnimationPhase: Dispatch<SetStateAction<LocalAnimationPhase>>;
  setAnimationPending: (pending: boolean) => void;
  handleCellClick: (row: number, col: number) => void;
  currentGridSize: number;
}

export function Grid({
  gameId,
  gameStatus,
  aliveRows,
  aliveCols,
  roundOutcome,
  isSelectingTile,
  isCashingOut,
  localAnimationPhase,
  setLocalAnimationPhase,
  setAnimationPending,
  handleCellClick,
}: GridProps) {
  // ------------------------------------------------ HOOKS
  const { localLaserTarget, localSelectedCell, animationGrid } = useGameAnimation({
    gameId,
    roundOutcome,
    aliveRows,
    aliveCols,
    gameStatus,
    localAnimationPhase,
    setLocalAnimationPhase,
    setAnimationPending,
  });

  // --- Victory explosion state ---
  const [showVictoryExplosion, setShowVictoryExplosion] = useState(false);

  // --- Track if victory sound has been played ---
  const hasPlayedVictorySound = useRef(false);

  // --- Victory sound ---
  const { playVictory } = useSounds({
    localAnimationPhase: localAnimationPhase,
  });

  // --- Store playVictory in ref to avoid dependency issues ---
  const playVictoryRef = useRef(playVictory);
  playVictoryRef.current = playVictory;

  // --- Detect victory for explosion ---
  useEffect(() => {
    let delayTimeoutId: ReturnType<typeof setTimeout> | undefined;

    // Check if player has reached the victory condition:
    // - Both aliveRows and aliveCols have length 1 (only one cell left)
    // - Game is still active (they haven't cashed out yet)
    const hasReachedFinalCell = aliveRows.length === 1 && aliveCols.length === 1;
    const isActiveGame = gameStatus === 'active';
    const isVictoryCondition = hasReachedFinalCell && isActiveGame;

    if (isVictoryCondition && !hasPlayedVictorySound.current) {
      // Play victory sound only once
      playVictoryRef.current();
      hasPlayedVictorySound.current = true;

      // Add small delay before showing victory explosion
      delayTimeoutId = setTimeout(() => {
        setShowVictoryExplosion(true);
        // Don't hide - let it stay until new game starts
      }, 500);
    } else if (!hasReachedFinalCell) {
      // Reset the flag when game is no longer in victory condition
      hasPlayedVictorySound.current = false;
      // Only hide if we're no longer in the final cell (new game started)
      setShowVictoryExplosion(false);
    }

    // Cleanup on re-run/unmount
    return () => {
      if (delayTimeoutId) clearTimeout(delayTimeoutId);
    };
  }, [gameStatus, aliveRows.length, aliveCols.length]);

  // ------------------------------------------------ RENDER
  return (
    <div className='aspect-square max-h-[95vw] sm:max-h-full size-full flex items-center justify-center relative'>
      <div className='relative w-full h-full'>
        {/* Laser Overlay Animation */}
        {(localAnimationPhase === 'laser' ||
          localAnimationPhase === 'dying' ||
          localAnimationPhase === 'dead') &&
          localLaserTarget &&
          animationGrid && (
            <>
              {localLaserTarget.dimension === 'row' ? (
                <div
                  className='absolute left-0 pointer-events-none z-50'
                  style={{
                    top: `calc((100% / ${
                      animationGrid.rows.length
                    }) * ${animationGrid.rows.findIndex((r) => r === localLaserTarget.index)})`,
                    height: `calc(100% / ${animationGrid.rows.length})`,
                    width: '100%',
                  }}
                >
                  <div
                    className='bg-red-500 opacity-60 h-full'
                    style={{
                      width:
                        localAnimationPhase === 'dying' || localAnimationPhase === 'dead'
                          ? '100%'
                          : '0%',
                      animation:
                        localAnimationPhase === 'laser'
                          ? `laser-row-sweep ${LASER_DURATION}s linear forwards`
                          : 'none',
                    }}
                  />
                </div>
              ) : (
                <div
                  className='absolute top-0 pointer-events-none z-50'
                  style={{
                    left: `calc((100% / ${
                      animationGrid.cols.length
                    }) * ${animationGrid.cols.findIndex((c) => c === localLaserTarget.index)})`,
                    width: `calc(100% / ${animationGrid.cols.length})`,
                    height: '100%',
                  }}
                >
                  <div
                    className='bg-red-500 opacity-60 w-full'
                    style={{
                      height:
                        localAnimationPhase === 'dying' || localAnimationPhase === 'dead'
                          ? '100%'
                          : '0%',
                      animation:
                        localAnimationPhase === 'laser'
                          ? `laser-col-sweep ${LASER_DURATION}s linear forwards`
                          : 'none',
                    }}
                  />
                </div>
              )}

              {/* Global CSS for laser animations */}
              <style jsx global>{`
                @keyframes laser-row-sweep {
                  from {
                    width: 0%;
                  }
                  to {
                    width: 100%;
                  }
                }

                @keyframes laser-col-sweep {
                  from {
                    height: 0%;
                  }
                  to {
                    height: 100%;
                  }
                }
              `}</style>
            </>
          )}

        {/* Grid Cells */}
        <div className='flex flex-col w-full h-full border border-red-500 bg-red-900/10'>
          {animationGrid.rows.map((r) => {
            const isRowShrinking =
              localAnimationPhase === 'shrinking' &&
              localLaserTarget &&
              localLaserTarget.dimension === 'row' &&
              localLaserTarget.index === r;

            return (
              <div
                key={`row-${r}`}
                className={cn(
                  'flex w-full flex-1',
                  localAnimationPhase !== 'idle' &&
                    'transition-[flex] duration-300 ease-in-out origin-center',
                  isRowShrinking &&
                    '!flex-[0_1_0%] overflow-hidden p-0 m-0 border-0 transition-[flex,border-width]'
                )}
              >
                {animationGrid.cols.map((c) => {
                  const cellKey = animationGrid?.keys[r][c];
                  const isSelected = localSelectedCell?.row === r && localSelectedCell?.col === c;
                  // Check if this is the final cell (only one row and one column left)
                  const isFinalCell = aliveRows.length === 1 && aliveCols.length === 1;

                  const isDisabled =
                    localAnimationPhase !== 'idle' ||
                    gameStatus !== 'active' ||
                    isSelectingTile ||
                    isCashingOut ||
                    isFinalCell; // Disable clicking on the final cell

                  const isCellHighlighted =
                    (localAnimationPhase === 'laser' ||
                      localAnimationPhase === 'dying' ||
                      localAnimationPhase === 'dead') &&
                    localLaserTarget &&
                    ((localLaserTarget.dimension === 'row' && localLaserTarget.index === r) ||
                      (localLaserTarget.dimension === 'col' && localLaserTarget.index === c));

                  const isCellShrinking =
                    localAnimationPhase === 'shrinking' &&
                    localLaserTarget &&
                    ((localLaserTarget.dimension === 'row' && localLaserTarget.index === r) ||
                      (localLaserTarget.dimension === 'col' && localLaserTarget.index === c));

                  // Show skull when player is dead and in the dying or dead animation phase
                  const shouldShowSkull =
                    gameStatus === 'lost' &&
                    isSelected &&
                    (localAnimationPhase === 'dying' || localAnimationPhase === 'dead');

                  return (
                    <button
                      key={cellKey}
                      onClick={() => handleCellClick(r, c)}
                      disabled={isDisabled}
                      className={cn(
                        'relative flex-1 flex items-center justify-center border border-red-500',
                        localAnimationPhase !== 'idle' &&
                          'transition-[flex,border-width,transform] duration-300 ease-in-out origin-center',
                        isCellShrinking
                          ? '!flex-[0_1_0%] overflow-hidden p-0 m-0 opacity-0'
                          : 'opacity-100',
                        isCellHighlighted &&
                          (localAnimationPhase === 'laser' ||
                            localAnimationPhase === 'dying' ||
                            localAnimationPhase === 'dead') &&
                          'animate-pulse duration-1000 z-60 ',
                        isSelected && shouldShowSkull && '!z-[60]',
                        isSelected && !shouldShowSkull && 'bg-red-500/50',
                        isSelected && shouldShowSkull && 'bg-red-500',
                        !isCellHighlighted &&
                          !isSelected &&
                          ' sm:hover:bg-red-500/20 cursor-pointer disabled:cursor-not-allowed',
                        isDisabled && 'cursor-not-allowed'
                      )}
                    >
                      <div
                        className={cn(
                          'w-full h-full flex items-center justify-center',
                          isCellShrinking ? 'invisible' : ''
                        )}
                      >
                        {shouldShowSkull && (
                          <Skull className='text-white drop-shadow-lg w-1/2 h-1/2' weight='fill' />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>
      <VictoryScreen
        isActive={showVictoryExplosion}
        isGameActive={showVictoryExplosion && gameStatus === 'active'}
      />
    </div>
  );
}
