import { AnimatedFaceIcon } from "@/components/ui/icons/animated/AnimatedFaceIcon";
import { motion } from "framer-motion";

export function VictoryIcon({ showText }: { showText: boolean }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.2 }}
      animate={showText ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.2 }}
      transition={{
        opacity: { duration: 0.2, ease: [0.25, 0.46, 0.45, 0.94] },
        scale: { duration: 0.5, ease: [0.26,-0.12,0.03,1.86] },
      }}
      style={{ transformOrigin: 'center' }}
    >
      <AnimatedFaceIcon isAnimating={showText} animation='laugh' className='md:w-48 md:h-48 w-24 h-24 text-red-400 drop-shadow-2xl' />
    </motion.div>
  );
}