'use client';

import { motion } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import { Message } from './Message';
import { VictoryIcon } from './VictoryIcon';

interface VictoryScreenProps {
  isActive: boolean;
  isGameActive: boolean;
}

const GRID_SIZE = 10;

const shuffle = <T,>(a: T[]): T[] => {
  const copy = [...a];
  for (let i = copy.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [copy[i], copy[j]] = [copy[j], copy[i]];
  }
  return copy;
};

const getCellKey = (r: number, c: number) => `${r}-${c}`;

export const VictoryScreen = ({ isActive, isGameActive }: VictoryScreenProps) => {
  const [cellStates, setCellStates] = useState<Record<string, 'red' | 'black'>>({});
  const [showText, setShowText] = useState(false);
  const timeoutRefs = useRef<number[]>([]);
  const intervalRef = useRef<number | null>(null);

  const clearAllTimers = () => {
    timeoutRefs.current.forEach(clearTimeout);
    timeoutRefs.current = [];
    if (intervalRef.current !== null) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const runShuffle = (cells: string[]) => {
    const shuffled = shuffle([...cells]);
    const batchSize = 5;
    const delayBetweenBatches = 150;
  
    const batches = Math.ceil(shuffled.length / batchSize);
  
    for (let i = 0; i < batches; i++) {
      const batch = shuffled.slice(i * batchSize, (i + 1) * batchSize);
  
      batch.forEach(key => {
        const delay = i * delayBetweenBatches;
  
        timeoutRefs.current.push(
          window.setTimeout(() => {
            setCellStates(prev => ({ ...prev, [key]: 'red' }));
          }, delay),
          window.setTimeout(() => {
            setCellStates(prev => ({ ...prev, [key]: 'black' }));
          }, delay + 250)
        );
      });
    }
  
    timeoutRefs.current.push(
      window.setTimeout(() => setShowText(true), 1000)
    );
  
    const totalDuration = batches * delayBetweenBatches + 250;
    timeoutRefs.current.push(
      window.setTimeout(() => runShuffle(cells), totalDuration)
    );
  };
  

  useEffect(() => {
    if (!isActive) return;
  
    setCellStates({});
    setShowText(false);
    clearAllTimers();
  
    const allCells: string[] = [];
    for (let r = 0; r < GRID_SIZE; r++) {
      for (let c = 0; c < GRID_SIZE; c++) {
        allCells.push(getCellKey(r, c));
      }
    }
  
    runShuffle(allCells);
  
    return () => clearAllTimers();
  }, [isActive]);

  const getBlocks = (rowIndex: number) =>
    [...Array(GRID_SIZE)].map((_, col) => {
      const key = getCellKey(rowIndex, col);
      const state = cellStates[key];
      return (
        <motion.div
          key={col}
          className={`flex-1 transition-colors duration-100 border border-red-700 ${
            state === 'red'
              ? 'bg-red-700'
              : 'bg-black'
          }`}
        />
      );
    });

  if (!isActive) return null;

  return (
    <div className="absolute inset-0 z-[100] flex items-center justify-center pointer-events-none border border-black/70 overflow-hidden">
      <div className="flex flex-col w-full h-full">
        {[...Array(GRID_SIZE)].map((_, row) => (
          <div key={row} className="flex w-full flex-1">
            {getBlocks(row)}
          </div>
        ))}
      </div>

      <motion.div
        className="absolute inset-0 bg-black/70 flex flex-col items-center gap-4 justify-center text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: showText ? 1 : 0 }}
        transition={{ duration: 0.5 }}
      >
        <VictoryIcon showText={showText} />
        <Message showMessage={showText} showSubtitle={isGameActive} />
        <div className="absolute left-0 top-0 w-full h-full bg-red-500/15" />
      </motion.div>
    </div>
  );
};
