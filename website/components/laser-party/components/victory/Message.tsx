'use client';

import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import React from 'react';

interface MessageProps {
  showMessage: boolean;
  showSubtitle: boolean;
}

export const Message: React.FC<MessageProps> = ({
  showMessage,
  showSubtitle
}) => {
  return (
    <AnimatePresence>
      {showMessage && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{
            duration: 0.5,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
          style={{ textAlign: 'center' }}
        >
          <div className="text-xl md:text-3xl font-bold text-red-400 drop-shadow-2xl mb-2">
            You're a Death God!
          </div>
          <div className={cn("text-lg md:text-xl font-semibold text-white drop-shadow-lg opacity-0 transition-opacity duration-500", showSubtitle ? "opacity-100" : "opacity-0")}>
            Cash out to keep playing
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
