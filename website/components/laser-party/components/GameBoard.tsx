'use client';

import { motion } from 'framer-motion';
import React, { useEffect, useState, useRef } from 'react';
import { Grid, GridProps } from './Grid';
import { ProgressBar, ProgressBarProps } from './ProgressBar';
import { SkullExplosion } from '../../common/SkullExplosion';

interface GameBoardProps extends GridProps, ProgressBarProps {}

export const GameBoard: React.FC<GameBoardProps> = (props) => {
  const [mounted, setMounted] = useState(false);

  // --- Skull explosion state ---
  const [showExplosion, setShowExplosion] = useState(false);

  useEffect(() => {
    // Wait for header and controls to animate in (700ms)
    const timer = setTimeout(() => {
      setMounted(true);
    }, 400);
    return () => clearTimeout(timer);
  }, []);

  // --- Detect death for explosion ---
  useEffect(() => {
    let delayTimeoutId: ReturnType<typeof setTimeout> | undefined;
    let hideTimeoutId: ReturnType<typeof setTimeout> | undefined;

    // Only trigger skull explosion when entering 'dying' phase (live gameplay only)
    if (props.localAnimationPhase === 'dying' && props.roundOutcome?.didPlayerDie) {
      // Add 1 second delay before showing explosion
      delayTimeoutId = setTimeout(() => {
        setShowExplosion(true);
        // Hide after animation (3600 ms = twice as long as Death Race)
        hideTimeoutId = setTimeout(() => setShowExplosion(false), 3600);
      }, 100);
    } else {
      setShowExplosion(false);
    }

    // Cleanup on re-run/unmount
    return () => {
      if (delayTimeoutId) clearTimeout(delayTimeoutId);
      if (hideTimeoutId) clearTimeout(hideTimeoutId);
    };
  }, [props.localAnimationPhase, props.roundOutcome]);

  // ---------------------------------------------------------------- RENDER
  return (
    <div className='flex-1 flex justify-center min-h-0 p-6 pt-3 sm:pt-6 relative'>
      <SkullExplosion showExplosion={showExplosion} explosionPos={{ x: 0, y: 0 }} duration={3600} />

      <motion.div
        initial={{ opacity: 0, y: '2rem' }}
        animate={{ 
          opacity: mounted ? 1 : 0, 
          y: mounted ? 0 : '2rem' 
        }}
        transition={{
          duration: 0.5,
          delay: 0.7, // Wait for header/controls
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
        className='flex flex-col justify-center items-center flex-1 overflow-hidden min-h-0'
      >
        {/* Horizontal ProgressBar for small screens */}
        <div className='w-full px-4 pt-8 pb-5 max-w-md mx-auto flex  justify-center sm:hidden'>
          <ProgressBar {...props} orientation='horizontal' />
        </div>

        <div className='flex flex-1 w-full sm:w-auto items-center justify-center min-h-0 sm:pl-24'>
          <Grid {...props} />

          {/* Vertical ProgressBar for large screens */}
          <div className='hidden sm:flex self-stretch px-14 pt-3 pb-2'>
            <ProgressBar {...props} orientation='vertical' />
          </div>
        </div>
      </motion.div>
    </div>
  );
};
