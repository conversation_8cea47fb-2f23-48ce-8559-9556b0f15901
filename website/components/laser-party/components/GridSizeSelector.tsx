'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button/Button';
import { GridFour } from '@phosphor-icons/react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog/Dialog';
import { Slider } from '@/components/laser-party/ui/slider';
import { LP_MIN_GRID_SIZE, LP_MAX_GRID_SIZE } from '@/lib/constants';
import { formatMultiplier, calculateMaxMultiplierForGridSize } from '@/lib/utils/game';
import { cn } from '@/lib/utils';

interface GridSizeSelectorProps {
  currentGridSize: number;
  onGridSizeChange: (size: number) => void;
  disabled?: boolean;
  className?: string;
}

export function GridSizeSelector({
  currentGridSize,
  onGridSizeChange,
  disabled = false,
  className,
}: GridSizeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempGridSize, setTempGridSize] = useState(currentGridSize);

  const handleApply = () => {
    onGridSizeChange(tempGridSize);
    setIsOpen(false);
  };

  const handleCancel = () => {
    setTempGridSize(currentGridSize);
    setIsOpen(false);
  };

  const maxMultiplier = calculateMaxMultiplierForGridSize(tempGridSize);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          type='button'
          variant='white'
          size='lg'
          className={cn('shrink-0 px-3 text-sm gap-0.5 flex-col py-1.5', className)}
          disabled={disabled}
        >
          <div className='text-[0.6rem] uppercase leading-none font-bold'>Grid Size</div>
          <div className='flex items-center gap-1'>
            <GridFour className='hidden sm:block !w-5 !h-5' />
            <span className='leading-none'>
              {currentGridSize}x{currentGridSize}
            </span>
          </div>
        </Button>
      </DialogTrigger>

      <DialogContent className='bg-black border-red-400/20 max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-white font-bold text-lg'>Select Grid Size</DialogTitle>
          <DialogDescription className='text-white/60 text-sm'>
            Larger grids offer higher max multipliers but more rounds to survive.
          </DialogDescription>
        </DialogHeader>

        <div className='py-6 space-y-6'>
          <div className='space-y-2'>
            {/* Grid Size and Max Multiplier values above slider */}
            <div className='flex justify-between items-start'>
              <div>
                <div className='text-xs font-bold text-white mb-1'>Grid Size</div>
                <div className='text-2xl font-bold text-red-400'>
                  {tempGridSize}x{tempGridSize}
                </div>
              </div>
              <div>
                <div className='text-xs font-bold text-white mb-1'>Max Multiplier</div>
                <div className='text-2xl font-bold text-red-400'>
                  {formatMultiplier(maxMultiplier)}
                </div>
              </div>
            </div>

            <div>
              <Slider
                value={[tempGridSize]}
                onValueChange={(value: number[]) => setTempGridSize(value[0])}
                min={LP_MIN_GRID_SIZE}
                max={LP_MAX_GRID_SIZE}
                step={1}
                className='w-full'
              />

              <div className='flex justify-between text-xs text-white/60 -mt-2'>
                <span>
                  {LP_MIN_GRID_SIZE}x{LP_MIN_GRID_SIZE}
                </span>
                <span>
                  {LP_MAX_GRID_SIZE}x{LP_MAX_GRID_SIZE}
                </span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className='gap-2'>
          <Button
            variant='outline'
            onClick={handleCancel}
            className='border-white/20 text-white hover:bg-white/10'
          >
            Cancel
          </Button>
          <Button
            variant='primary'
            onClick={handleApply}
            className='bg-red-500 hover:bg-red-600 text-white'
          >
            Apply
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
