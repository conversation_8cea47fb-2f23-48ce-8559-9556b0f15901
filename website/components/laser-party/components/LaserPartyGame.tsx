'use client';

import { GameStatus } from '@/components/common/GameStatus';
import { MuteButton } from '@/components/common/MuteButton';
import { Button } from '@/components/ui/button/Button';
import {
  useGetUser,
  getActiveGameOptions,
  getRowConfigOptions,
  getUserOptions,
  getLeaderboardEntriesOptions,
} from '@/lib/client/queries';
import {
  DEFAULT_LASER_PARTY_ROW_CONFIG,
  IS_MAINNET,
  LASER_PARTY_GAME_TYPE,
  LP_MAX_TURNS,
  TESTNET_CLAIM_THRESHOLD,
  LP_GRID_SIZE,
  DEATH_FUN_MIN_BET_ETH,
} from '@/lib/constants';
import { useAbstractSession } from '@/lib/hooks/useAbstractSession';
import { useGambling } from '@/lib/hooks/useGambling';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';
import { parseEther } from 'viem';
import { useDemoGame } from '../hooks/useDemoGame';
import { GameBoard } from './GameBoard';
import { useGame } from '../hooks/useGame';
import { GameControls } from '@/components/death-race/GameControls';
import { NoFunds } from '@/components/death-race/NoFunds';
import { toast } from 'sonner';
import { TestnetClaimButton } from '@/components/death-race/TestnetClaimButton';
import superjson from 'superjson';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { LPDisplayRow } from '@/components/death-race/types';
import { useShowUI } from '@/lib/hooks/useShowUI';
import { useSounds } from '../hooks/useSounds';
import { generateLaserPartyRowConfig } from '@/lib/utils/game';
import { analytics } from '@/lib/client/analytics';

// Define preset bets as a file-level constant
const PRESET_BETS = [parseEther('0.001'), parseEther('0.01'), parseEther('0.1')];

export function LaserPartyGame() {
  // ------------------------------------------------ STATE
  const [betAmount, setBetAmount] = useState<bigint>(PRESET_BETS[0]);
  const [demoMode, setDemoMode] = useState(false);

  const [toastId, setToastId] = useState<string | number>('');
  const [testnetClaimed, setTestnetClaimed] = useState(false);
  const showUI = useShowUI(500);

  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { walletAddress, sessionConfig } = userData ?? {};
  const { userBalance, maxBetAmount, isUserBalanceLoading } = useGambling(LASER_PARTY_GAME_TYPE);
  const queryClient = useQueryClient();

  const demoGame = useDemoGame();
  const realGame = useGame();
  const gameHook = demoMode ? demoGame : realGame;

  const {
    currentGame,
    previousGame,
    isCashingOut,
    cashOutError,
    isSelectingTile,
    roundOutcome,
    processingTileIndex,
    animationPending,
    localAnimationPhase,
    gridSize,
    activeGameLoading,
    setGridSize,
    setLocalAnimationPhase,
    setAnimationPending,
    setRoundOutcome,
    selectTile,
    cashOut,
  } = gameHook;

  const { handleCreateSessionAndUpdateUser, publicClient, isValidSession } = useAbstractSession();

  // ------------------------------------------------ MEMO
  const gameToUse =
    ['pending_onchain', 'creation_failed'].includes(currentGame?.status ?? '') && previousGame
      ? previousGame
      : currentGame;
  const isGameOver =
    !!gameToUse &&
    !['active', 'pending_onchain', 'cashout_pending', 'cashout_failed'].includes(gameToUse.status);
  const gameId = gameToUse?.id;
  const currentGridSize =
    gameToUse && !gridSize ? gameToUse.rows[0]?.tiles : (gridSize ?? LP_GRID_SIZE);
  const currentRows =
    gameToUse && !gridSize ? gameToUse.rows : generateLaserPartyRowConfig(currentGridSize);
  const currentRowIndex = gameToUse?.currentRowIndex && !gridSize ? gameToUse.currentRowIndex : 0;
  let currentTurn = currentRowIndex;
  currentTurn = currentTurn > 0 && animationPending ? currentTurn - 1 : currentTurn;

  const shouldShowGameControls =
    (!gameId || isGameOver) &&
    !isCashingOut &&
    currentGame?.status !== 'cashout_pending' &&
    !animationPending;

  const hasWon = gameToUse?.status === 'won';
  let finalMultiplier =
    gameToUse?.status === 'lost'
      ? (gameToUse?.rows[gameToUse?.currentRowIndex]?.multiplier ?? 1)
      : (gameToUse?.finalMultiplier ?? 1);
  finalMultiplier = animationPending
    ? (gameToUse?.rows[currentTurn > 0 ? currentTurn - 1 : currentTurn]?.multiplier ?? 1)
    : finalMultiplier;
  const payoutAmount = isGameOver
    ? (BigInt(gameToUse.betAmount ?? 0) * BigInt(Math.round(finalMultiplier * 10000))) /
      BigInt(10000)
    : betAmount && finalMultiplier
      ? (betAmount * BigInt(Math.round(finalMultiplier * 10000))) / BigInt(10000)
      : BigInt(0);
  const currentBet =
    gameToUse?.status === 'active'
      ? BigInt(gameToUse.betAmount ?? 0)
      : demoMode
        ? BigInt(gameToUse?.betAmount ?? 0)
        : BigInt(betAmount);
  const commitmentHash = gameToUse?.commitmentHash;
  const gameSeed = gameToUse?.gameSeed;

  const shouldShowGameStatus =
    !!gameId ||
    isCashingOut ||
    currentGame?.status === 'cashout_pending' ||
    currentGame?.status === 'cashout_failed';
  const hasNoFunds =
    IS_MAINNET && walletAddress && !isUserBalanceLoading && userBalance < DEATH_FUN_MIN_BET_ETH;
  const isTestnetClaimEligible =
    !IS_MAINNET &&
    walletAddress &&
    userBalance < TESTNET_CLAIM_THRESHOLD &&
    userData?.testnetClaimStatus === false &&
    !isUserBalanceLoading &&
    !testnetClaimed;

  let aliveRows: number[] = DEFAULT_LASER_PARTY_ROW_CONFIG.filter(
    (row) => row.dimension === 'row'
  ).map((_, idx) => idx);
  let aliveCols: number[] = DEFAULT_LASER_PARTY_ROW_CONFIG.filter(
    (row) => row.dimension === 'col'
  ).map((_, idx) => idx);
  switch (gameToUse?.status) {
    case 'active':
      aliveRows =
        gameToUse?.rows
          ?.filter(
            (row) => row.deathTileIndex === null && (row as LPDisplayRow).dimension === 'row'
          )
          .map((_, idx) => idx) ||
        DEFAULT_LASER_PARTY_ROW_CONFIG.filter((row) => row.dimension === 'row').map(
          (_, idx) => idx
        );
      aliveCols =
        gameToUse?.rows
          ?.filter(
            (row) => row.deathTileIndex === null && (row as LPDisplayRow).dimension === 'col'
          )
          .map((_, idx) => idx) ||
        DEFAULT_LASER_PARTY_ROW_CONFIG.filter((row) => row.dimension === 'col').map(
          (_, idx) => idx
        );
      break;
    case 'won':
      aliveRows =
        currentRows
          .slice(currentRowIndex)
          // @ts-ignore
          .filter((row) => (row as LPDisplayRow).dimension === 'row')
          // @ts-ignore
          .map((_, idx) => idx) || [];
      // @ts-ignore
      aliveCols =
        currentRows
          // @ts-ignore
          .slice(currentRowIndex)
          // @ts-ignore
          .filter((row) => (row as LPDisplayRow).dimension === 'col')
          // @ts-ignore
          .map((_, idx) => idx) || [];
      break;
    case 'lost':
      aliveRows =
        currentRows
          .slice(currentRowIndex)
          // @ts-ignore
          .filter((row) => (row as LPDisplayRow).dimension === 'row')
          // @ts-ignore
          .map((_, idx) => idx) || [];
      // @ts-ignore
      aliveCols =
        currentRows
          // @ts-ignore
          .slice(currentRowIndex)
          // @ts-ignore
          .filter((row) => (row as LPDisplayRow).dimension === 'col')
          // @ts-ignore
          .map((_, idx) => idx) || [];
      break;
  }

  // ------------------------------------------------ GAME SOUNDS
  const { playGameStart } = useSounds({
    roundOutcome,
    aliveRows,
    aliveCols,
    isGameOver,
    hasWon,
    localAnimationPhase,
  });

  // ------------------------------------------------ MUTATIONS
  const { mutate: createGame, isPending: isCreatingGame } = useMutation({
    mutationKey: ['createGame'],
    mutationFn: async () => {
      const localToastId = toast.loading('Preparing game...');
      setToastId(localToastId);

      const queryParams = new URLSearchParams({ gameType: LASER_PARTY_GAME_TYPE });
      const prepareResponse = await fetch(`/api/abstract/games/create?${queryParams.toString()}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: superjson.stringify({
          betAmount: betAmount,
          gameType: LASER_PARTY_GAME_TYPE,
          gridSize: currentGridSize,
        }),
        credentials: 'include',
      });
      if (!prepareResponse.ok) {
        const errorData = await prepareResponse.json();
        throw new Error(
          `Failed to prepare game: ${errorData.error || prepareResponse.statusText}, ${JSON.stringify(
            errorData.details
          )}`
        );
      }
      const { preliminaryGameId } = await prepareResponse.json();
      return { nextGameId: preliminaryGameId };
    },
    onSuccess: async () => {
      toast.success('Game created', { id: toastId });
      playGameStart();
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
      queryClient.setQueryData(getRowConfigOptions().queryKey, (oldData: any) => ({
        ...oldData,
        fresh: false,
      }));
      setRoundOutcome(null);
      setGridSize(null);
    },
    onError: (error) => {
      console.error('Error creating game:', error);
      toast.error('Error creating game', { id: toastId, description: error.message });
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
    },
  });

  const isStartDisabled =
    betAmount <= BigInt(0) ||
    isCreatingGame ||
    currentGame?.status === 'pending_onchain' ||
    betAmount > maxBetAmount;

  // ------------------------------------------------ HANDLERS
  const handleBetChange = (bet: bigint) => {
    setBetAmount(bet);
  };

  const handleGridSizeChange = (size: number) => {
    setGridSize(size);
    setRoundOutcome(null);
    analytics.gridSizeChanged({ size });
  };

  const handleStartGame = async () => {
    if (!walletAddress || !config) {
      toast.error('Wallet not connected.');
      return;
    }
    if (!publicClient) {
      toast.error('public client not initialized');
      return;
    }
    if (betAmount <= 0) {
      toast.error('Bet amount must be greater than 0.');
      return;
    }
    if (
      !sessionConfig ||
      sessionConfig.callPolicies[0].target ===
        process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS ||
      !(await isValidSession(sessionConfig))
    ) {
      try {
        await handleCreateSessionAndUpdateUser();
      } catch (error) {
        console.error('Error creating session:', error);
        toast.error('Error creating session', { id: toastId });
        return;
      }
    }

    createGame();
  };

  const handleCashOut = async () => {
    if (isCashingOut) return;
    await cashOut();
  };

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    if (isGameOver) {
      queryClient.invalidateQueries({
        queryKey: getLeaderboardEntriesOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
    }
  }, [isGameOver, queryClient]);

  // Refetch active game when walletAddress changes (e.g., after login)
  useEffect(() => {
    if (walletAddress) {
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
    }
  }, [walletAddress, queryClient]);

  // Update bet as soon as we have currentGame and balances
  const targetBetRef = useRef(false);
  useEffect(() => {
    // Wait for data to be loaded
    if (!userBalance || targetBetRef.current || !currentGame || !currentGame.betAmount) return;
    targetBetRef.current = true;

    // First try to use the current game's bet amount
    let targetBet: bigint | null = null;

    targetBet = BigInt(currentGame.betAmount);

    // Check if the target bet is affordable and within limits
    if (targetBet && targetBet > 0 && targetBet <= userBalance && targetBet <= maxBetAmount) {
      if (betAmount !== targetBet) {
        setBetAmount(targetBet);
      }
      return;
    }
  }, [currentGame, previousGame, userBalance, maxBetAmount]);

  const createGameRef = useRef(true);
  useEffect(() => {
    if (currentGame?.status === 'pending_onchain') {
      const loadingToast = toast.loading('Preparing game...', {
        id: toastId,
      });
      setToastId(loadingToast);
      createGameRef.current = false;
    }
    if (currentGame?.status === 'active' && !createGameRef.current) {
      createGameRef.current = true;
      toast.success('Game created', { id: toastId });
    }
  }, [currentGame, toastId]);

  useEffect(() => {
    if (currentGame?.status === 'creation_failed' && !isCreatingGame) {
      toast.error('Game creation failed', { id: toastId });
    }
  }, [currentGame, toastId]);

  useEffect(() => {
    if (isCreatingGame || activeGameLoading) return;
    if (currentGame?.status === 'lost' && !roundOutcome && !gridSize) {
      const currentCell = (currentGame.selectedTiles as number[][])![
        currentGame.currentRowIndex
      ] as [number, number];
      if (!currentCell) return;
      const currentDimension = currentGame.rows[currentGame.currentRowIndex].dimension;
      setRoundOutcome({
        laserTarget: {
          dimension: currentDimension,
          index: currentCell[currentDimension === 'row' ? 0 : 1],
        },
        didPlayerDie: true,
        pick: {
          row: currentCell[0],
          col: currentCell[1],
        },
      });
      setLocalAnimationPhase('dead');
      setAnimationPending(false);
    } else if (currentGame?.status === 'lost' && roundOutcome && gridSize) {
      setRoundOutcome(null);
      setLocalAnimationPhase('dead');
      setAnimationPending(false);
    }
  }, [currentGame?.status, roundOutcome, gridSize, isCreatingGame, activeGameLoading]);

  return (
    <div
      className={cn(
        'relative flex flex-col h-full max-h-full overflow-hidden bg-black opacity-0',
        showUI && 'animate-fade-in'
      )}
    >
      <GameBoard
        gameId={gameId ?? ''}
        gameStatus={currentGame?.status ?? ''}
        aliveRows={aliveRows}
        aliveCols={aliveCols}
        roundOutcome={roundOutcome}
        handleCellClick={(row, col) => selectTile({ row, col })}
        currentTurn={currentTurn}
        maxTurns={LP_MAX_TURNS}
        currentBet={betAmount}
        isSelectingTile={isSelectingTile}
        isCashingOut={isCashingOut}
        localAnimationPhase={localAnimationPhase}
        setLocalAnimationPhase={setLocalAnimationPhase}
        setAnimationPending={setAnimationPending}
        currentGridSize={currentGridSize}
      />

      <div className='flex items-end'>
        <div
          className={cn(
            'flex flex-col justify-center space-y-4 shrink-0 border-0 border-t-2 md:border-2 container max-w-full md:max-w-2xl md:mb-6 md:rounded-3xl mx-auto p-4 py-6 md:py-4 transition-[height] duration-500 ease-in-out',
            showUI && 'animate-slide-up',
            isGameOver && !animationPending ? 'h-[17rem] sm:h-[15rem]' : 'h-[12rem] sm:h-[11rem]'
          )}
        >
          {shouldShowGameStatus && (
            <GameStatus
              isGameOver={isGameOver}
              hasWon={hasWon}
              currentBet={currentBet}
              finalMultiplier={finalMultiplier}
              onCashOut={handleCashOut}
              commitmentHash={commitmentHash ?? null}
              gameSeed={gameSeed ?? null}
              isPayoutPending={isCashingOut || currentGame?.status === 'cashout_pending'}
              isCashingOut={isCashingOut || currentGame?.status === 'cashout_pending'}
              isSelectingTile={isSelectingTile}
              payoutAmount={payoutAmount}
              payoutError={
                cashOutError || currentGame?.status === 'cashout_failed'
                  ? 'Payout failed'
                  : undefined
              }
              onRetryPayout={handleCashOut}
              updatedAt={currentGame?.updatedAt}
              gameBetAmount={currentGame?.betAmount ? BigInt(currentGame.betAmount) : undefined}
              processingTileIndex={processingTileIndex}
              currentRowIndex={currentTurn}
              rows={currentRows || []}
              gameOverText='LASERED!'
              animationPending={animationPending}
              selectedTiles={(gameToUse?.selectedTiles as number[][]) || undefined}
              gameType={LASER_PARTY_GAME_TYPE}
            />
          )}

          {shouldShowGameControls && demoMode && (
            <div className='flex flex-row gap-4 w-full justify-center items-center px-10'>
              <Button
                size='lg'
                variant='primary'
                onClick={() => {
                  setBetAmount(BigInt(demoGame.currentGame?.betAmount ?? 0));
                  demoGame.startDemo();
                }}
              >
                Play Again
              </Button>
              <div className='text-white/80 font-bold text-sm'>or</div>
              <Button
                size='lg'
                variant='white'
                onClick={() => {
                  setBetAmount(PRESET_BETS[0]);
                  setDemoMode(false);
                }}
              >
                Exit Demo
              </Button>
            </div>
          )}

          {shouldShowGameControls && !demoMode && (
            <>
              {hasNoFunds ? (
                <NoFunds showText={!gameToUse} />
              ) : isTestnetClaimEligible ? (
                <TestnetClaimButton onClaim={() => setTestnetClaimed(true)} />
              ) : (
                <GameControls
                  onStartGame={handleStartGame}
                  isStartDisabled={isStartDisabled}
                  gameCreationStatus={currentGame?.status ?? ''}
                  currentBet={currentBet}
                  gameType={LASER_PARTY_GAME_TYPE}
                  onBetChange={handleBetChange}
                  shuffleRows={() => {}}
                  onTryDemo={() => {
                    setDemoMode(true);
                    setBetAmount(BigInt(demoGame.currentGame?.betAmount ?? 0));
                    demoGame.startDemo();
                  }}
                  onShowFaq={() => window.dispatchEvent(new Event('open-faq'))}
                  transactionInProgress={
                    isCreatingGame || currentGame?.status === 'pending_onchain'
                  }
                  currentGridSize={currentGridSize}
                  onGridSizeChange={handleGridSizeChange}
                />
              )}
            </>
          )}

          <MuteButton className='xl:hidden absolute bottom-2.5 right-2.5 z-5 !mt-0 p-2 transition-colors' />
        </div>
      </div>

      <MuteButton className='hidden xl:block absolute bottom-4 right-4 z-50 bg-black/70 rounded-full p-2 hover:bg-black/90 transition-colors' />
    </div>
  );
}
