import {
  LASER_PARTY_GAME_TYPE,
  ALGO_VERSION,
  DEFAULT_LASER_PARTY_ROW_CONFIG,
} from '@/lib/constants';
import {
  CellSelection,
  LaserPartyGameState,
  LocalAnimationPhase,
  RoundOutcome,
} from '@/lib/types/laserParty';
import { useState } from 'react';
import { generateGameSeed } from '@/lib/utils/game-verification';
import { parseEther } from 'viem';
import { analytics } from '@/lib/client/analytics';
import { LPDisplayRow } from '@/components/death-race/types';
import { calculateRowMultipliers } from '@/lib/utils/game';
import { getDeathTileIndex } from '@/lib/utils/provably-fair';

function getInitialLaserPartyRowsWithDeathTiles(
  seed: string,
  baseRows?: LPDisplayRow[]
): LPDisplayRow[] {
  const rowsToUse = baseRows || DEFAULT_LASER_PARTY_ROW_CONFIG;
  const multipliers = calculateRowMultipliers(rowsToUse);
  return rowsToUse.map((row, i) => ({
    ...row,
    dimension: row.dimension,
    deathTileIndex: null,
    multiplier: multipliers[i],
  }));
}

export const useDemoGame = () => {
  const [gameSeed, setGameSeed] = useState(() => generateGameSeed());
  const [rows, setRows] = useState<LPDisplayRow[]>(() =>
    getInitialLaserPartyRowsWithDeathTiles(gameSeed)
  );
  const [currentRowIndex, setCurrentRowIndex] = useState<number>(0);
  const [selectedTiles, setSelectedTiles] = useState<number[][]>([]);
  const [finalMultiplier, setFinalMultiplier] = useState<number>(1);
  const [isGameOver, setIsGameOver] = useState(false);
  const [hasWon, setHasWon] = useState(false);
  const [isSelectingTile, setIsSelectingTile] = useState(false);
  const [processingTileIndex, setProcessingTileIndex] = useState<number | null>(null);
  const [isCashingOut, setIsCashingOut] = useState(false);
  const [roundOutcome, setRoundOutcome] = useState<RoundOutcome | null>(null);
  const [animationPending, setAnimationPending] = useState(false);
  const [localAnimationPhase, setLocalAnimationPhase] = useState<LocalAnimationPhase>('idle');
  const [gridSize, setGridSize] = useState<number | null>(null);
  const [activeGameLoading] = useState(false);

  // Mimic the shape of useGame - simple CurrentGame structure
  const currentGame = {
    id: 'demo',
    status: isGameOver ? (hasWon ? 'won' : 'lost') : 'active',
    currentRowIndex,
    finalMultiplier,
    rows,
    selectedTiles,
    commitmentHash: null,
    gameSeed,
    payoutError: null,
    payoutAmount: null,
    payoutTxSignature: null,
    betAmount: parseEther('1'),
    algoVersion: ALGO_VERSION,
    gameType: LASER_PARTY_GAME_TYPE,
    createdAt: '',
    updatedAt: '',
  };

  const selectTile = (tileVec: CellSelection) => {
    if (isSelectingTile) return;

    setRoundOutcome(null);
    const newRows = [...rows];
    const row = newRows[currentRowIndex];
    row.deathTileIndex = getDeathTileIndex(gameSeed, currentRowIndex, row.tiles);
    setIsSelectingTile(true);
    setProcessingTileIndex(row.dimension === 'row' ? tileVec.row : tileVec.col);

    setTimeout(() => {
      const isDeath =
        row.dimension === 'row'
          ? row.deathTileIndex === tileVec.row
          : row.deathTileIndex === tileVec.col;
      setRoundOutcome({
        laserTarget: {
          dimension: row.dimension,
          index: row.deathTileIndex!,
        },
        didPlayerDie: isDeath,
        pick: tileVec,
      });
      setSelectedTiles((prev) => [...prev, [tileVec.row, tileVec.col]]);

      if (isDeath) {
        setIsGameOver(true);
        setHasWon(false);
        setFinalMultiplier(0);
      } else {
        setFinalMultiplier(newRows[currentRowIndex].multiplier);
      }
      setRows(newRows);
      setCurrentRowIndex(currentRowIndex + 1);
      setAnimationPending(true);
      setProcessingTileIndex(null);
      setIsSelectingTile(false);
    }, 200);
  };
  const cashOut = async () => {
    if (isGameOver || isCashingOut) return;
    setIsCashingOut(true);
    setIsGameOver(true);
    setHasWon(true);
    setFinalMultiplier(currentRowIndex > 0 ? rows[currentRowIndex - 1].multiplier : 1);
    setIsCashingOut(false);
  };

  const startDemo = () => {
    analytics.demoPlayed({ gameType: LASER_PARTY_GAME_TYPE });
    const newSeed = generateGameSeed();
    setLocalAnimationPhase('idle');
    setRoundOutcome(null);
    setGameSeed(newSeed);
    setIsGameOver(false);
    setHasWon(false);
    setRows(getInitialLaserPartyRowsWithDeathTiles(newSeed));
    setCurrentRowIndex(0);
    setFinalMultiplier(1);
    setIsCashingOut(false);
    setIsSelectingTile(false);
    setSelectedTiles([]);
    setProcessingTileIndex(null);
    setAnimationPending(false);
  };

  const setCashoutToastId = () => {};

  return {
    currentGame: currentGame as unknown as LaserPartyGameState,
    previousGame: undefined,
    isLoading: false,
    error: null,
    isCashingOut,
    cashOutError: null,
    processingTileIndex,
    isSelectingTile,
    rowConfig: rows,
    roundOutcome,
    animationPending,
    localAnimationPhase,
    gridSize,
    activeGameLoading,
    setGridSize,
    setLocalAnimationPhase,
    setAnimationPending,
    setRoundOutcome,
    selectTile,
    cashOut,
    setCashoutToastId,
    startDemo,
  };
};
