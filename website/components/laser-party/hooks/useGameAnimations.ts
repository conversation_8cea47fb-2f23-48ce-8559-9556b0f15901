import { LASER_DURATION, LASER_HOLD, LP_SHRINK_PAUSE } from '@/lib/constants';
import { CellSelection, LaserTarget, RoundOutcome } from '@/lib/types/laserParty';
import { delay, generateLaserPartyRowConfig } from '@/lib/utils/game';
import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { LocalAnimationPhase } from '@/lib/types/laserParty';

type AnimationGrid = {
  rows: number[]; // array of row indices
  cols: number[]; // array of column indices
  keys: string[][]; // 2D array of keys corresponding to the rows and cols
};

export function useGameAnimation({
  gameId,
  roundOutcome,
  aliveRows,
  aliveCols,
  gameStatus,
  localAnimationPhase,
  setLocalAnimationPhase,
  setAnimationPending,
}: {
  gameId: string;
  roundOutcome: RoundOutcome | null;
  aliveRows: number[];
  aliveCols: number[];
  gameStatus: string;
  localAnimationPhase: LocalAnimationPhase;
  setLocalAnimationPhase: Dispatch<SetStateAction<LocalAnimationPhase>>;
  setAnimationPending: (pending: boolean) => void;
}) {
  // ------------------------------------------------ STATE

  const animationId = useRef<string | null>(null);

  const [localLaserTarget, setLocalLaserTarget] = useState<LaserTarget | null>(null);
  const [localSelectedCell, setLocalSelectedCell] = useState<CellSelection | null>(null);

  const [animationGrid, setAnimationGrid] = useState<AnimationGrid>({
    rows: [],
    cols: [],
    keys: [],
  });

  // ------------------------------------------------ MEMO

  const laserRow = (currentAnimationGrid: AnimationGrid, laserIndex: number): AnimationGrid => {
    // Find the index of the row to remove in the rows array

    // Create new arrays excluding the lasered row
    const newRows =
      gameStatus === 'lost'
        ? Array.from({ length: currentAnimationGrid.rows.length }, (_, i) => i)
        : Array.from({ length: currentAnimationGrid.rows.length - 1 }, (_, i) => i);

    const newKeys =
      gameStatus === 'lost'
        ? currentAnimationGrid.keys
        : currentAnimationGrid.keys.filter((_, index) => index !== laserIndex);

    const newAnimationGrid = {
      rows: newRows,
      cols: currentAnimationGrid.cols,
      keys: newKeys,
    };

    return newAnimationGrid;
  };

  const laserCol = (currentAnimationGrid: AnimationGrid, laserIndex: number): AnimationGrid => {
    const newCols = Array.from({ length: currentAnimationGrid.cols.length - 1 }, (_, i) => i);
    const newKeys = currentAnimationGrid.keys.map((row) =>
      row.filter((_, index) => index !== laserIndex)
    );

    return {
      rows: currentAnimationGrid.rows,
      cols: newCols,
      keys: newKeys,
    };
  };

  // ------------------------------------------------ Init Animation Grid

  const initAnimationGrid = (aliveRows: number[], aliveCols: number[]): AnimationGrid => {
    return {
      rows: aliveRows,
      cols: aliveCols,
      keys: aliveRows.map((r) => aliveCols.map((c) => uuidv4())),
    };
  };

  useEffect(() => {
    const isGridAvailable = aliveRows.length > 0 && aliveCols.length > 0;

    if (roundOutcome === null && isGridAvailable) {
      setAnimationGrid(initAnimationGrid(aliveRows, aliveCols));
    }
  }, [aliveRows, aliveCols, roundOutcome]);

  // ------------------------------------------------ Animation Helper

  const laserAnimation = async () => {
    setLocalAnimationPhase('laser');
    await delay((LASER_DURATION + LASER_HOLD) * 1000);
  };

  const shrinkingAnimation = async () => {
    setLocalAnimationPhase('shrinking');
    await delay(LP_SHRINK_PAUSE * 1000);
  };

  const cleanupAnimation = (laserTarget: LaserTarget) => {
    setLocalLaserTarget(null);
    setLocalSelectedCell(null);
    setLocalAnimationPhase('idle');

    const { dimension, index: laserIndex } = laserTarget;

    if (dimension === 'row') {
      setAnimationGrid((g) => laserRow(g, laserIndex));
    } else {
      setAnimationGrid((g) => laserCol(g, laserIndex));
    }
  };

  // ------------------------------------------------ Effects

  useEffect(() => {
    if (localAnimationPhase === 'dead') {
      // If this is initial load and game is already lost, go directly to dead
      if (roundOutcome && gameStatus === 'lost' && !localLaserTarget && !localSelectedCell) {
        setLocalLaserTarget(roundOutcome.laserTarget);
        setLocalSelectedCell(roundOutcome.pick);
        return;
      } else if (gameStatus === 'lost' && !roundOutcome) {
        setLocalLaserTarget(null);
        setLocalSelectedCell(null);
      }
    }
  }, [roundOutcome, localAnimationPhase, gameStatus]);

  const gameStartedRef = useRef(false);

  useEffect(() => {
    if (roundOutcome && localAnimationPhase === 'idle') {
      gameStartedRef.current = true;
      // Create a unique ID based on the laser target to ensure each animation runs
      const aId = `animation-id:${gameId}-${roundOutcome.laserTarget?.dimension}-${roundOutcome.laserTarget?.index}`;

      if (animationId.current !== aId) {
        animationId.current = aId;

        const animate = async () => {
          if (!roundOutcome || !roundOutcome.laserTarget) {
            console.error('useGameAnimation: Missing laser target');
            return;
          }

          setLocalLaserTarget(roundOutcome.laserTarget);
          setLocalSelectedCell(roundOutcome.pick);
          await laserAnimation();
          setAnimationPending(false);

          if (roundOutcome.didPlayerDie) {
            setLocalAnimationPhase('dying'); // Transition to dying phase first
            // Add a longer delay for dying phase to allow skull explosion and sounds to trigger
            await delay(2000); // 2 second pause for effects
            setLocalAnimationPhase((prev) => (prev === 'dying' ? 'dead' : prev));
          } else {
            await shrinkingAnimation();
            cleanupAnimation(roundOutcome.laserTarget);
          }
        };

        animate();
      }
    }
  }, [roundOutcome, localAnimationPhase, animationGrid, setAnimationPending]);

  useEffect(() => {
    if (gameStatus !== 'lost') {
      setLocalLaserTarget(null);
      setLocalSelectedCell(null);
      setLocalAnimationPhase('idle');
    }
  }, [gameStatus]);

  //  -
  return {
    localAnimationPhase,
    localLaserTarget,
    localSelectedCell,
    animationGrid,
  };
}
