import {
  getUserOptions,
  getActiveGameOptions,
  useGetActiveGame,
  useGetUser,
  getLeaderboardEntriesOptions,
} from '@/lib/client/queries';
import { useAbstractSession } from '@/lib/hooks/useAbstractSession';
import { useGambling } from '@/lib/hooks/useGambling';
import { useSounds } from '@/lib/hooks/useSounds';
import { ActiveGame, ActiveLaserPartyGame } from '@/lib/types/game';
import { decimalToFraction } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { SetStateAction, Dispatch, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { LASER_PARTY_GAME_TYPE } from '@/lib/constants';
import { LPDisplayRow } from '@/components/death-race/types';
import { CellSelection, LocalAnimationPhase, RoundOutcome } from '@/lib/types/laserParty';
import { SelectTileResponse } from '@/lib/types/api';

interface UseGameReturn {
  currentGame: ActiveLaserPartyGame['currentGame'] | undefined;
  previousGame: ActiveLaserPartyGame['previousGame'] | undefined;
  error: string | null;
  isCashingOut: boolean;
  cashOutError: Error | null;
  processingTileIndex: number | null;
  isSelectingTile: boolean;
  roundOutcome: RoundOutcome | null;
  animationPending: boolean;
  localAnimationPhase: LocalAnimationPhase;
  gridSize: number | null;
  activeGameLoading: boolean;
  setGridSize: Dispatch<SetStateAction<number | null>>;
  setLocalAnimationPhase: Dispatch<SetStateAction<LocalAnimationPhase>>;
  setAnimationPending: (done: boolean) => void;
  setRoundOutcome: (roundOutcome: RoundOutcome | null) => void;
  selectTile: (tileVec: CellSelection) => Promise<void>;
  cashOut: () => Promise<void>;
  setCashoutToastId: (toastId: string | number) => void;
}

export const useGame = (): UseGameReturn => {
  // ------------------------------------------------ STATE
  const cashoutToastIdRef = useRef<string | number | null>(null);
  const [sessionError, setSessionError] = useState<Error | null>(null);
  const [processingTileIndex, setProcessingTileIndex] = useState<number | null>(null);
  const [roundOutcome, setRoundOutcome] = useState<RoundOutcome | null>(null);
  const [localAnimationPhase, setLocalAnimationPhase] = useState<LocalAnimationPhase>('idle');
  const [animationPending, setAnimationPending] = useState(false);
  const [gridSize, setGridSize] = useState<number | null>(null);

  // ------------------------------------------------ HOOKS
  const {
    data: activeGame,
    isPending: isActiveGamePending,
    isFetching: isActiveGameFetching,
  } = useGetActiveGame<ActiveLaserPartyGame>(LASER_PARTY_GAME_TYPE);
  const { currentGame, previousGame } = activeGame ?? {};
  const { data: userData } = useGetUser();
  const { walletAddress, sessionConfig } = userData ?? {};
  const { maxPayoutAmount } = useGambling(LASER_PARTY_GAME_TYPE);
  const queryClient = useQueryClient();
  const { playCashOut } = useSounds();
  const { handleCreateSessionAndUpdateUser, isValidSession } = useAbstractSession();

  // ------------------------------------------------ MUTATIONS
  const {
    mutate: callSelectTile,
    isPending: isSelectingTile,
    error: selectTileError,
  } = useMutation({
    mutationKey: ['selectTile'],
    mutationFn: async ({
      tileVec,
      currentVersion,
    }: {
      tileVec: CellSelection;
      currentVersion: number;
    }) => {
      setRoundOutcome(null);
      const localCurrentRow = currentGame?.rows[currentGame?.currentRowIndex] as LPDisplayRow;
      const localTileIndex = localCurrentRow?.dimension === 'row' ? tileVec.row : tileVec.col;
      const response = await fetch(`/api/games/${currentGame?.id}/select-tile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          game_type: LASER_PARTY_GAME_TYPE,
          cell: [tileVec.row, tileVec.col],
          version: currentVersion,
        }),
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to select tile', { cause: (await response.json()).error });
      }
      const data = await response.json();
      const { currentRowIndex, currentRow, status, finalMultiplier, version, isDeathTile } = data;

      setRoundOutcome({
        laserTarget: {
          dimension: currentRow.dimension,
          index: currentRow.deathTileIndex!,
        },
        didPlayerDie: isDeathTile,
        pick: tileVec,
      });

      // If the game is over (lost), invalidate the query to get fresh data with game seed
      if (status === 'lost') {
        setAnimationPending(true);
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
        });
        queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
        queryClient.invalidateQueries({
          queryKey: getLeaderboardEntriesOptions(LASER_PARTY_GAME_TYPE).queryKey,
        });
        return data;
      }

      // Otherwise update the cache as before
      queryClient.setQueryData(
        getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
        // @ts-ignore
        (oldData: ActiveGame | undefined) => {
          if (!oldData) return oldData;
          const previousCurrentRowIndex = oldData.currentGame?.currentRowIndex;
          const newSelectedTiles = Array.isArray(oldData.currentGame.selectedTiles)
            ? [...oldData.currentGame.selectedTiles]
            : [];
          const newRows = [...oldData.currentGame.rows];
          if (previousCurrentRowIndex !== null && previousCurrentRowIndex !== undefined) {
            newRows[previousCurrentRowIndex] = currentRow;
            newSelectedTiles[previousCurrentRowIndex] = localTileIndex;
          }
          return {
            ...oldData,
            currentGame: {
              ...oldData.currentGame,
              currentRowIndex,
              rows: newRows as unknown as LPDisplayRow[],
              status,
              finalMultiplier,
              selectedTiles: newSelectedTiles,
              version,
            },
          };
        }
      );

      setAnimationPending(true);

      return data as SelectTileResponse;
    },
    onError: (err) => {
      if (err.cause === 'Game already updated') {
        toast.error('Game already updated');
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
        });
        return;
      }
      if (err.cause === 'Cannot select tile after cashout has been initiated') {
        toast.error('Cannot select tile after cashout has been initiated');
        queryClient.invalidateQueries({
          queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
        });
        return;
      }
      // Error already toasted inside the block or is a signature reset
      if (!(err instanceof Error && err.message === 'Signature expired or reused.')) {
        const message = err instanceof Error ? err.message : 'Failed to select tile';
        // Avoid double-toasting if API call failed
        if (!message.includes('Failed to select tile')) {
          toast.error('Operation failed', {
            description: message,
          });
        }
      }
    },
    onSuccess: () => {
      setProcessingTileIndex(null);
    },
  });

  const {
    mutate: callCashOut,
    isPending: isCashingOut,
    error: cashOutError,
  } = useMutation({
    mutationKey: ['cashOut'],
    mutationFn: async (gameId: string) => {
      setRoundOutcome(null);
      const response = await fetch(`/api/games/${gameId}/cash-out`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error((await response.json()).error || 'Failed to cash out');
      }
      return response.json();
    },
    onSuccess: (data) => {
      playCashOut();
      if (cashoutToastIdRef.current) {
        // Update the toast to show success and auto-dismiss after 2 seconds
        toast.success('Cashout successful!', {
          id: cashoutToastIdRef.current,
          duration: 2000,
        });
        cashoutToastIdRef.current = null;
      }
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
      queryClient.invalidateQueries({
        queryKey: getLeaderboardEntriesOptions(LASER_PARTY_GAME_TYPE).queryKey,
      });
    },
    onError: (err) => {
      console.error('[cashOut] Error:', err);
      toast.error(`Cash out failed!`, {
        id: cashoutToastIdRef.current ?? undefined,
        description: err.message,
      });
    },
  });

  // ------------------------------------------------ HANDLERS
  const selectTile = async (tileVec: CellSelection) => {
    if (!currentGame || !walletAddress) {
      toast.error('Cannot select tile: Missing game or connection.');
      return;
    }

    if (currentGame.status !== 'active') {
      toast.error('Game is already over');
      return;
    }

    if (currentGame.currentRowIndex < 0) {
      toast.error('No active turn');
      return;
    }

    if (!currentGame.rows) {
      toast.error('No board found');
      return;
    }

    const currentRow = currentGame.rows[currentGame.currentRowIndex] as LPDisplayRow;
    const tileIndex = currentRow?.dimension === 'row' ? tileVec.row : tileVec.col;

    if (!currentRow || tileIndex < 0 || tileIndex >= currentRow.tiles) {
      toast.error('Invalid tile selection');
      return;
    }

    // --- Max profit enforcement ---
    // Calculate the potential payout if this tile is selected (i.e., if the player survives this row)
    const betAmount = currentGame.betAmount;
    const nextRowIndex = currentGame.currentRowIndex;
    const nextMultiplier = currentGame.rows[nextRowIndex].multiplier;
    const { numerator, denominator } = decimalToFraction(nextMultiplier);
    const potentialPayout = (BigInt(betAmount) * numerator) / denominator;
    if (maxPayoutAmount && potentialPayout > maxPayoutAmount) {
      toast.error('Wow, you hit the profit limit! You must cash out now.');
      setProcessingTileIndex(null);
      return;
    }

    setProcessingTileIndex(tileIndex);
    callSelectTile({ tileVec, currentVersion: currentGame.version });
  };

  const cashOut = async (): Promise<void> => {
    if (!['active', 'cashout_pending', 'cashout_failed'].includes(currentGame?.status ?? '')) {
      toast.error('Cannot cash out: Game is not active.');
      console.warn(
        '[Cash Out] Aborted: Client game status is not active (',
        currentGame?.status,
        ')'
      );
      return;
    }

    if (!currentGame || !walletAddress || !config) {
      console.error('[Cash Out] Missing required data:', {
        currentGame,
        walletAddress,
        config,
      });
      toast.error('Cannot cash out: Missing game, wallet connection, or signature.');
      return;
    }

    if (sessionConfig && !(await isValidSession(sessionConfig))) {
      try {
        await handleCreateSessionAndUpdateUser();
      } catch (error) {
        console.error('Error renewing session:', error);
        toast.error('Error renewing session: ' + error);
        setSessionError(error as Error);
        return;
      }
    }

    cashoutToastIdRef.current = toast.loading('Processing cash out...');
    callCashOut(currentGame.id);
  };

  return {
    currentGame,
    previousGame,
    error: selectTileError?.message || cashOutError?.message || sessionError?.message || null,
    isCashingOut,
    cashOutError,
    isSelectingTile,
    processingTileIndex,
    roundOutcome,
    animationPending,
    localAnimationPhase,
    gridSize,
    activeGameLoading: isActiveGamePending || isActiveGameFetching,
    setGridSize,
    setLocalAnimationPhase,
    setAnimationPending,
    setRoundOutcome,
    selectTile,
    cashOut,
    setCashoutToastId: () => {},
  };
};
