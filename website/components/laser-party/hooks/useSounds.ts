import { useEffect, useRef } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { useGameAnimation } from './useGameAnimations';
import { LocalAnimationPhase, RoundOutcome } from '@/lib/types/laserParty';

function useAudio(src: string, volume: number) {
  const ref = useRef<HTMLAudioElement | null>(null);
  useEffect(() => {
    const audio = new Audio(src);
    audio.preload = 'auto';
    audio.volume = volume;
    ref.current = audio;
  }, [src, volume]);
  return ref;
}

function playSound(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  playingRef: React.MutableRefObject<boolean>,
  isMuted: boolean
) {
  if (isMuted) return;
  if (playingRef.current) return;
  const audio = audioRef.current;
  if (!audio) return;
  playingRef.current = true;
  audio.currentTime = 0;
  audio.play().finally(() => {
    playingRef.current = false;
  });
}

interface UseLaserPartySoundsProps {
  roundOutcome?: RoundOutcome | null;
  aliveRows?: number[];
  aliveCols?: number[];
  isGameOver?: boolean;
  hasWon?: boolean;
  localAnimationPhase: LocalAnimationPhase;
}

export function useSounds({
  roundOutcome,
  aliveRows,
  aliveCols,
  isGameOver,
  hasWon,
  localAnimationPhase,
}: UseLaserPartySoundsProps) {
  const [isMuted] = useLocalStorage<boolean>('deathrace-muted', false);

  // --- Audio refs ---
  const laserAudioRef = useAudio('/sounds/laser-party/laser2.mp3', 0.1);
  const chargingAudioRef = useAudio('/sounds/laser-party/charging2.mp3', 0.3);
  const gameOverAudioRef = useAudio('/sounds/laser-party/game-over.mp3', 0.25);
  const bowserAudioRef = useAudio('/sounds/laser-party/bowser.mp3', 0.07);
  const gameStartAudioRef = useAudio('/sounds/laser-party/game-start.mp3', 0.3);
  const cashOutAudioRef = useAudio('/sounds/laser-party/coins1.mp3', 0.2);
  const victoryAudioRef = useAudio('/sounds/laser-party/cash-out.mp3', 0.4);

  // --- Playing refs ---
  const laserPlayingRef = useRef(false);
  const chargingPlayingRef = useRef(false);
  const bowserPlayingRef = useRef(false);
  const gameOverPlayingRef = useRef(false);
  const gameStartPlayingRef = useRef(false);
  const cashOutPlayingRef = useRef(false);
  const victoryPlayingRef = useRef(false);

  function playLaser() {
    playSound(laserAudioRef, laserPlayingRef, isMuted);
  }

  function playCharging() {
    playSound(chargingAudioRef, chargingPlayingRef, isMuted);
  }

  function playDeath() {
    playSound(bowserAudioRef, bowserPlayingRef, isMuted);
    playSound(gameOverAudioRef, gameOverPlayingRef, isMuted);
  }

  function playGameStart() {
    playSound(gameStartAudioRef, gameStartPlayingRef, isMuted);
  }

  function playCashOut() {
    playSound(cashOutAudioRef, cashOutPlayingRef, isMuted);
  }

  function playVictory() {
    playSound(victoryAudioRef, victoryPlayingRef, isMuted);
  }

  function stopCharging() {
    const audio = chargingAudioRef.current;
    if (audio && !audio.paused) {
      audio.pause();
      audio.currentTime = 0;
    }
    chargingPlayingRef.current = false;
  }

  function reset() {
    /* no-op, kept for API compatibility */
  }

  // ------------------------------------------------ AUTOMATIC GAME INTEGRATION
  // Only run automatic sounds if game state props are provided
  const shouldRunAutomaticSounds =
    roundOutcome !== undefined && aliveRows !== undefined && aliveCols !== undefined;

  // Handle animation phase changes
  useEffect(() => {
    if (!shouldRunAutomaticSounds) return;

    const currentPhase = localAnimationPhase;

    // Play charging sound when entering countdown phase
    if (currentPhase === 'countdown') {
      //playCharging();
    }

    // Stop charging and play laser sound when entering laser phase
    if (currentPhase === 'laser') {
      //stopCharging();
      playLaser();
    }

    // Stop charging if we exit countdown without going to laser (shouldn't happen normally)
    if (currentPhase !== 'laser' && currentPhase !== 'countdown') {
      //stopCharging();
    }

    // Play death sounds only when explicitly entering dying phase (not on page refresh)
    if (currentPhase === 'dying' && roundOutcome?.didPlayerDie) {
      playDeath();
    }
  }, [localAnimationPhase, roundOutcome, shouldRunAutomaticSounds]);

  return {
    playLaser,
    playCharging,
    playDeath,
    playGameStart,
    playCashOut,
    playVictory,
    stopCharging,
    reset,
  };
}
