'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { List, Link as LinkIcon, User } from '@phosphor-icons/react';

import { Referral } from '@/components/Referral';
import { TotalPot } from '@/components/TotalPot';
import { Button } from '@/components/ui/button/Button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

import { useGetUser } from '@/lib/client/queries';
import { useAvailableGames } from '@/lib/hooks/flags/useAvailableGames';
import {
  GAME_META_BY_TYPE,
  IS_MAINNET,
  DOCS_LINK,
  SUPPORT_LINK,
  GameType,
} from '@/lib/constants';
import { isAdmin } from '@/lib/utils';

import MobileMenuGameButton from './MobileMenuGameButton';
import { useGameInfo } from '@/lib/hooks/useGameInfo';

export function MobileMenu() {
  const { data: user } = useGetUser();
  const { gameType } = useGameInfo();
  const admin = isAdmin(user?.role);
  const pathname = usePathname();
  const availableGames = useAvailableGames();
  const renderGames = availableGames && availableGames?.length > 1;

  const navLinks: { label: string; href: string; icon: React.ReactNode }[] = [
    { label: 'Support', href: SUPPORT_LINK, icon: <LinkIcon className='w-5 h-5' /> },
    { label: 'Docs', href: DOCS_LINK, icon: <LinkIcon className='w-5 h-5' /> },
  ];

  if (user) {
    navLinks.unshift({
      label: 'Stats',
      href: '/stats',
      icon: <User className='w-5 h-5' />,
    });
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          className='lg:hidden -ml-2 text-white/60 hover:text-white/80 data-[state=open]:bg-transparent data-[state=open]:text-white/60 active:bg-transparent focus-visible:ring-0'
        >
          <List className='w-6 h-6' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </SheetTrigger>

      <SheetContent
        side='left'
        className='w-[280px] bg-black border-r border-gc-400/20 p-6 pt-12'
      >
        <div className='space-y-6'>
          {/* Game Selector */}
          {renderGames && (
            <div className='space-y-2'>
              <div className='text-sm text-white/60'>Games</div>
              <div className='space-y-1'>
                {availableGames.map((gameName) => {
                  const game = GAME_META_BY_TYPE[gameName];
                  const isActive = pathname === game.route;

                  return (
                    <MobileMenuGameButton
                      key={game.gameType}
                      gameType={game.gameType}
                      isActive={isActive}
                    />
                  );
                })}
              </div>
            </div>
          )}

          {/* Total Pot */}
          {(!IS_MAINNET || admin) && (
            <div className='space-y-2'>
              <div className='text-sm text-white/60'>Total Pot</div>
              <TotalPot gameType={gameType ?? 'death_race'} />
            </div>
          )}

          {/* Navigation */}
          <nav className='space-y-1'>
            {admin && (
              <Link
                href='/admin/stats'
                className='flex items-center gap-2 px-3 py-2 text-sm rounded-md hover:bg-gc-900/20 text-gc-400 hover:text-white'
              >
                <User className='w-5 h-5' />
                Admin
              </Link>
            )}

            {navLinks.map(({ label, href, icon }) => (
              <Button
                key={label}
                to={href}
                variant='ghost'
                size='sm'
                className='w-full justify-start'
              >
                {icon}
                {label}
              </Button>
            ))}

            {/* Referral */}
            <div className='flex items-center gap-2 px-3 py-1.5 text-sm rounded-md hover:bg-white/10 text-white cursor-pointer'>
              <LinkIcon className='w-5 h-5' />
              <Referral className='text-white hover:text-white' />
            </div>
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
}
