import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { GAME_META_BY_TYPE, GameType, getSideNavColor } from '@/lib/constants';
import { Button } from '@/components/ui/button/Button';
import { Icon } from '@/components/ui/icons/Icon';

type MobileMenuGameButtonProps = {
  gameType: GameType;
  isActive: boolean;
};

export default function MobileMenuGameButton({ gameType, isActive }: MobileMenuGameButtonProps) {
  const router = useRouter();
  const gameMeta = GAME_META_BY_TYPE[gameType];

  const handleClick = () => {
    router.push(gameMeta.route);
  };

  const sideNavColors = getSideNavColor(gameType);

  return (
    <Button
      onClick={handleClick}
      variant='ghost'
      size='sm'
      className={cn(
        'w-full justify-start',
        isActive
          ? `bg-${sideNavColors.bg}/20 text-${sideNavColors.text}`
          : `text-${sideNavColors.text} hover:bg-${sideNavColors.bg}/20`
      )}
    >
      <Icon id={gameMeta.logoId} className={`w-5 h-5 text-${sideNavColors.text}`} />
      {gameMeta.displayName}
    </Button>
  );
}
