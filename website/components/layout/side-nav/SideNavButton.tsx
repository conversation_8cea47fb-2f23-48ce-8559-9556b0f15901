import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { GAME_META_BY_TYPE, GameType, getSideNavColor } from '@/lib/constants';
import { Button } from '@/components/ui/button/Button';
import React from 'react';
import { Icon } from '@/components/ui/icons/Icon';

type SideNavButtonProps = {
  gameType: GameType;
  isActive: boolean;
  showNewIndicator?: boolean;
};

export default function SideNavButton({
  gameType,
  isActive,
  showNewIndicator = false,
}: SideNavButtonProps) {
  const router = useRouter();
  const gameMeta = GAME_META_BY_TYPE[gameType];

  const handleClick = () => {
    router.push(gameMeta.route);
  };

  const sideNavColors = getSideNavColor(gameType);

  return (
    <Button
      onClick={handleClick}
      variant='ghost'
      size='icon'
      className={cn(
        'group w-[5.5rem] h-[5.5rem] flex flex-col items-center justify-center gap-2 border-b border-border relative transition-all rounded-none',
        isActive ? `bg-${sideNavColors.bg}/20` : `hover:bg-${sideNavColors.bg}/20`,
        isActive ? `text-${sideNavColors.text}` : `text-gray-500 hover:text-${sideNavColors.text}`
      )}
    >
      {showNewIndicator && (
        <div className='absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full leading-none animate-pulse z-10'>
          New
        </div>
      )}

      <Icon id={gameMeta.logoId} className='w-6 h-6' />

      <span className={cn('text-xs font-medium text-center transition-colors duration-200 px-2')}>
        {gameMeta.shortName}
      </span>
    </Button>
  );
}
