'use client';

import { GAME_META_BY_TYPE } from '@/lib/constants';
import { useSearchParamsContext } from '@/lib/context/ViewContext';
import { useAvailableGames } from '@/lib/hooks/flags/useAvailableGames';
import { useShowUI } from '@/lib/hooks/useShowUI';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import { useLocalStorage } from 'usehooks-ts';
import { useEffect } from 'react';
import SideNavButton from './SideNavButton';

interface SideNavProps {}

export function SideNav({}: SideNavProps) {
  const pathname = usePathname();
  const { searchParams } = useSearchParamsContext();
  const gameTypeParam = searchParams.get('gameType');
  const showUI = useShowUI(500);
  const availableGames = useAvailableGames();
  const [visitedRoutes, setVisitedRoutes] = useLocalStorage<string[]>('visitedGameRoutes', []);

  // Mark current route as visited when pathname changes
  useEffect(() => {
    if (pathname && !visitedRoutes.includes(pathname)) {
      setVisitedRoutes((prev) => [...prev, pathname]);
    }
  }, [pathname, visitedRoutes, setVisitedRoutes]);

  if (!availableGames || availableGames.length < 2) {
    return null;
  }

  return (
    <div
      className={cn(
        'hidden lg:flex bg-background border-r border-border flex-col opacity-0 z-20',
        showUI && 'animate-slide-right'
      )}
    >
      {availableGames.map((game) => {
        const gameMeta = GAME_META_BY_TYPE[game];
        const isActive = pathname === gameMeta.route || gameTypeParam === gameMeta.gameType;
        const showNewIndicator = gameMeta.new && !visitedRoutes.includes(gameMeta.route);

        return (
          <div key={gameMeta.gameType} className='relative'>
            <SideNavButton
              gameType={gameMeta.gameType}
              isActive={isActive}
              showNewIndicator={showNewIndicator}
            />
          </div>
        );
      })}
    </div>
  );
}
