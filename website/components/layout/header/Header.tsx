'use client';

import { FAQ } from '@/components/FAQ';
import { MobileMenu } from '@/components/layout/mobile-menu/MobileMenu';
import { Referral } from '@/components/Referral';
import { TotalPot } from '@/components/TotalPot';
import { WalletButton } from '@/components/WalletButton';
import { useGetUser } from '@/lib/client/queries';
import { DOCS_LINK, IS_MAINNET, SUPPORT_LINK, X_USERNAME } from '@/lib/constants';
import { useShowUI } from '@/lib/hooks/useShowUI';
import { cn, isAdmin } from '@/lib/utils';
import { useState } from 'react';
import { DeathPoints } from '@/components/DeathPoints';
import { UsernameModal } from '@/components/UsernameModal';
import { Icon } from '@/components/ui/icons/Icon';
import { Button } from '@/components/ui/button/Button';
import { Header<PERSON>ogo } from '@/components/common/HeaderLogo';
import { useGameInfo } from '@/lib/hooks/useGameInfo';
import { NoFundsModal } from '@/components/common/NoFundsModal';
import { Bridge } from '@/components/bridge/Bridge';

export function Header() {
  // ------------------------------------------------ STATE
  const [showUsernameModal, setShowUsernameModal] = useState(false);

  // ------------------------------------------------ HOOKS
  const { data: user } = useGetUser();
  const admin = isAdmin(user?.role);
  const { gameType } = useGameInfo();
  const showUI = useShowUI(500);

  // ------------------------------------------------ MEMO
  const navLinks: { label: string; href: string }[] = [
    { label: 'Support', href: SUPPORT_LINK },
    { label: 'Docs', href: DOCS_LINK },
  ];

  // ------------------------------------------------ HANDLERS

  return (
    <header
      className={cn(
        'flex items-center gap-3 p-4 shrink-0 bg-gc-900/20 border-b-2 border-gc-400/20 opacity-0',
        showUI && 'animate-slide-down'
      )}
    >
      <div className='flex items-center justify-start gap-4 flex-1'>
        {(!IS_MAINNET || admin) && (
          <div className='hidden lg:flex items-center pr-4'>
            <TotalPot gameType={gameType} />
          </div>
        )}
        <div className='flex items-center gap-5'>
          <MobileMenu />
          <FAQ gameType={gameType} />
          <NoFundsModal />
          <Bridge />

          {navLinks.map(({ label, href }) => (
            <Button
              key={label}
              variant='link'
              size='none'
              to={href}
              className='hidden lg:flex text-sm'
            >
              {label}
            </Button>
          ))}

          <Referral className='hidden lg:flex' />

          {user && (
            <Button variant='link' size='none' to='/stats' className='hidden lg:flex text-sm'>
              Stats
            </Button>
          )}

          <Button
            variant='ghost'
            size='icon'
            to={`https://x.com/${X_USERNAME}`}
            aria-label='X (Twitter)'
          >
            <Icon id='x' className='w-[1rem] sm:w-[1.1rem] h-auto text-white sm:text-gc-400' />
          </Button>
        </div>
      </div>

      <HeaderLogo />

      <div className='flex-1 flex items-center justify-end gap-6'>
        <div className='flex items-center gap-2'>
          {admin && (
            <Button to='/admin/stats' variant='ghost' size='sm' className='hidden lg:flex'>
              Admin
            </Button>
          )}
          <DeathPoints points={user?.points || 0} />
          <WalletButton onEditUsername={() => setShowUsernameModal(true)} />
        </div>
      </div>

      <UsernameModal
        isOpen={showUsernameModal}
        onClose={() => {
          setShowUsernameModal(false);
        }}
      />
    </header>
  );
}
