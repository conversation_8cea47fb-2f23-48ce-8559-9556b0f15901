import { TextInput } from '@/components/ui/input/TextInput';
import { SelectInput } from '@/components/ui/input/SelectInput';
import { NumberInput } from '@/components/ui/input/NumberInput';

export function InputSection() {
  const textInputSizes = ['sm', 'md', 'lg'] as const;
  const textInputVariants = ['default', 'readOnly'] as const;

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold mb-6">Input Components</h2>
      
      {/* Text Input Section */}
      <div className="space-y-8 mb-12">
        <h3 className="text-xl font-medium">Text Input</h3>
        
        {/* Sizes */}
        <div className="border border-border rounded-lg p-6">
          <h4 className="text-lg font-medium mb-4">Sizes</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {textInputSizes.map((size) => (
              <div key={size} className="flex flex-col space-y-2">
                <span className="text-sm text-muted-foreground capitalize">{size}</span>
                <TextInput 
                  size={size} 
                  placeholder={`${size} size input`}
                  className="w-full"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Variants */}
        <div className="border border-border rounded-lg p-6">
          <h4 className="text-lg font-medium mb-4">Variants</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {textInputVariants.map((variant) => (
              <div key={variant} className="flex flex-col space-y-2">
                <span className="text-sm text-muted-foreground capitalize">{variant}</span>
                <TextInput 
                  variant={variant} 
                  placeholder={`${variant} variant`}
                  value={variant === 'readOnly' ? 'Read-only value' : ''}
                  className="w-full"
                />
              </div>
            ))}
          </div>
        </div>

        {/* States */}
        <div className="border border-border rounded-lg p-6">
          <h4 className="text-lg font-medium mb-4">States</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Default</span>
              <TextInput placeholder="Enter text..." className="w-full" />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Label</span>
              <TextInput label="Username" placeholder="Enter username" className="w-full" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm text-muted-foreground">With Error</span>
              <TextInput 
                label="Email"
                size='sm'
                placeholder="Enter email" 
                error="Please enter a valid email address"
                className="w-full" 
              />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Disabled</span>
              <TextInput 
                label="Disabled Field" 
                placeholder="This field is disabled" 
                disabled
                className="w-full" 
              />
            </div>
          </div>
        </div>
      </div>

      {/* Select Input Section */}
      <div className="space-y-8 mb-12">
        <h3 className="text-xl font-medium">Select Input</h3>

        <div className="border border-border rounded-lg p-6">
          <h4 className="text-lg font-medium mb-4">Select Input Examples</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Default</span>
              <SelectInput className="w-full">
                <option value="">Select an option</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Size: Lg</span>
              <SelectInput size="lg" className="w-full">
                <option value="">Select an option</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Label</span>
              <SelectInput label="Game Type" className="w-full">
                <option value="">Select game type</option>
                <option value="death_race">Death Race</option>
                <option value="laser_party">Laser Party</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Error</span>
              <SelectInput 
                label="Difficulty" 
                error="Please select a difficulty level"
                className="w-full"
              >
                <option value="">Select difficulty</option>
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Disabled</span>
              <SelectInput 
                label="Disabled Select" 
                disabled
                className="w-full"
              >
                <option value="">Disabled options</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Default Value</span>
              <SelectInput label="Theme" defaultValue="dark" className="w-full">
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
              </SelectInput>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Required</span>
              <SelectInput 
                label="Required Select" 
                required
                className="w-full"
              >
                <option value="">Please select</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </SelectInput>
            </div>
          </div>
        </div>
      </div>

      {/* Number Input Section */}
      <div className="space-y-8 mb-12">
        <h3 className="text-xl font-medium">Number Input</h3>
        
        <div className="border border-border rounded-lg p-6">
          <h4 className="text-lg font-medium mb-4">Number Input Examples</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Default</span>
              <NumberInput placeholder="Enter number" className="w-full" />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Label</span>
              <NumberInput label="Bet Amount" placeholder="0.01" className="w-full" />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Error</span>
              <NumberInput 
                label="Age" 
                placeholder="Enter age" 
                error="Age must be between 18 and 100"
                className="w-full" 
              />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Min/Max</span>
              <NumberInput 
                label="Score" 
                placeholder="0-100" 
                min={0}
                max={100}
                className="w-full" 
              />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">With Step</span>
              <NumberInput 
                label="Percentage" 
                placeholder="0-100" 
                min={0}
                max={100}
                step={5}
                className="w-full" 
              />
            </div>
            <div className="flex flex-col space-y-2">
              <span className="text-sm text-muted-foreground">Decimal Step</span>
              <NumberInput 
                label="Price" 
                placeholder="0.00" 
                min={0}
                step={0.01}
                className="w-full" 
              />
            </div>
          </div>
        </div>
      </div>

      {/* Interactive Examples */}
      <div className="space-y-8">
        <h3 className="text-xl font-medium">Interactive Examples</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="border border-border rounded-lg p-6">
            <h4 className="text-lg font-medium mb-4">Form Example</h4>
            <div className="space-y-4">
              <TextInput 
                label="Player Name" 
                placeholder="Enter your name" 
                className="w-full" 
              />
              <SelectInput label="Game Mode" className="w-full">
                <option value="">Select game mode</option>
                <option value="casual">Casual</option>
                <option value="competitive">Competitive</option>
                <option value="tournament">Tournament</option>
              </SelectInput>
              <NumberInput 
                label="Bet Amount (ETH)" 
                placeholder="0.01" 
                min={0.001}
                step={0.001}
                className="w-full" 
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 