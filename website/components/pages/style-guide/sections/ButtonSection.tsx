import { Button } from '@/components/ui/button/Button';
import { Shuffle } from '@phosphor-icons/react';

export function ButtonSection() {
  const variants = [
    'default',
    'outline', 
    'primary',
    'primary-outline',
    'secondary',
    'ghost',
    'link',
    'white',
    'destructive'
  ] as const;

  const sizes = ['sm', 'default', 'lg', 'xl', '2xl', 'icon'] as const;

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold mb-6">Button Variants & Sizes</h2>
      
      <div className="space-y-8">
        {variants.map((variant) => (
          <div key={variant} className="border border-border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4 capitalize">{variant}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sizes.map((size) => (
                <div key={size} className="flex flex-col items-start space-y-2">
                  <span className="text-sm text-muted-foreground capitalize">
                    {size === 'default' ? 'default' : size}
                  </span>
                  <Button variant={variant} size={size}>
                    {size === 'icon' ? <Shuffle className='w-5 h-5' /> : `${variant} ${size}`}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Disabled States</h2>
        
        <div className="border border-border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Primary (disabled)</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sizes.map((size) => (
              <div key={size} className="flex flex-col items-start space-y-2">
                <span className="text-sm text-muted-foreground capitalize">
                  {size === 'default' ? 'default' : size}
                </span>
                <Button variant="primary" size={size} disabled>
                  {size === 'icon' ? <Shuffle className='w-5 h-5' /> : `primary ${size}`}
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Interactive Examples</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="border border-border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Primary Actions</h3>
            <div className="space-y-3">
              <Button variant="primary" size="lg" className="w-full">
                Start Game
              </Button>
              <Button variant="primary" size="default" className="w-full">
                Cash Out
              </Button>
              <Button variant="primary" size="sm" className="w-full">
                Place Bet
              </Button>
            </div>
          </div>

          <div className="border border-border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Secondary Actions</h3>
            <div className="space-y-3">
              <Button variant="secondary" size="lg" className="w-full">
                View Stats
              </Button>
              <Button variant="outline" size="default" className="w-full">
                Settings
              </Button>
              <Button variant="ghost" size="sm" className="w-full">
                Help
              </Button>
            </div>
          </div>

          <div className="border border-border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Destructive Actions</h3>
            <div className="space-y-3">
              <Button variant="destructive" size="lg" className="w-full">
                Delete Game
              </Button>
              <Button variant="destructive" size="default" className="w-full">
                Reset Progress
              </Button>
              <Button variant="destructive" size="sm" className="w-full">
                Clear Data
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Icon Buttons</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {variants.map((variant) => (
            <div key={variant} className="flex flex-col items-center space-y-2">
              <Button variant={variant} size="icon">
                <Shuffle className='w-5 h-5' />
              </Button>
              <span className="text-xs text-muted-foreground capitalize">{variant}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Rendering</h2>
        
        <div className="border border-border rounded-lg p-6">
          <p className="text-muted-foreground mb-6">
            The Button component renders the appropriate HTML element based on the props you pass. 
            It detects internal links (using Next.js Link), external links (and adds proper security attributes), 
            and buttons with onClick events.
          </p>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Button Element</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
{`<Button onClick={handleClick}>
  Click me
</Button>`}
                </pre>
              </div>
              <Button onClick={() => alert('Button clicked!')}>
                Click me
              </Button>
              <div className="bg-muted p-4 rounded-md">
                <h4 className="text-sm font-medium mb-2">Renders as:</h4>
                <pre className="text-sm overflow-x-auto text-muted-foreground">
{`<button class="inline-flex items-center...">
  Click me
</button>`}
                </pre>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Internal Link</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
{`<Button to="/stats">
  View Stats
</Button>`}
                </pre>
              </div>
              <Button to="/stats">
                View Stats
              </Button>
              <div className="bg-muted p-4 rounded-md">
                <h4 className="text-sm font-medium mb-2">Renders as:</h4>
                <pre className="text-sm overflow-x-auto text-muted-foreground">
{`<a href="/stats" class="inline-flex items-center...">
  View Stats
</a>`}
                </pre>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">External Link</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
{`<Button to="https://example.com">
  External Link
</Button>`}
                </pre>
              </div>
              <Button to="https://example.com">
                External Link
              </Button>
              <div className="bg-muted p-4 rounded-md">
                <h4 className="text-sm font-medium mb-2">Renders as:</h4>
                <pre className="text-sm overflow-x-auto text-muted-foreground">
{`<a href="https://example.com" 
   target="_blank" 
   rel="noopener noreferrer" 
   class="inline-flex items-center...">
  External Link
</a>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  );
}
