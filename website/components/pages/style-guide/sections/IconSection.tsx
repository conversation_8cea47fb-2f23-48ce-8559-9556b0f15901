import { Icon } from '@/components/ui/icons/Icon';

export function IconSection() {
  const iconIds = [
    'laser',
    'logo',
    'eth',
    'dp-icon-face',
    'x'
  ] as const;

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold mb-6">Icons</h2>
      
      <div className="space-y-6">
        {/* Icon Showcase */}
        <div className="border border-border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Available Icons</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {iconIds.map((id) => (
              <div key={id} className="flex flex-col items-center space-y-2">
                <Icon id={id} className="w-8 h-8" />
                <span className="text-xs text-muted-foreground font-mono">{id}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Code Usage */}
        <div className="border border-border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Usage</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-md font-medium">Basic Usage</h4>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
{`import { Icon } from '@/components/ui/icons/Icon';

<Icon id="dp-icon-face" className="w-6 h-6" />
<Icon id="eth" className="w-5 h-5 text-primary" />
<Icon id="laser" className="w-8 h-8 text-red-500" />`}
                </pre>
              </div>
              <div className="flex items-center space-x-4">
                <Icon id="dp-icon-face" className="w-6 h-6" />
                <Icon id="eth" className="w-5 h-5 text-primary" />
                <Icon id="laser" className="w-8 h-8 text-red-500" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
