import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button/Button';
import { GameController, Trophy, Users, Wallet } from '@phosphor-icons/react';

export function CardSection() {
  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold mb-6">Card Components</h2>
      
      <div className="space-y-8">
        {/* Default Card */}
        <div className="border border-border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Default Card</h3>
          <p className="text-sm text-muted-foreground mb-4">
            The default card uses <code className="bg-muted px-1 rounded">bg-gc-900/20 border-gc-400/20</code> styling
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Simple Card</CardTitle>
                <CardDescription>A basic card with title and description</CardDescription>
              </CardHeader>
              <CardContent>
                <p className='text-sm'>This is the default card styling used throughout the application.</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Usage Examples */}
        <div className="border border-border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Usage Examples</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-md font-medium">Basic Usage</h4>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
{`<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content goes here
  </CardContent>
</Card>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 