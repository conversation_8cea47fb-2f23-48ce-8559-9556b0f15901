'use client';

import { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog/Dialog';
import { Button } from '@/components/ui/button/Button';
import { toast } from 'sonner';
import { CrossAppAccountWithMetadata, getAccessToken, usePrivy } from '@privy-io/react-auth';
import { getCrossAppWalletAddress } from '@/lib/client/wallet';
import { getUserOptions, useGetUser } from '@/lib/client/queries';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import superjson from 'superjson';
import { REFERRAL_STORAGE_KEY } from '@/lib/client/constants';
import { useLocalStorage } from 'usehooks-ts';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X as XIcon } from '@phosphor-icons/react';
import { TextInput } from './ui/input/TextInput';

interface UsernameModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UsernameModal({ isOpen, onClose }: UsernameModalProps) {
  // ------------------------------------------------ HOOKS
  const { user } = usePrivy();
  const walletAddress = getCrossAppWalletAddress(
    user?.linkedAccounts as CrossAppAccountWithMetadata[]
  );
  const { data: userData, error: userError } = useGetUser();
  const queryClient = useQueryClient();

  // ------------------------------------------------ STATE
  const [username, setUsername] = useState(userData?.username || '');
  const [error, setError] = useState<string | null>(null);
  const [referralCode] = useLocalStorage<string | null>(REFERRAL_STORAGE_KEY, null);

  // ------------------------------------------------ MUTATIONS
  const { mutate: createUser, isPending: isCreateUserPending } = useMutation({
    mutationKey: ['createUser'],
    mutationFn: async (localUsername: string) => {
      // --- renew access token if expired
      await getAccessToken();

      // --- fetch referral code from localStorage (if any)
      let storedReferralCode: string | undefined;
      if (referralCode && referralCode !== 'null') {
        storedReferralCode = referralCode.replace(/['"]/g, '');
      }

      // --- create user
      const bodyPayload: Record<string, unknown> = {
        username: localUsername,
      };
      if (storedReferralCode) {
        bodyPayload.referralCode = storedReferralCode;
      }
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: superjson.stringify(bodyPayload),
        credentials: 'include',
      });
      let data;
      try {
        data = await response.json();
      } catch {}
      if (!response.ok) {
        throw data?.error ? { error: data.error } : new Error('Failed to create user');
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getUserOptions(true).queryKey });
      onClose();
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to save username';
      if (
        typeof error === 'object' &&
        error !== null &&
        'error' in error &&
        typeof error.error === 'string'
      ) {
        setError(error.error);
      } else {
        setError(message);
      }
    },
  });

  const { mutate: updateUser, isPending: isUpdateUserPending } = useMutation({
    mutationKey: ['updateUser'],
    mutationFn: async (localUsername: string) => {
      await getAccessToken();

      const response = await fetch('/api/users', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: superjson.stringify({ username: localUsername }),
        credentials: 'include',
      });
      let data;
      try {
        data = await response.json();
      } catch {}
      if (!response.ok) {
        throw data?.error ? { error: data.error } : new Error('Failed to update user');
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getUserOptions(true).queryKey });
      toast.success('Username updated successfully');
      onClose();
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to save username';
      if (
        typeof error === 'object' &&
        error !== null &&
        'error' in error &&
        typeof error.error === 'string'
      ) {
        setError(error.error);
      } else {
        setError(message);
      }
    },
  });

  // ------------------------------------------------ HANDLERS
  const validateUsername = (username: string): string | null => {
    if (!username.trim()) return 'Username is required';
    if (username.length > 12) return 'Username must be 12 characters or less';
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      return 'Username can only contain letters, numbers, underscores, and dashes';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!walletAddress || !username.trim() || isCreateUserPending || isUpdateUserPending) return;

    const validationError = validateUsername(username);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);

    if (!userError) {
      updateUser(username);
    } else {
      createUser(username);
    }
  };

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    if (userData?.username) {
      setUsername(userData.username);
    }
  }, [userData?.username]);

  useEffect(() => {
    if (isOpen) {
      setUsername(userData?.username || '');
      setError(null);
    }
  }, [isOpen, userData?.username]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open && userData?.username) {
          onClose();
        }
      }}
    >
      <DialogContent className='sm:max-w-[25rem] bg-black border border-white/10'>
        {userData?.username && (
          <DialogPrimitive.Close className='absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'>
            <XIcon className='h-4 w-4' />
            <span className='sr-only'>Close</span>
          </DialogPrimitive.Close>
        )}
        <DialogHeader>
          <DialogTitle className='text-white text-center'>Choose a username</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <TextInput
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder='Enter username'
            className='w-full'
            disabled={isCreateUserPending || isUpdateUserPending}
            maxLength={12}
            error={error ?? ''}
          />
          <Button
            type='submit'
            variant='primary'
            className='w-full mt-4'
            disabled={!username.trim() || isCreateUserPending || isUpdateUserPending}
          >
            {isCreateUserPending || isUpdateUserPending ? 'Saving...' : 'Save Username'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
