import React from 'react'
import Link from 'next/link'

type AnchorType = {
  to: string
  ariaLabel?: string
} & React.AnchorHTMLAttributes<HTMLAnchorElement>

type ButtonType = React.ButtonHTMLAttributes<HTMLButtonElement>

export type ClickableType = (ButtonType & { to?: string }) | AnchorType

const externalProps = { target: '_blank', rel: 'noopener noreferrer' }

const Anchor = ({
  ariaLabel,
  to,
  className,
  style,
  children,
  target,
  onClick,
  ...p
}: AnchorType) => {
  const props = { className, style, target, 'aria-label': ariaLabel, onClick }

  if (/^[./]/.test(to)) {
    return (
      <Link href={to} scroll={false} {...props}>
        {children}
      </Link>
    )
  }

  // external links
  if (/^https?:/.test(to))
    return (
      <a {...p} {...props} href={to} {...externalProps}>
        {children}
      </a>
    )

  // fallback for anything else
  return (
    <a {...p} {...props} href={to}>
      {children}
    </a>
  )
}

const Clickable = ({ to, ...props }: ClickableType) => {
  if (!to) return <button {...(props as ButtonType)} />
  return <Anchor {...(props as AnchorType)} to={to} />
}

export default Clickable
