import * as React from 'react';
import { cn } from '@/lib/utils';
import Clickable, { ClickableType } from './Clickable';

type Variant = 'default' | 'outline' | 'primary' | 'primary-outline' | 'secondary' | 'ghost' | 'link' | 'white' | 'destructive';
type Size = 'xs' | 'sm' | 'default' | 'lg' | 'xl' | '2xl' | 'icon' | 'none';

export type ButtonProps = {
  variant?: Variant;
  size?: Size;
  className?: string;
  children?: React.ReactNode;
  as?: typeof Clickable;
  disabled?: boolean;
} & ClickableType;

export const variantClasses: Record<Variant, string> = {
  default: 'bg-primary text-white hover:bg-primary/90',
  outline: 'border shadow-[inset_0_0_0_1px] shadow-gray-300 hover:bg-gc-500/10 text-white',
  secondary: 'text-gc-400 bg-gc-900/30 hover:bg-gc-900/40',
  primary: 'bg-gc-400 hover:bg-gc-500 text-gray-900',
  'primary-outline': 'bg-black text-gc-500 shadow-[inset_0_0_0_1px] shadow-gc-500 hover:bg-gc-500 hover:text-black',
  link: 'text-white/60 hover:text-white/80',
  white: 'bg-white text-black border-white/60 font-semibold hover:bg-gray-200',
  ghost: 'hover:bg-gray-100/10',
  destructive: 'bg-red-500 text-white hover:bg-red-600',
};

const sizeClasses: Record<Size, string> = {
  xs: 'text-xs px-1.5 py-1 gap-1',
  sm: 'text-sm px-3 py-1.5',
  default: 'text-sm px-4 py-2',
  lg: 'text-base px-6 py-3',
  xl: 'text-lg px-6 py-4',
  '2xl': 'text-xl px-8 py-5',
  icon: 'w-9 h-9 flex items-center justify-center',
  none: '',
};

const FallbackComponent = Clickable;


const Button = ({ 
  className, 
  variant = 'default', 
  size = 'default', 
  as: Component = FallbackComponent, 
  children,
  disabled,
  ...props 
}: ButtonProps) => {
  return (
    <Component
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors gap-2',
        variantClasses[variant],
        sizeClasses[size],
        className,
        disabled && 'opacity-50 pointer-events-none'
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

export { Button };