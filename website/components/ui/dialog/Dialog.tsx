'use client';

import {
  Root as Dialog,
  Trigger as DialogTrigger,
  Portal as DialogPortal,
  Overlay as RadixDialogOverlay,
  Content as RadixDialogContent,
  Close as DialogClose,
  Title as DialogTitle,
  Description as DialogDescription,
} from '@radix-ui/react-dialog';

import { X as XIcon } from '@phosphor-icons/react';
import { cn } from '@/lib/utils';

export { Dialog, DialogTrigger, DialogPortal, DialogClose, DialogTitle, DialogDescription };

export function DialogOverlay({ className, ...props }: React.ComponentPropsWithoutRef<typeof RadixDialogOverlay>) {
  return (
    <RadixDialogOverlay
      className={cn(
        'fixed inset-0 z-50 bg-[#111]/80 backdrop-blur-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0 flex items-center justify-center',
        className
      )}
      {...props}
    />
  );
}

export function DialogContent({
  className,
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof RadixDialogContent>) {
  return (
    <DialogPortal>
      <DialogOverlay />
      <RadixDialogContent
        className={cn(
          'z-50 fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-lg rounded-lg border border-gc-400/50 bg-black p-6 shadow-lg flex flex-col gap-4',
          'data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0',
          'data-[state=open]:animate-in data-[state=closed]:animate-out',
          'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
          'data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]',
          className
        )}
        {...props}
      >
        {children}
        <DialogClose className="absolute right-4 top-4 opacity-70 transition-opacity hover:opacity-100 focus:outline-none">
          <XIcon className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </RadixDialogContent>
    </DialogPortal>
  );
}

export function DialogHeader({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn('flex flex-col space-y-1.5 text-center', className)} {...props} />;
}

export function DialogFooter({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2', className)} {...props} />;
}
