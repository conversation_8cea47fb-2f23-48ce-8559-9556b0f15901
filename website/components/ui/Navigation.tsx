'use client';

import { BottomNav } from '@/components/BottomNav';
import { Header } from '@/components/layout/header/Header';
import { SearchParamsProvider } from '@/lib/context/ViewContext';

interface NavigationPropsWithChildren {
  children: React.ReactNode;
}

export const Navigation: React.FC<NavigationPropsWithChildren> = ({ children }) => {
  return (
    <>
      <Header />
      <div className='flex-1 min-h-0 overflow-y-auto'>
        <SearchParamsProvider>{children}</SearchParamsProvider>
      </div>
      <BottomNav />
    </>
  );
};
