'use client';

import { Toaster as Sonner } from 'sonner';

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      theme='dark'
      className='toaster group'
      offset={{ bottom: '6rem', right: '1rem' }}
      mobileOffset={{ top: '5rem', right: '1rem' }}
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-black group-[.toaster]:text-white group-[.toaster]:border-border group-[.toaster]:shadow-lg',
          title: 'group-[.toast]:text-white',
          description: 'group-[.toast]:text-gray-400',
          actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
          cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
          success: 'group-[.toaster]:!text-gc-500',
          error: 'group-[.toaster]:!text-red-500',
          loading: 'group-[.toaster]:!text-white',
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
