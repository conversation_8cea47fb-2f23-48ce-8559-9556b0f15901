import * as React from 'react';
import { cn } from '@/lib/utils';
interface FieldContextValue {
  id: string;
  describedBy: string;
  hasError: boolean;
  disabled?: boolean;
}

const FieldContext = React.createContext<FieldContextValue | null>(null);

const useFieldContext = () => {
  const context = React.useContext(FieldContext);
  if (!context) throw new Error('Field component must be used within <Field.Root>');
  return context;
}

interface RootProps extends React.HTMLAttributes<HTMLDivElement> {
  id?: string;
  error?: string;
  disabled?: boolean;
  children: React.ReactNode;
}

const Root = ({ id, error, disabled, children, ...props }: RootProps) => {
  const fieldId = id ?? React.useId();
  const errorId = `${fieldId}-error`;

  const contextValue: FieldContextValue = {
    id: fieldId,
    describedBy: error ? errorId : '',
    hasError: !!error,
    disabled,
  };

  return (
    <FieldContext.Provider value={contextValue}>
      <div {...props}>
        {children}
        {error && (
          <p id={errorId} className="mt-1 text-xs text-destructive" role="alert">
            {error}
          </p>
        )}
      </div>
    </FieldContext.Provider>
  );
};

const Label = ({ className, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) => {
  const { id, disabled } = useFieldContext();
  return (
    <label
      htmlFor={id}
      className={cn('text-xs font-semibold text-gc-400 mb-1', disabled && 'opacity-50', className)}
      {...props}
    />
  );
};

// Shared base props for input-like components
interface BaseInputProps {
  variant?: 'default' | 'readOnly';
  inputSize?: 'sm' | 'md' | 'lg';
}

// Shared styling logic
const getInputClasses = (variant: 'default' | 'readOnly' = 'default', inputSize: 'sm' | 'md' | 'lg' = 'md', hasError: boolean) => {
  return cn(
    'flex w-full rounded-md border px-3 text-base shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
    hasError ? 'border-destructive focus-visible:ring-destructive' : 'border-input focus-visible:ring-ring',
    // Variant classes
    {
      'bg-black text-white text-sm border-neutral-700 focus:border-gc-500 focus:ring-gc-500 placeholder:text-neutral-500':
        variant === 'default',
      'bg-black/50 font-mono text-sm border-white/10':
        variant === 'readOnly',
    },
    // Size classes
    {
      'py-1.5': inputSize === 'sm',
      'py-2': inputSize === 'md',
      'py-3.5': inputSize === 'lg',
    }
  );
};

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement>, BaseInputProps {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, variant = 'default', inputSize = 'md', ...props }, ref) => {
    const { id, describedBy, hasError, disabled } = useFieldContext();
    return (
      <input
        ref={ref}
        id={id}
        aria-describedby={describedBy || undefined}
        aria-invalid={hasError || undefined}
        disabled={disabled}
        className={cn(getInputClasses(variant, inputSize, hasError), className)}
        {...props}
      />
    );
  }
);
Input.displayName = 'Field.Input';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement>, BaseInputProps {}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, variant = 'default', inputSize = 'md', ...props }, ref) => {
    const { id, describedBy, hasError, disabled } = useFieldContext();
    return (
      <select
        ref={ref}
        id={id}
        aria-describedby={describedBy || undefined}
        aria-invalid={hasError || undefined}
        disabled={disabled}
        className={cn(getInputClasses(variant, inputSize, hasError), className)}
        {...props}
      />
    );
  }
);
Select.displayName = 'Field.Select';

export const Field = {
  Root,
  Label,
  Input,
  Select,
};
