import * as React from 'react';
import { cn } from '@/lib/utils';
import { Field } from './Field';

interface TextInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  variant?: 'default' | 'readOnly';
  id?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const TextInput = React.forwardRef<HTMLInputElement, TextInputProps>(
  ({ label, error, className, variant = 'default', id, size = 'md', ...props }, ref) => {

    const autoId = React.useId();
    const inputId = id ?? autoId;
    const ariaLabelFallback = !label ? { 'aria-label': props.placeholder ?? 'Text input' } : {};

    return (
      <Field.Root id={inputId} error={error} disabled={props.disabled} className={cn(className)}>
        {label && <Field.Label>{label}</Field.Label>}
        <Field.Input
          type="text"
          ref={ref}
          variant={variant}
          inputSize={size}
          readOnly={variant === 'readOnly'}
          {...props}
          {...ariaLabelFallback}
        />
      </Field.Root>
    );
  }
);

TextInput.displayName = 'TextInput';
