import * as React from 'react';
import { cn } from '@/lib/utils';
import { Field } from './Field';

interface NumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  variant?: 'default';
  size?: 'sm' | 'md' | 'lg';
}

export const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  ({ label, error, className, variant = 'default', size = 'md', ...props }, ref) => {
    const ariaLabelFallback = !label ? { 'aria-label': props.placeholder ?? 'Number input' } : {};

    return (
      <Field.Root error={error} disabled={props.disabled} className={cn(className)}>
        {label && <Field.Label>{label}</Field.Label>}
        <Field.Input
          type="number"
          ref={ref}
          inputMode="decimal"
          variant={variant}
          inputSize={size}
          min={props.min ?? 0}
          step={props.step ?? 'any'}
          {...props}
          {...ariaLabelFallback}
        />
      </Field.Root>
    );
  }
);

NumberInput.displayName = 'NumberInput';
