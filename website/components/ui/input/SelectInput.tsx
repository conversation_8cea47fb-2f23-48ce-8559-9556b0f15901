import * as React from 'react';
import { Field } from './Field';
import { cn } from '@/lib/utils';

interface SelectInputProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  label?: string;
  error?: string;
  id?: string;
  variant?: 'default';
  size?: 'sm' | 'md' | 'lg';
}

export const SelectInput = React.forwardRef<HTMLSelectElement, SelectInputProps>(
  ({ label, error, className, id, variant = 'default', size = 'md', children, ...props }, ref) => {
    const autoId = React.useId();
    const selectId = id ?? autoId;

    return (
      <Field.Root error={error} disabled={props.disabled} id={selectId} className={cn(className)}>
        {label && <Field.Label>{label}</Field.Label>}
        <Field.Select
          ref={ref}
          id={selectId}
          variant={variant}
          inputSize={size}
          {...props}
        >
          {children}
        </Field.Select>
      </Field.Root>
    );
  }
);

SelectInput.displayName = 'SelectInput';