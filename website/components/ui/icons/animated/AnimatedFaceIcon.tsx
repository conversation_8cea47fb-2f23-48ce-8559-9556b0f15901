import { cn } from '@/lib/utils';

type AnimationType = 'laugh' | 'headache';

interface AnimatedFaceIconProps {
  className?: string;
  animation?: AnimationType;
  isAnimating?: boolean;
}

export const AnimatedFaceIcon = ({ className, animation = 'laugh', isAnimating = false }: AnimatedFaceIconProps) => {
  const laughingIcon = () => (
    <svg className={className} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g className={cn('face', isAnimating && 'animate-laugh delay-600')}>
        <path className={cn('mouth duration-200 ease-out delay-600', isAnimating ? 'opacity-0' : 'opacity-1')} d="M46.53 53.85C46.08 53.85 45.63 53.68 45.29 53.34L43.17 51.22L41.05 53.34C40.37 54.02 39.26 54.02 38.58 53.34L36.46 51.22L34.34 53.34C33.66 54.02 32.55 54.02 31.87 53.34L28.51 49.98C27.83 49.3 27.83 48.19 28.51 47.51C29.19 46.83 30.3 46.83 30.98 47.51L33.1 49.63L35.22 47.51C35.9 46.83 37.01 46.83 37.69 47.51L39.81 49.63L41.93 47.51C42.61 46.83 43.72 46.83 44.4 47.51L46.52 49.63L48.64 47.51C49.32 46.83 50.43 46.83 51.11 47.51C51.79 48.19 51.79 49.3 51.11 49.98L47.75 53.34C47.41 53.68 46.96 53.85 46.51 53.85H46.53Z" fill="currentColor"/>
        <path className={cn('smile origin-center', isAnimating ? 'opacity-1 animate-mouth-laugh delay-600' : 'opacity-0')} d="M40.39 66.53C28.6 66.53 19 57.66 19 46.75C19 45.78 19.78 45 20.75 45H60.02C60.99 45 61.77 45.78 61.77 46.75C61.77 57.66 52.18 66.53 40.38 66.53H40.39ZM22.61 48.5C23.57 56.66 31.18 63.03 40.39 63.03C49.6 63.03 57.21 56.66 58.17 48.5H22.6H22.61Z" fill="currentColor"/>
        <g className="eyes">
          <path d="M20.9197 39.0966C21.2697 39.7866 21.9697 40.1866 22.6997 40.1866V40.1966C23.0097 40.1966 23.3197 40.1266 23.6097 39.9766L31.4097 35.9766C32.0697 35.6366 32.4897 34.9766 32.4997 34.2366C32.5097 33.5066 32.1197 32.8166 31.4797 32.4566L23.6797 28.0566C22.7197 27.5166 21.4997 27.8566 20.9597 28.8166C20.4197 29.7766 20.7597 30.9966 21.7197 31.5366L26.2797 34.1066L21.7897 36.4066C20.7997 36.9066 20.4197 38.1166 20.9197 39.0966Z" fill="currentColor"/>
          <path d="M57.4197 36.4066C58.4097 36.9066 58.7897 38.1166 58.2897 39.0966V39.1066C57.9397 39.7966 57.2397 40.1966 56.5097 40.1966C56.1997 40.1966 55.8897 40.1266 55.5997 39.9766L47.7997 35.9766C47.1397 35.6366 46.7197 34.9766 46.7097 34.2366C46.6997 33.5066 47.0897 32.8166 47.7297 32.4566L55.5297 28.0566C56.4897 27.5166 57.7097 27.8566 58.2497 28.8166C58.7897 29.7766 58.4497 30.9966 57.4897 31.5366L52.9297 34.1066L57.4197 36.4066Z" fill="currentColor"/>
        </g>
      </g>
      <path d="M78.8632 17.1409C76.8232 6.77091 67.6832 0.800907 67.2932 0.550907C66.2932 -0.0890927 65.0332 -0.179093 63.9532 0.320907C62.8732 0.810907 62.1132 1.83091 61.9432 3.01091C61.9432 3.08091 61.2832 7.36091 59.0732 11.6309C53.4332 8.10091 46.7732 6.06091 39.6432 6.06091C32.5132 6.06091 25.7932 8.12091 20.1432 11.6809C17.9132 7.40091 17.2532 3.08091 17.2432 3.02091C17.0732 1.84091 16.3232 0.820907 15.2432 0.320907C14.1632 -0.179093 12.8932 -0.0890927 11.8932 0.550907C11.5032 0.800907 2.36318 6.77091 0.323184 17.1409C-0.916816 23.4309 1.62318 28.8209 4.34318 32.5509C3.39318 35.8209 2.87318 39.2609 2.87318 42.8309C2.87318 63.1109 19.3732 79.6109 39.6532 79.6109C59.9332 79.6109 76.4332 63.1109 76.4332 42.8309C76.4332 39.2309 75.9032 35.7509 74.9332 32.4509C77.6332 28.7309 80.1132 23.3809 78.8832 17.1409H78.8632ZM39.6332 72.6109C23.2132 72.6109 9.85318 59.2509 9.85318 42.8309C9.85318 41.3909 9.96318 39.9809 10.1632 38.5909C10.3532 37.2609 10.6332 35.9609 10.9932 34.6909C11.3432 33.4509 11.7732 32.2509 12.2732 31.0809C10.8832 29.6909 8.89318 27.3309 7.79318 24.4509C7.10318 22.6309 6.75318 20.6109 7.17318 18.4809C7.88318 14.8809 9.89318 11.9409 11.7832 9.87091C12.4132 11.7409 13.3232 13.8709 14.5332 15.9809C15.6532 17.9409 17.0632 19.8609 18.8132 21.5709C19.6432 20.7509 20.5232 19.9909 21.4532 19.2809C22.4032 18.5509 23.3932 17.8609 24.4332 17.2509C28.8832 14.5909 34.0832 13.0609 39.6432 13.0609C45.2032 13.0609 50.3532 14.5809 54.7932 17.2209C55.8332 17.8409 56.8232 18.5109 57.7732 19.2509C58.6932 19.9609 59.5732 20.7309 60.4132 21.5409C62.1632 19.8309 63.5632 17.9009 64.6732 15.9409C65.8632 13.8509 66.7532 11.7509 67.3832 9.90091C69.2732 11.9809 71.2932 14.9209 71.9932 18.5109C72.4032 20.6009 72.0832 22.5809 71.4132 24.3809C70.3432 27.2709 68.3632 29.6309 66.9632 31.0509C67.4732 32.2209 67.8932 33.4209 68.2532 34.6609C68.6132 35.9309 68.9032 37.2209 69.0932 38.5509C69.3032 39.9609 69.4132 41.4009 69.4132 42.8609C69.4132 59.2809 56.0532 72.6409 39.6332 72.6409V72.6109Z" fill="currentColor"/>
    </svg>
  );

  const headacheIcon = () => (
    <svg className={cn(isAnimating && 'animate-head-shake', className)} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 201 202">
      <path d="M0.872849 43.4923C6.04965 17.1801 29.2438 2.03217 30.2335 1.39784C32.7711 -0.226059 35.9686 -0.454419 38.7092 0.814252C41.4499 2.05755 43.3785 4.64563 43.8099 7.6397C43.8099 7.81731 45.4847 18.6771 51.093 29.5116C65.4053 20.5548 82.306 15.3786 100.399 15.3786C118.493 15.3786 135.546 20.6055 149.884 29.6384C155.543 18.7786 157.217 7.81731 157.243 7.66507C157.674 4.67101 159.577 2.08292 162.318 0.814252C165.059 -0.454419 168.282 -0.226059 170.819 1.39784C171.809 2.03217 195.003 17.1801 200.18 43.4923C203.327 59.4522 196.881 73.1285 189.979 82.5928C192.389 90.8899 193.709 99.6183 193.709 108.677C193.709 160.134 151.838 202 100.374 202C48.9106 202 7.03934 160.134 7.03934 108.677C7.03934 99.5422 8.38431 90.7122 10.8458 82.339C3.99417 72.9001 -2.29922 59.3253 0.822083 43.4923H0.872849ZM100.425 184.239C142.093 184.239 175.996 150.34 175.996 108.677C175.996 105.023 175.717 101.445 175.209 97.9183C174.727 94.5436 174.017 91.2451 173.103 88.0227C172.215 84.8764 171.124 81.8315 169.855 78.8629C173.382 75.336 178.432 69.3478 181.224 62.0403C182.975 57.4223 183.863 52.2969 182.797 46.8924C180.995 37.7579 175.895 30.2981 171.098 25.0459C169.5 29.7907 167.19 35.1952 164.12 40.549C161.278 45.5222 157.7 50.3939 153.259 54.7327C151.152 52.6521 148.919 50.7237 146.559 48.9222C144.149 47.07 141.636 45.3192 138.997 43.7714C127.705 37.0221 114.509 33.14 100.399 33.14C86.2901 33.14 73.2212 36.9967 61.9541 43.6953C59.3149 45.2685 56.8027 46.9685 54.3919 48.8461C52.0573 50.6476 49.8241 52.6014 47.6925 54.6566C43.2516 50.3178 39.6989 45.4207 36.8821 40.4475C33.8623 35.1445 31.6038 29.8161 30.0051 25.122C25.2089 30.3996 20.0829 37.8594 18.3065 46.9685C17.2661 52.2715 18.0781 57.2955 19.7783 61.8627C22.4936 69.1956 27.5182 75.1837 31.0709 78.7867C29.7767 81.7554 28.7109 84.8002 27.7973 87.9465C26.8838 91.169 26.1478 94.4421 25.6657 97.8168C25.1328 101.394 24.8536 105.048 24.8536 108.753C24.8536 150.416 58.7566 184.315 100.425 184.315V184.239Z" fill="currentColor"/>
      <g style={{ transformBox: 'fill-box', transformOrigin: 'center' }} className={cn('face origin-center', isAnimating && 'animate-face-circle')}>
        <path className={cn('origin-center', isAnimating && 'animate-mouth-flat')} d="M71 125L131 125" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
        <g
          className={cn(isAnimating && 'animate-spin')}
          style={{ transformBox: 'fill-box', transformOrigin: 'center' }}
        >
          <path d="M123.971 85.5689L116.562 78.8449H116.511C114.455 76.9419 114.303 73.7449 116.181 71.6642C118.084 69.609 121.282 69.4568 123.362 71.3344L131.483 78.7181L139.603 71.3344C141.684 69.4314 144.907 69.5836 146.785 71.6642C148.688 73.7449 148.536 76.9673 146.455 78.8449L139.045 85.5689L146.455 92.2928C148.511 94.1958 148.663 97.3929 146.785 99.4735C145.795 100.565 144.4 101.123 143.029 101.123C141.811 101.123 140.593 100.691 139.629 99.8034L131.508 92.4197L123.388 99.8034C122.424 100.691 121.205 101.123 119.987 101.123C118.617 101.123 117.221 100.565 116.232 99.4735C114.328 97.3929 114.481 94.1705 116.562 92.2928L123.971 85.5689Z" fill="currentColor"/>
        </g>
        <g
          className={cn(isAnimating && 'animate-spin')}
          style={{
            transformOrigin: 'center',
            transformBox: 'fill-box',
          }}
        >
          <path d="M54.6683 78.8449L62.0782 85.5689H62.1289L54.719 92.2928C52.6381 94.1958 52.4859 97.3929 54.3637 99.4735C55.3534 100.565 56.7491 101.123 58.1194 101.123C59.3375 101.123 60.5556 100.691 61.5199 99.8034L69.6404 92.4197L77.7609 99.8034C78.7252 100.691 79.9432 101.123 81.1613 101.123C82.5316 101.123 83.902 100.565 84.917 99.4735C86.7949 97.3929 86.6426 94.1705 84.5618 92.2928L77.1518 85.5689L84.5618 78.8449C86.6426 76.9419 86.7949 73.7449 84.917 71.6642C83.0138 69.609 79.8164 69.4568 77.7355 71.3344L69.615 78.7181L61.4945 71.3344C59.4137 69.4314 56.1908 69.5836 54.313 71.6642C52.4351 73.7449 52.5874 76.9673 54.6683 78.8449Z" fill="currentColor"/>
        </g>
      </g>
    </svg>
  );

  const angryIcon = () => (
    <svg className={cn(className)} viewBox="0 0 201 202" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M200.127 43.4923C194.95 17.1801 171.756 2.03217 170.767 1.39784C168.229 -0.226059 165.031 -0.454419 162.291 0.814251C159.55 2.05755 157.622 4.64564 157.19 7.63971C157.19 7.81732 155.515 18.6771 149.907 29.5116C135.595 20.5548 118.694 15.3786 100.601 15.3786C82.5071 15.3786 65.4541 20.6055 51.1164 29.6384C45.4574 18.7786 43.7826 7.81732 43.7572 7.66508C43.3258 4.67102 41.4226 2.08292 38.6819 0.814251C35.9412 -0.454419 32.7184 -0.226059 30.1808 1.39784C29.1911 2.03217 5.99692 17.1801 0.820127 43.4923C-2.32656 59.4522 4.11906 73.1284 11.0215 82.5927C8.6107 90.8898 7.29112 99.6182 7.29112 108.677C7.29112 160.134 49.1624 202 100.626 202C152.089 202 193.961 160.134 193.961 108.677C193.961 99.5421 192.616 90.7122 190.154 82.3389C197.006 72.9 203.299 59.3253 200.178 43.4923H200.127ZM100.575 184.239C58.907 184.239 25.0039 150.34 25.0039 108.677C25.0039 105.023 25.2831 101.445 25.7906 97.9182C26.2728 94.5435 26.9833 91.245 27.8969 88.0226C28.7851 84.8763 29.8763 81.8315 31.1451 78.8628C27.6177 75.3359 22.5678 69.3478 19.7764 62.0402C18.0254 57.4223 17.1372 52.2969 18.203 46.8923C20.0047 37.7579 25.1054 30.2981 29.9016 25.0459C31.5004 29.7907 33.8096 35.1952 36.8802 40.549C39.7223 45.5222 43.3004 50.3938 47.7413 54.7327C49.8476 52.6521 52.0807 50.7237 54.4407 48.9222C56.8515 47.0699 59.3638 45.3192 62.0029 43.7714C73.2955 37.0221 86.4912 33.14 100.601 33.14C114.71 33.14 127.779 36.9967 139.046 43.6953C141.685 45.2684 144.197 46.9684 146.608 48.8461C148.943 50.6476 151.176 52.6013 153.308 54.6566C157.748 50.3177 161.301 45.4207 164.118 40.4475C167.138 35.1444 169.396 29.816 170.995 25.122C175.791 30.3996 180.917 37.8594 182.694 46.9684C183.734 52.2715 182.922 57.2954 181.222 61.8626C178.506 69.1955 173.482 75.1836 169.929 78.7867C171.223 81.7553 172.289 84.8001 173.203 87.9464C174.116 91.1689 174.852 94.442 175.334 97.8167C175.867 101.394 176.146 105.048 176.146 108.753C176.146 150.416 142.243 184.315 100.575 184.315V184.239Z" fill="white"/>

    <path d="M123 87L142.722 75.1666" stroke="white" stroke-width="10" stroke-linecap="round"/>
    {/* Lower right lid */}
    <path d="M123 87L142.919 98.5" stroke="white" stroke-width="10" stroke-linecap="round"/>
    <path d="M77.0139 85.8334L57.2915 74" stroke="white" stroke-width="10" stroke-linecap="round"/>
    {/* Lower left lid */}
    <path d="M76.9186 86L57 97.5" stroke="white" stroke-width="10" stroke-linecap="round"/>
    {/* Left eye */}
    <circle className="opacity-0" cx="64" cy="87" r="7" fill="white"/>
    {/* Right eye */}
    <circle className="opacity-0" cx="136" cy="87" r="7" fill="white"/>
    {/* Mouth */}
    <g className="mouth">
      <path className={cn(isAnimating && 'animate-mouth-stagger')} d="M94.1062 124.569L86.6962 117.845H86.6455C84.59 115.942 84.4377 112.745 86.3156 110.664C88.2188 108.609 91.4163 108.457 93.4971 110.334L101.618 117.718L109.738 110.334C111.819 108.431 115.042 108.584 116.92 110.664C118.823 112.745 118.671 115.967 116.59 117.845L109.18 124.569L116.59 131.293C118.645 133.196 118.798 136.393 116.92 138.474C115.93 139.565 114.534 140.123 113.164 140.123C111.946 140.123 110.728 139.691 109.763 138.803L101.643 131.42L93.5225 138.803C92.5582 139.691 91.3401 140.123 90.1221 140.123C88.7517 140.123 87.356 139.565 86.3663 138.474C84.4631 136.393 84.6154 133.17 86.6962 131.293L94.1062 124.569Z" fill="white"/>
      <g className={cn(isAnimating && 'animate-mouth-stagger')} style={{ animationDelay: '0.3s' }}>
        <rect x="67" y="114" width="69" height="20" rx="4" stroke="white" stroke-width="10" stroke-linejoin="round"/>
        <line x1="102" y1="114" x2="102" y2="134" stroke="white" stroke-width="10"/>
        <line x1="84" y1="114" x2="84" y2="134" stroke="white" stroke-width="10"/>
        <line x1="119" y1="114" x2="119" y2="134" stroke="white" stroke-width="10"/>
      </g>
      <path className={cn(isAnimating && 'animate-mouth-stagger')} style={{ animationDelay: '0.6s' }} d="M83.942 115C83.9335 115 83.9251 115 83.9166 115L83.9674 115C83.9589 115 83.9505 115 83.942 115C85.0755 115.006 86.2069 115.438 87.0633 116.294L92.4431 121.673L97.8229 116.294C99.5485 114.569 102.365 114.569 104.091 116.294L109.471 121.673L114.851 116.294C116.576 114.569 119.393 114.569 121.119 116.294L129.645 124.82C131.371 126.545 131.371 129.361 129.645 131.087C127.919 132.812 125.103 132.812 123.377 131.087L117.997 125.708L112.617 131.087C110.892 132.812 108.075 132.812 106.349 131.087L100.97 125.708L95.5898 131.087C93.8642 132.812 91.0474 132.812 89.3218 131.087L83.942 125.708L78.5622 131.087C76.8366 132.812 74.0198 132.812 72.2942 131.087C70.5686 129.361 70.5686 126.545 72.2942 124.82L80.8207 116.294C81.6771 115.438 82.8085 115.006 83.942 115Z" fill="white"/>
      <path className={cn(isAnimating && 'animate-mouth-stagger')} style={{ animationDelay: '0.9s' }} d="M117.997 132.381C118.006 132.381 118.014 132.381 118.023 132.381L117.972 132.381C117.98 132.381 117.989 132.381 117.997 132.381C116.864 132.374 115.732 131.943 114.876 131.087L109.496 125.708L104.116 131.087C102.391 132.812 99.5739 132.812 97.8483 131.087L92.4685 125.708L87.0887 131.087C85.3631 132.812 82.5463 132.812 80.8207 131.087L72.2942 122.561C70.5686 120.836 70.5686 118.019 72.2942 116.294C74.0198 114.569 76.8366 114.569 78.5622 116.294L83.942 121.673L89.3218 116.294C91.0474 114.569 93.8642 114.569 95.5898 116.294L100.97 121.673L106.349 116.294C108.075 114.569 110.892 114.569 112.617 116.294L117.997 121.673L123.377 116.294C125.103 114.569 127.919 114.569 129.645 116.294C131.371 118.019 131.371 120.836 129.645 122.561L121.119 131.087C120.262 131.943 119.131 132.374 117.997 132.381Z" fill="white"/>
      <path className={cn(isAnimating && 'animate-mouth-stagger')} style={{ animationDelay: '1.2s' }} d="M85 140L123 123" stroke="white" stroke-width="10" stroke-linecap="round"/>
      <path className={cn(isAnimating && 'animate-mouth-stagger')} style={{ animationDelay: '1.5s' }} d="M71 124L131 124" stroke="white" stroke-width="10" stroke-linecap="round"/>
      <path  d="M73 126.667C73 126.667 83.5 116.167 100.5 126.667C117.5 137.167 128.5 126.667 128.5 126.667" stroke="white" stroke-width="10" stroke-linecap="round"/>
    </g>
    </svg>
  )


  const iconMap = {
    laugh: laughingIcon,
    headache: headacheIcon,
  };

  const renderIcon = iconMap[animation];
  return renderIcon();
};

