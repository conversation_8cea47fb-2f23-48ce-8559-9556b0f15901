import { cn } from '@/lib/utils';

type AnimationType = 'laughing' | 'confused';

interface AnimatedFaceIconProps {
  className?: string;
  animation?: AnimationType;
  isAnimating?: boolean;
}

export const AnimatedFaceIcon = ({ className, animation = 'confused', isAnimating = false }: AnimatedFaceIconProps) => {
  const renderLaughingIcon = () => (
    <svg className={className} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g className={cn('face', isAnimating && 'animate-laugh delay-600')}>
        <path className={cn('mouth duration-200 ease-out delay-600', isAnimating ? 'opacity-0' : 'opacity-1')} d="M46.53 53.85C46.08 53.85 45.63 53.68 45.29 53.34L43.17 51.22L41.05 53.34C40.37 54.02 39.26 54.02 38.58 53.34L36.46 51.22L34.34 53.34C33.66 54.02 32.55 54.02 31.87 53.34L28.51 49.98C27.83 49.3 27.83 48.19 28.51 47.51C29.19 46.83 30.3 46.83 30.98 47.51L33.1 49.63L35.22 47.51C35.9 46.83 37.01 46.83 37.69 47.51L39.81 49.63L41.93 47.51C42.61 46.83 43.72 46.83 44.4 47.51L46.52 49.63L48.64 47.51C49.32 46.83 50.43 46.83 51.11 47.51C51.79 48.19 51.79 49.3 51.11 49.98L47.75 53.34C47.41 53.68 46.96 53.85 46.51 53.85H46.53Z" fill="currentColor"/>
        <path className={cn('smile origin-center', isAnimating ? 'opacity-1 animate-mouth-laugh delay-600' : 'opacity-0')} d="M40.39 66.53C28.6 66.53 19 57.66 19 46.75C19 45.78 19.78 45 20.75 45H60.02C60.99 45 61.77 45.78 61.77 46.75C61.77 57.66 52.18 66.53 40.38 66.53H40.39ZM22.61 48.5C23.57 56.66 31.18 63.03 40.39 63.03C49.6 63.03 57.21 56.66 58.17 48.5H22.6H22.61Z" fill="currentColor"/>
        <g className="eyes">
          <path d="M20.9197 39.0966C21.2697 39.7866 21.9697 40.1866 22.6997 40.1866V40.1966C23.0097 40.1966 23.3197 40.1266 23.6097 39.9766L31.4097 35.9766C32.0697 35.6366 32.4897 34.9766 32.4997 34.2366C32.5097 33.5066 32.1197 32.8166 31.4797 32.4566L23.6797 28.0566C22.7197 27.5166 21.4997 27.8566 20.9597 28.8166C20.4197 29.7766 20.7597 30.9966 21.7197 31.5366L26.2797 34.1066L21.7897 36.4066C20.7997 36.9066 20.4197 38.1166 20.9197 39.0966Z" fill="currentColor"/>
          <path d="M57.4197 36.4066C58.4097 36.9066 58.7897 38.1166 58.2897 39.0966V39.1066C57.9397 39.7966 57.2397 40.1966 56.5097 40.1966C56.1997 40.1966 55.8897 40.1266 55.5997 39.9766L47.7997 35.9766C47.1397 35.6366 46.7197 34.9766 46.7097 34.2366C46.6997 33.5066 47.0897 32.8166 47.7297 32.4566L55.5297 28.0566C56.4897 27.5166 57.7097 27.8566 58.2497 28.8166C58.7897 29.7766 58.4497 30.9966 57.4897 31.5366L52.9297 34.1066L57.4197 36.4066Z" fill="currentColor"/>
        </g>
      </g>
      <path d="M78.8632 17.1409C76.8232 6.77091 67.6832 0.800907 67.2932 0.550907C66.2932 -0.0890927 65.0332 -0.179093 63.9532 0.320907C62.8732 0.810907 62.1132 1.83091 61.9432 3.01091C61.9432 3.08091 61.2832 7.36091 59.0732 11.6309C53.4332 8.10091 46.7732 6.06091 39.6432 6.06091C32.5132 6.06091 25.7932 8.12091 20.1432 11.6809C17.9132 7.40091 17.2532 3.08091 17.2432 3.02091C17.0732 1.84091 16.3232 0.820907 15.2432 0.320907C14.1632 -0.179093 12.8932 -0.0890927 11.8932 0.550907C11.5032 0.800907 2.36318 6.77091 0.323184 17.1409C-0.916816 23.4309 1.62318 28.8209 4.34318 32.5509C3.39318 35.8209 2.87318 39.2609 2.87318 42.8309C2.87318 63.1109 19.3732 79.6109 39.6532 79.6109C59.9332 79.6109 76.4332 63.1109 76.4332 42.8309C76.4332 39.2309 75.9032 35.7509 74.9332 32.4509C77.6332 28.7309 80.1132 23.3809 78.8832 17.1409H78.8632ZM39.6332 72.6109C23.2132 72.6109 9.85318 59.2509 9.85318 42.8309C9.85318 41.3909 9.96318 39.9809 10.1632 38.5909C10.3532 37.2609 10.6332 35.9609 10.9932 34.6909C11.3432 33.4509 11.7732 32.2509 12.2732 31.0809C10.8832 29.6909 8.89318 27.3309 7.79318 24.4509C7.10318 22.6309 6.75318 20.6109 7.17318 18.4809C7.88318 14.8809 9.89318 11.9409 11.7832 9.87091C12.4132 11.7409 13.3232 13.8709 14.5332 15.9809C15.6532 17.9409 17.0632 19.8609 18.8132 21.5709C19.6432 20.7509 20.5232 19.9909 21.4532 19.2809C22.4032 18.5509 23.3932 17.8609 24.4332 17.2509C28.8832 14.5909 34.0832 13.0609 39.6432 13.0609C45.2032 13.0609 50.3532 14.5809 54.7932 17.2209C55.8332 17.8409 56.8232 18.5109 57.7732 19.2509C58.6932 19.9609 59.5732 20.7309 60.4132 21.5409C62.1632 19.8309 63.5632 17.9009 64.6732 15.9409C65.8632 13.8509 66.7532 11.7509 67.3832 9.90091C69.2732 11.9809 71.2932 14.9209 71.9932 18.5109C72.4032 20.6009 72.0832 22.5809 71.4132 24.3809C70.3432 27.2709 68.3632 29.6309 66.9632 31.0509C67.4732 32.2209 67.8932 33.4209 68.2532 34.6609C68.6132 35.9309 68.9032 37.2209 69.0932 38.5509C69.3032 39.9609 69.4132 41.4009 69.4132 42.8609C69.4132 59.2809 56.0532 72.6409 39.6332 72.6409V72.6109Z" fill="currentColor"/>
    </svg>
  );

  const renderConfusedIcon = () => (
    <svg className={cn(isAnimating && 'animate-head-shake', className)} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 201 202">
      <path d="M0.872849 43.4923C6.04965 17.1801 29.2438 2.03217 30.2335 1.39784C32.7711 -0.226059 35.9686 -0.454419 38.7092 0.814252C41.4499 2.05755 43.3785 4.64563 43.8099 7.6397C43.8099 7.81731 45.4847 18.6771 51.093 29.5116C65.4053 20.5548 82.306 15.3786 100.399 15.3786C118.493 15.3786 135.546 20.6055 149.884 29.6384C155.543 18.7786 157.217 7.81731 157.243 7.66507C157.674 4.67101 159.577 2.08292 162.318 0.814252C165.059 -0.454419 168.282 -0.226059 170.819 1.39784C171.809 2.03217 195.003 17.1801 200.18 43.4923C203.327 59.4522 196.881 73.1285 189.979 82.5928C192.389 90.8899 193.709 99.6183 193.709 108.677C193.709 160.134 151.838 202 100.374 202C48.9106 202 7.03934 160.134 7.03934 108.677C7.03934 99.5422 8.38431 90.7122 10.8458 82.339C3.99417 72.9001 -2.29922 59.3253 0.822083 43.4923H0.872849ZM100.425 184.239C142.093 184.239 175.996 150.34 175.996 108.677C175.996 105.023 175.717 101.445 175.209 97.9183C174.727 94.5436 174.017 91.2451 173.103 88.0227C172.215 84.8764 171.124 81.8315 169.855 78.8629C173.382 75.336 178.432 69.3478 181.224 62.0403C182.975 57.4223 183.863 52.2969 182.797 46.8924C180.995 37.7579 175.895 30.2981 171.098 25.0459C169.5 29.7907 167.19 35.1952 164.12 40.549C161.278 45.5222 157.7 50.3939 153.259 54.7327C151.152 52.6521 148.919 50.7237 146.559 48.9222C144.149 47.07 141.636 45.3192 138.997 43.7714C127.705 37.0221 114.509 33.14 100.399 33.14C86.2901 33.14 73.2212 36.9967 61.9541 43.6953C59.3149 45.2685 56.8027 46.9685 54.3919 48.8461C52.0573 50.6476 49.8241 52.6014 47.6925 54.6566C43.2516 50.3178 39.6989 45.4207 36.8821 40.4475C33.8623 35.1445 31.6038 29.8161 30.0051 25.122C25.2089 30.3996 20.0829 37.8594 18.3065 46.9685C17.2661 52.2715 18.0781 57.2955 19.7783 61.8627C22.4936 69.1956 27.5182 75.1837 31.0709 78.7867C29.7767 81.7554 28.7109 84.8002 27.7973 87.9465C26.8838 91.169 26.1478 94.4421 25.6657 97.8168C25.1328 101.394 24.8536 105.048 24.8536 108.753C24.8536 150.416 58.7566 184.315 100.425 184.315V184.239Z" fill="currentColor"/>
      <g style={{ transformBox: 'fill-box', transformOrigin: 'center' }} className={cn('face origin-center', isAnimating && 'animate-face-circle')}>
        <path className={cn('origin-center', isAnimating && 'animate-mouth-flat')} d="M71 125L131 125" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
        <g
          className={cn(isAnimating && 'animate-spin')}
          style={{ transformBox: 'fill-box', transformOrigin: 'center' }}
        >
          <path d="M123.971 85.5689L116.562 78.8449H116.511C114.455 76.9419 114.303 73.7449 116.181 71.6642C118.084 69.609 121.282 69.4568 123.362 71.3344L131.483 78.7181L139.603 71.3344C141.684 69.4314 144.907 69.5836 146.785 71.6642C148.688 73.7449 148.536 76.9673 146.455 78.8449L139.045 85.5689L146.455 92.2928C148.511 94.1958 148.663 97.3929 146.785 99.4735C145.795 100.565 144.4 101.123 143.029 101.123C141.811 101.123 140.593 100.691 139.629 99.8034L131.508 92.4197L123.388 99.8034C122.424 100.691 121.205 101.123 119.987 101.123C118.617 101.123 117.221 100.565 116.232 99.4735C114.328 97.3929 114.481 94.1705 116.562 92.2928L123.971 85.5689Z" fill="currentColor"/>
        </g>
        <g
          className={cn(isAnimating && 'animate-spin')}
          style={{
            transformOrigin: 'center',
            transformBox: 'fill-box',
          }}
        >
          <path d="M54.6683 78.8449L62.0782 85.5689H62.1289L54.719 92.2928C52.6381 94.1958 52.4859 97.3929 54.3637 99.4735C55.3534 100.565 56.7491 101.123 58.1194 101.123C59.3375 101.123 60.5556 100.691 61.5199 99.8034L69.6404 92.4197L77.7609 99.8034C78.7252 100.691 79.9432 101.123 81.1613 101.123C82.5316 101.123 83.902 100.565 84.917 99.4735C86.7949 97.3929 86.6426 94.1705 84.5618 92.2928L77.1518 85.5689L84.5618 78.8449C86.6426 76.9419 86.7949 73.7449 84.917 71.6642C83.0138 69.609 79.8164 69.4568 77.7355 71.3344L69.615 78.7181L61.4945 71.3344C59.4137 69.4314 56.1908 69.5836 54.313 71.6642C52.4351 73.7449 52.5874 76.9673 54.6683 78.8449Z" fill="currentColor"/>
        </g>
      </g>
    </svg>
  );

  const animationRenderers = {
    laughing: renderLaughingIcon,
    confused: renderConfusedIcon,
  } as const;

  const renderIcon = animationRenderers[animation];
  return renderIcon ? renderIcon() : renderConfusedIcon();
};

// Keep the old export for backward compatibility
export const ConfusedIcon = ({ className, isAnimating = false }: { className?: string; isAnimating?: boolean }) => {
  return <AnimatedFaceIcon className={className} animation="confused" isAnimating={isAnimating} />;
}; 