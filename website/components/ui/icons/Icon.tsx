import React from 'react';

import { DpIconFace } from './sprites/DpIconFace';
import { XIcon } from './sprites/XIcon';
import { EthIcon } from './sprites/EthIcon';
import { LaserIcon } from './sprites/LaserIcon';
import { Logo } from './sprites/Logo';

const icons = {
  laser: LaserIcon,
  logo: Logo,
  eth: EthIcon,
  'dp-icon-face': DpIconFace,
  x: XIcon,
} as const;

type IconsMap = typeof icons;
export type IconSpriteType = keyof IconsMap;

type IconComponent = React.FC<React.SVGProps<SVGSVGElement>>;

export type IconProps = {
  id: IconSpriteType;
  className?: string;
} & Omit<React.SVGProps<SVGSVGElement>, 'id'>;

export const Icon = ({
  id,
  className,
  ...props
}: IconProps) => {
  const Component = icons[id] as IconComponent;

  if (!Component) {
    return null
  }
  
  return <Component className={className} {...props} />;
};
