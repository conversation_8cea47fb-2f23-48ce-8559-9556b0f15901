import { cn } from '@/lib/utils';
import { IconProps } from '../Icon';

export function DpIconFace({ className, ...props }:IconProps ) {
  return (
    <svg
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={cn('', className)}
      {...props}
    >
      <circle cx='11.9999' cy='11.9999' r='12' fill='currentColor' />
      <path
        d='M11.9984 20C15.8576 20 19 16.7041 19 12.6503C19 12.2898 18.7447 12 18.4272 12H5.57283C5.25532 12 5 12.2898 5 12.6503C5 16.7041 8.13912 20 12.0016 20H11.9984ZM17.8183 13.3005C17.5041 16.3326 15.0131 18.6995 11.9984 18.6995C8.98363 18.6995 6.49264 16.3326 6.1784 13.3005H17.8216H17.8183Z'
        fill='black'
      />
      <path
        d='M15.4458 8.00096L14.3552 7.13678H14.3477C14.0452 6.8922 14.0228 6.4813 14.2992 6.21389C14.5793 5.94975 15.0499 5.93018 15.3562 6.1715L16.5514 7.12047L17.7466 6.1715C18.0528 5.92692 18.5272 5.94649 18.8036 6.21389C19.0837 6.4813 19.0613 6.89546 18.755 7.13678L17.6644 8.00096L18.755 8.86515C19.0576 9.10973 19.08 9.52062 18.8036 9.78803C18.6579 9.92826 18.4525 10 18.2508 10C18.0715 10 17.8922 9.94456 17.7503 9.83042L16.5551 8.88145L15.3599 9.83042C15.218 9.94456 15.0387 10 14.8594 10C14.6577 10 14.4523 9.92826 14.3066 9.78803C14.0265 9.52062 14.0489 9.10647 14.3552 8.86515L15.4458 8.00096Z'
        fill='black'
      />
      <path
        d='M5.24554 7.13678L6.33616 8.00096H6.34363L5.25301 8.86515C4.94674 9.10973 4.92433 9.52062 5.20072 9.78803C5.34639 9.92826 5.55181 10 5.7535 10C5.93278 10 6.11206 9.94456 6.25399 9.83042L7.44919 8.88145L8.64439 9.83042C8.78632 9.94456 8.9656 10 9.14488 10C9.34657 10 9.54826 9.92826 9.69766 9.78803C9.97405 9.52062 9.95163 9.10647 9.64537 8.86515L8.55475 8.00096L9.64537 7.13678C9.95163 6.8922 9.97405 6.4813 9.69766 6.21389C9.41753 5.94975 8.94692 5.93018 8.64065 6.1715L7.44545 7.12047L6.25025 6.1715C5.94398 5.92692 5.46964 5.94649 5.19325 6.21389C4.91686 6.4813 4.93927 6.89546 5.24554 7.13678Z'
        fill='black'
      />
    </svg>
  );
}
