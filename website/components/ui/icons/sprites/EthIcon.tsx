import { cn } from '@/lib/utils';
import { IconProps } from '../Icon';

export function EthIcon({ className, ...props }: IconProps) {
  return (
    <svg
      viewBox='0 0 9 15'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={cn('', className)}
      {...props}
    >
      <path d='M4.50004 11.4418V15L9 8.72419L4.50004 11.4418Z' fill='currentColor' />
      <path d='M4.50004 5.55472V10.0567L9 7.32982L4.50004 5.55472Z' fill='currentColor' />
      <path d='M4.50004 0V5.55472L9 7.32982L4.50004 0Z' fill='currentColor' />
      <path d='M4.50004 11.4418V15L0 8.72214L4.50004 11.4418Z' fill='currentColor' />
      <path d='M4.50004 5.55472V10.0567L0 7.32982L4.50004 5.55472Z' fill='currentColor' />
      <path d='M4.50004 0V5.55472L0 7.32982L4.50004 0Z' fill='currentColor' />
    </svg>
  );
}
