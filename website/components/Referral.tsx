'use client';

import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog/Dialog';
import { Button } from '@/components/ui/button/Button';
import { useState, useEffect, useCallback } from 'react';
import { REFERRAL_PERCENTAGE, MINIMUM_CLAIM_AMOUNT, DEATH_RACE_HOUSE_EDGE } from '@/lib/constants';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import {
  getUserReferralBonusesOptions,
  useGetUser,
  useGetUserReferralBonuses,
} from '@/lib/client/queries';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { analytics } from '@/lib/client/analytics';
import { formatEther, parseEther } from 'viem';
import { getBalanceQueryOptions } from 'wagmi/query';
import { useConfig } from 'wagmi';
import { TextInput } from './ui/input/TextInput';

interface ReferralProps {
  className?: string;
}

export function Referral({ className }: ReferralProps) {
  // ------------------------------------------------ STATE
  const [referralLink, setReferralLink] = useState('');

  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { walletAddress } = userData ?? {};
  const queryClient = useQueryClient();
  const { data: referralBonuses, isPending: isLoadingReferralBonuses } =
    useGetUserReferralBonuses();
  const totalBonusesAmount = parseEther(referralBonuses?.total || '0');
  const wagmiConfig = useConfig();

  // ------------------------------------------------ COMPUTED VALUES
  // Use custom referral percentage if set, otherwise use global percentage
  const effectiveReferralPercentage = userData?.customReferralPercentage ?? REFERRAL_PERCENTAGE;

  // ------------------------------------------------ MUTATIONS
  const { mutate: claimReferralBonuses, isPending: isClaimingBonuses } = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/users/referral-bonuses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to claim bonuses');
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success('Bonuses claimed successfully!');
      queryClient.invalidateQueries({ queryKey: getUserReferralBonusesOptions().queryKey });
      queryClient.invalidateQueries({
        queryKey: getBalanceQueryOptions(wagmiConfig, {
          address: walletAddress as `0x${string}`,
          chainId: config.chain.id,
        }).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: ['user'] });
    },
    onError: (error) => {
      toast.error('Failed to claim bonuses');
      console.error('Error claiming bonuses:', error);
    },
  });

  // ------------------------------------------------ HANDLERS

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      toast.success('Referral link copied to clipboard!');
      analytics.copiedReferralLink({ referralLink });
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy referral link');
    }
  };

  const handleClaim = useCallback(async () => {
    if (!walletAddress || isClaimingBonuses || !referralBonuses?.total) {
      return;
    }

    claimReferralBonuses();
  }, [walletAddress, isClaimingBonuses, referralBonuses?.total, claimReferralBonuses]);

  useEffect(() => {
    if (userData?.referralCode) {
      const baseUrl = window.location.origin;
      const link = `${baseUrl}?r=${userData.referralCode}`;
      setReferralLink(link);
    } else {
      setReferralLink('');
    }
  }, [userData]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <button
          className={cn(
            'flex items-center gap-2 text-white/60 hover:text-white/80 transition-colors',
            className
          )}
        >
          <span className='text-sm'>Referrals</span>
        </button>
      </DialogTrigger>
      <DialogContent className='bg-black/95 border-white/10 backdrop-blur-md'>
        <DialogHeader>
          <DialogTitle className='text-white text-2xl text-center'>Referral Program</DialogTitle>
        </DialogHeader>
        <div className='space-y-6 text-sm text-white/80'>
          {!walletAddress ? (
            <p>
              Share Death.fun and earn a % of profits on all referrals! <br />
              <br />
              Sign in to get your link & referral %!
            </p>
          ) : (
            <>
              <p>
                Share your referral link and earn{' '}
                {(effectiveReferralPercentage / DEATH_RACE_HOUSE_EDGE) * 100}% of profits on all
                referrals!
              </p>

              <div className='flex gap-2'>
                <TextInput
                  value={referralLink}
                  variant='readOnly'
                  className='w-full'
                  autoFocus={false}
                />
                <Button onClick={handleCopy} variant='outline'>
                  Copy
                </Button>
              </div>

              <div className='space-y-2'>
                <div className='space-y-1'>
                  <p className='text-sm'>
                    Available royalties:{' '}
                    <span className='font-mono text-gc-400'>
                      {referralBonuses?.total || 0} {config?.currency.symbol || ''}
                    </span>
                  </p>
                  <p className='text-sm'>
                    {totalBonusesAmount > BigInt(0) &&
                      totalBonusesAmount < MINIMUM_CLAIM_AMOUNT && (
                        <span className='font-mono text-red-600'>
                          Minimum claim amount: {formatEther(MINIMUM_CLAIM_AMOUNT)} ETH
                        </span>
                      )}
                  </p>
                </div>
                <Button
                  onClick={handleClaim}
                  disabled={
                    isLoadingReferralBonuses ||
                    isClaimingBonuses ||
                    totalBonusesAmount < MINIMUM_CLAIM_AMOUNT
                  }
                  variant='primary'
                  className='w-full'
                >
                  {isClaimingBonuses ? 'Claiming...' : 'Claim Royalties'}
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
