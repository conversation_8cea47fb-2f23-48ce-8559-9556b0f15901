'use client';

import { Button } from '@/components/ui/button/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Wallet as WalletIcon, Copy } from '@phosphor-icons/react';
import { useCopyToClipboard } from 'usehooks-ts';
import { toast } from 'sonner';
import { usePrivy } from '@privy-io/react-auth';
import { useGetUser } from '@/lib/client/queries';
import { useLogout } from '@/lib/hooks/useLogout';
import { useBalance } from 'wagmi';
import { formatCurrency } from '@/lib/client/wallet';
import { formatEther } from 'viem';
import { useEffect, useMemo, useRef } from 'react';
import { UserNotFoundError } from '@/lib/client/errors';
import { analytics, identifyUser } from '@/lib/client/analytics';
import { Icon } from './ui/icons/Icon';
import { useAbstractPrivyLogin } from '@abstract-foundation/agw-react/privy';
import { useBridge } from '@/lib/hooks/flags/useBridge';
import { useGlobalStore } from '@/lib/store';

interface WalletButtonProps {
  onEditUsername: () => void;
}

export function WalletButton({ onEditUsername }: WalletButtonProps) {
  // ------------------------------------------------ STATE
  const toggleModals = useGlobalStore((state) => state.toggleModals);

  // ------------------------------------------------ HOOKS
  const { authenticated } = usePrivy();
  const { login } = useAbstractPrivyLogin();
  const { data: userData, isError, error } = useGetUser();
  const { walletAddress } = userData ?? {};
  const logout = useLogout();
  const [_, copy] = useCopyToClipboard();
  const { data: balance, isLoading: isBalanceLoading } = useBalance({
    address: walletAddress as `0x${string}`,
  });
  const bridgeEnabled = useBridge();

  // ------------------------------------------------ MEMO
  const shortAddress = useMemo(() => {
    if (!walletAddress) return '';
    return `${walletAddress.slice(0, 4)}..${walletAddress.slice(-4)}`;
  }, [walletAddress]);

  // ------------------------------------------------ HANDLERS
  const handleCopyAddress = () => {
    if (walletAddress) {
      copy(walletAddress)
        .then(() => {
          toast.success('Wallet address copied!');
        })
        .catch((error) => {
          console.error('Failed to copy address:', error);
          toast.error('Failed to copy address.');
        });
    }
  };

  // Track if login was user-initiated
  const loginClickedRef = useRef(false);
  const handleLogin = async () => {
    loginClickedRef.current = true;
    analytics.signInClicked();
    await login();
  };

  const handleAddFunds = () => {
    toggleModals({ bridge: true });
  };

  // ------------------------------------------------ EFFECTS
  // Fire analytics only if login was user-initiated and authentication just completed
  useEffect(() => {
    if (loginClickedRef.current && authenticated && walletAddress && balance) {
      identifyUser(userData?.id);
      analytics.walletConnected({
        wallet_balance: Number(formatEther(balance.value)),
      });
      loginClickedRef.current = false;
    }
  }, [authenticated, walletAddress, userData?.id, balance]);

  useEffect(() => {
    if (isError && error.message) {
      if (error instanceof UserNotFoundError) {
        onEditUsername();
      }
    }
  }, [isError, error]);

  // ------------------------------------------------ RENDER

  if (!authenticated) {
    return (
      <Button onClick={handleLogin} variant='outline' size='sm'>
        <WalletIcon className='h-4 w-4' />
        <span className='hidden sm:inline'>Sign in</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='sm'>
          <WalletIcon className='h-4 w-4' />
          <span className='hidden sm:inline-flex items-center text-gc-400 font-medium text-sm'>
            <Icon id='eth' className='w-2 h-3 mr-1' />
            {isBalanceLoading ? 'Loading...' : formatCurrency(balance?.value ?? BigInt(0), false)}
          </span>
          <span className='text-white/60 hidden lg:inline text-xs'>{shortAddress}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[200px] bg-black border border-white/10'>
        <DropdownMenuLabel className='text-xs font-normal text-white/60'>
          Connected Wallet
        </DropdownMenuLabel>
        <DropdownMenuItem
          onClick={handleCopyAddress}
          className='text-gc-400 hover:text-gc-400 hover:bg-white/10 focus:bg-white/10 focus:text-gc-400 cursor-pointer flex items-center justify-between text-xs'
        >
          <span>{shortAddress}</span>
          <Copy className='w-3 h-3 text-white/60' />
        </DropdownMenuItem>
        {bridgeEnabled && (
          <>
            <DropdownMenuSeparator className='bg-white/10' />
            <DropdownMenuItem
              onClick={handleAddFunds}
              className='text-white hover:text-white hover:bg-white/10 focus:bg-white/10 focus:text-white'
            >
              Add Funds
            </DropdownMenuItem>
          </>
        )}
        <DropdownMenuSeparator className='bg-white/10' />
        <DropdownMenuItem
          onClick={onEditUsername}
          className='text-white hover:text-white hover:bg-white/10 focus:bg-white/10 focus:text-white'
        >
          Edit Username
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={logout}
          className='text-destructive hover:text-destructive hover:bg-white/10 focus:bg-white/10 focus:text-destructive'
        >
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
