'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { getGameMeta } from '@/lib/utils';
import { Icon } from '@/components/ui/icons/Icon';

interface HeaderLogoProps {}

export const HeaderLogo: React.FC<HeaderLogoProps> = ({}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const gameType = searchParams.get('gameType');
  const gameMeta = getGameMeta(pathname, gameType);

  return (
    <div className='flex justify-center'>
      <Link href={gameMeta.route} className='flex items-center gap-4'>
        <Icon id={gameMeta.logoId} className='w-6 sm:w-8 h-auto aspect-square text-gc-400' />
        <h1 className='hidden lg:block lg:text-2xl uppercase font-fancy text-gc-400 -mt-0.5'>
          Death.Fun
        </h1>
      </Link>
    </div>
  );
};
