'use client';
import { useLocalStorage } from 'usehooks-ts';
import { Speaker<PERSON><PERSON><PERSON><PERSON><PERSON>, SpeakerSimpleSlash } from '@phosphor-icons/react';
import { Button } from '../ui/button/Button';

export function MuteButton({ className }: { className?: string }) {
  const [isMuted, setIsMuted] = useLocalStorage('deathrace-muted', false);

  return (
    <Button
      aria-label={isMuted ? 'Unmute' : 'Mute'}
      onClick={() => setIsMuted((prev) => !prev)}
      className={className}
      variant='ghost'
    >
      {isMuted ? (
        <SpeakerSimpleSlash className='w-6 h-6 text-white/50 hover:text-white' />
      ) : (
        <SpeakerSimpleHigh className='w-6 h-6 text-white hover:text-gc-400' />
      )}
    </Button>
  );
}
