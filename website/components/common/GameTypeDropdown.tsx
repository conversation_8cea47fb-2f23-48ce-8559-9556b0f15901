'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { GAMES_META } from '@/lib/constants';
import { useSearchParamsContext } from '@/lib/context/ViewContext';
import { useAvailableGames } from '@/lib/hooks/flags/useAvailableGames';
import { useRouter, usePathname } from 'next/navigation';
import React from 'react';

type GameTypeValue = 'all' | 'death_race' | 'laser_party';

interface GameTypeDropdownProps {}

export const GameTypeDropdown: React.FC<GameTypeDropdownProps> = ({}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { searchParams } = useSearchParamsContext();
  const availableGames = useAvailableGames();
  if (!availableGames || availableGames.length < 2) {
    return null;
  }

  // Get current gameType from URL or default to 'all'
  const gameTypeParam = searchParams.get('gameType');
  const currentGameType: GameTypeValue =
    gameTypeParam === 'death_race' || gameTypeParam === 'laser_party' ? gameTypeParam : 'all';

  // Function to update the gameType search parameter
  const updateGameType = (newGameType: GameTypeValue) => {
    const newParams = new URLSearchParams(searchParams);

    if (newGameType === 'all') {
      // Remove gameType param when 'all' is selected
      newParams.delete('gameType');
    } else {
      newParams.set('gameType', newGameType);
    }

    const queryString = newParams.toString();
    const newUrl = queryString ? `${pathname}?${queryString}` : pathname;
    router.push(newUrl, { scroll: false });
  };

  return (
    <Select value={currentGameType} onValueChange={updateGameType}>
      <SelectTrigger className='w-auto'>
        <SelectValue placeholder='Game Type' />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value='all'>All Games</SelectItem>
        {GAMES_META.map((game) => (
          <SelectItem key={game.gameType} value={game.gameType}>
            {game.displayName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
