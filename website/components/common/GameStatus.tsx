import { Button } from '@/components/ui/button/Button';
import { formatCurrency } from '@/lib/client/wallet';
import { cn } from '@/lib/utils';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { calculatePoints, calculateRowMultipliers } from '@/lib/utils/game';
import { Co<PERSON>, Shuffle, Spinner } from '@phosphor-icons/react';
import { useEffect, useState } from 'react';
import CountUp from 'react-countup';
import { DisplayRow } from '../death-race/types';
import { VerificationTooltip } from '../death-race/VerificationTooltip';
import { Icon } from '../ui/icons/Icon';

interface GameStatusProps {
  isGameOver: boolean;
  hasWon: boolean;
  currentBet: bigint;
  finalMultiplier: number;
  onCashOut: () => void;
  commitmentHash: string | null;
  gameSeed: string | null;
  isPayoutPending?: boolean;
  isCashingOut?: boolean;
  isSelectingTile?: boolean;
  payoutAmount: bigint;
  payoutError?: string;
  onRetryPayout?: () => Promise<void>;
  currentRowIndex?: number;
  rows?: DisplayRow[];
  updatedAt?: string;
  gameBetAmount?: bigint;
  processingTileIndex?: number | [number, number] | null;
  gameOverText?: string;
  animationPending?: boolean;
  selectedTiles?: number[] | number[][];
  gameType?: string;
}

export function GameStatus({
  isGameOver,
  hasWon,
  currentBet,
  finalMultiplier,
  onCashOut,
  commitmentHash,
  gameSeed,
  isPayoutPending = false,
  isCashingOut = false,
  isSelectingTile = false,
  payoutAmount,
  payoutError,
  onRetryPayout,
  currentRowIndex,
  rows = [],
  updatedAt,
  gameBetAmount,
  processingTileIndex = null,
  gameOverText = 'DEATH TILE!',
  animationPending = false,
  selectedTiles,
  gameType,
}: GameStatusProps) {
  if (!config) {
    return null;
  }

  // ------------------------------------------------ STATE
  const [copyText, setCopyText] = useState<string>('Copied!');
  const [isCopied, setIsCopied] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [shouldShowRetry, setShouldShowRetry] = useState(false);
  const [hasPressedCashOut, setHasPressedCashOut] = useState(false);

  // ------------------------------------------------ HANDLERS
  const formatHash = (hash: string | null) => {
    if (!hash) return '';
    if (hash.length > 8) {
      return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
    }
    return hash;
  };

  const handleCopyHash = () => {
    if (!commitmentHash) return;

    navigator.clipboard
      .writeText(commitmentHash)
      .then(() => {
        setIsCopied(true);
        setCopyText('Copied!');

        setTimeout(() => {
          setIsCopied(false);
        }, 1500);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
      });
  };

  const handleRetryPayout = async () => {
    if (!onRetryPayout) return;
    setIsRetrying(true);
    await onRetryPayout();
    setIsRetrying(false);
  };

  const handleCashOut = async () => {
    try {
      setHasPressedCashOut(true);
      await onCashOut();
    } catch (error) {
      // If the cash out fails, reset the button state
      setHasPressedCashOut(false);
      console.error('Cash out failed:', error);
    }
  };

  // ------------------------------------------------ EFFECTS
  // Reset hasPressedCashOut when game state changes
  useEffect(() => {
    setHasPressedCashOut(false);
  }, [currentRowIndex, isGameOver]);

  useEffect(() => {
    // Show retry button if there's an error
    if (payoutError) {
      setShouldShowRetry(true);
      return;
    }

    // Check if transaction has been pending for too long (2+ minutes)
    if (isPayoutPending && updatedAt) {
      const updateTime = new Date(updatedAt).getTime();
      const currentTime = Date.now();
      const timeElapsed = currentTime - updateTime;

      // Check immediately if it's already been more than 2 minutes
      if (timeElapsed > 2 * 60 * 1000) {
        setShouldShowRetry(true);
        return;
      }

      // Set timeout to check again after 10 seconds
      const checkRetryTimeout = setTimeout(() => {
        // Recalculate time elapsed to account for the delay
        const newTimeElapsed = Date.now() - updateTime;
        // Show retry button after 2 minutes of being stuck
        if (newTimeElapsed > 2 * 60 * 1000) {
          setShouldShowRetry(true);
        }
      }, 10000);

      return () => clearTimeout(checkRetryTimeout);
    } else {
      setShouldShowRetry(false);
    }
  }, [isPayoutPending, payoutError, updatedAt]);

  let currentMultiplier = 1;
  if (currentRowIndex !== undefined) {
    const multipliers = calculateRowMultipliers(rows);
    currentMultiplier = isGameOver
      ? finalMultiplier
      : currentRowIndex > 0
        ? multipliers[currentRowIndex - 1]
        : 1;
  } else {
    currentMultiplier = finalMultiplier;
  }

  // ------------------------------------------------ RENDER

  if (isPayoutPending || isCashingOut || payoutError) {
    return (
      <div className='text-center'>
        <div className='font-black relative mb-2'>
          <div
            className={cn(
              ' text-lg md:text-2xl flex items-center justify-center gap-2',
              payoutError ? 'text-red-400' : 'text-gc-400'
            )}
          >
            {payoutError ? (
              'Payout Failed'
            ) : (
              <>
                Processing Payout
                <Spinner className='w-4 h-4 animate-spin' />
              </>
            )}
          </div>
        </div>
        <div className='text-sm text-muted-foreground max-w-md mx-auto mb-4'>
          <div className='flex flex-col space-y-1'>
            <span>
              Your payout of{' '}
              <span className='text-gc-400 font-bold'>{formatCurrency(payoutAmount)}</span>{' '}
              {payoutError ? 'failed to process' : 'is being processed'}
            </span>
            {!payoutError && !shouldShowRetry && (
              <span className='text-xs'>This should only take a moment</span>
            )}
            {payoutError && <span className='text-xs text-red-400'>{payoutError}</span>}
          </div>
        </div>

        {shouldShowRetry && onRetryPayout && (
          <Button variant='primary' size='lg' onClick={handleRetryPayout} disabled={isRetrying}>
            {isRetrying ? (
              <>
                <Spinner className='mr-2 h-4 w-4 animate-spin' />
                Retrying...
              </>
            ) : (
              <>
                <Shuffle className='mr-2 h-4 w-4' />
                Retry Payout
              </>
            )}
          </Button>
        )}
      </div>
    );
  }

  if (isGameOver && !animationPending) {
    let points = 0;
    const betAmount = (gameBetAmount ?? currentBet).toString();

    // if (currentRowIndex !== undefined) {
    //   if (hasWon) {
    //     points = calculatePoints(betAmount, finalMultiplier);
    //   } else {
    //     const bustedRowMultiplier = rows?.[currentRowIndex]?.multiplier ?? 0;
    //     points = calculatePoints(betAmount, bustedRowMultiplier);
    //   }
    // } else {
    //   points = calculatePoints(betAmount, finalMultiplier);
    // }

    if (finalMultiplier) {
      points = calculatePoints(betAmount, finalMultiplier);
    } else {
      if (hasWon) {
        points = calculatePoints(betAmount, finalMultiplier);
      } else {
        const bustedRowMultiplier = rows?.[currentRowIndex ?? 0]?.multiplier ?? 0;
        points = calculatePoints(betAmount, bustedRowMultiplier);
      }
    }

    return (
      <div className='flex flex-col items-center justify-center gap-4'>
        <div className='relative'>
          <div
            className={`text-xl md:text-4xl font-bold text-black rounded-full px-5 py-1 sm:py-1.5 flex items-center gap-2 ${
              hasWon ? 'bg-gc-400' : 'bg-red-500'
            }`}
          >
            {hasWon ? (
              <div className='flex items-center gap-2'>
                <CountUp
                  end={Math.floor(finalMultiplier * 100) / 100}
                  decimals={2}
                  duration={1}
                  preserveValue
                />
                x
              </div>
            ) : (
              <div>{gameOverText}</div>
            )}
          </div>
          <VerificationTooltip
            commitmentHash={commitmentHash}
            gameSeed={gameSeed}
            rows={rows}
            text={false}
            className='rounded-full aspect-square absolute top-1/2 right-0 -translate-y-1/2 translate-x-[2.3rem]'
            selectedTiles={selectedTiles}
            gameType={gameType}
          />
        </div>
        <div className='flex items-center gap-10'>
          {hasWon && (
            <div className='text-lg md:text-2xl font-bold text-gc-400 flex items-center gap-1'>
              <Icon id='eth' className='w-5 h-5' />
              <CountUp
                prefix=''
                end={Number(formatCurrency(payoutAmount, false))}
                decimals={config.currency.decimals > 0 ? 4 : 0}
                duration={1}
                preserveValue
                separator=','
              />
            </div>
          )}
          {points > 0 && (
            <div
              className='text-white text-lg md:text-2xl font-bold flex items-center gap-2 cursor-pointer'
              onClick={() => window.dispatchEvent(new Event('open-death-points'))}
            >
              <Icon id='dp-icon-face' className='w-[1.125rem] h-[1.125rem]' />
              <CountUp prefix='+' start={0} end={points} duration={1} decimals={2} preserveValue />
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col items-center gap-4'>
      <div className='flex flex-col items-center'>
        <div className='text-3xl md:text-4xl font-bold tabular-nums flex items-center gap-8'>
          <span className='text-white'>
            <CountUp
              end={Math.floor(currentMultiplier * 100) / 100}
              decimals={2}
              duration={0.5}
              preserveValue
            />
            x
          </span>
          <span className='text-gc-400 flex items-center gap-0.5'>
            <Icon id='eth' className='w-6 h-6' />
            <CountUp
              prefix=''
              end={Number(formatCurrency(payoutAmount, false))}
              decimals={config.currency.decimals > 0 ? 4 : 0}
              duration={0.5}
              preserveValue
              separator=','
            />
          </span>
        </div>
      </div>
      <div className='flex flex-col items-center gap-3'>
        <Button
          size='lg'
          variant='white'
          onClick={handleCashOut}
          className='w-full md:w-[10rem] uppercase'
          disabled={
            currentRowIndex === 0 ||
            hasPressedCashOut ||
            processingTileIndex !== null ||
            isSelectingTile ||
            isCashingOut
          }
        >
          Cash Out
        </Button>

        {commitmentHash && (
          <Button
            variant='link'
            size='xs'
            aria-label='Copy commitment hash'
            onClick={handleCopyHash}
          >
            <Copy className='w-3 h-3' />
            {isCopied ? copyText : `GAME HASH: ${formatHash(commitmentHash)}`}
          </Button>
        )}
      </div>
    </div>
  );
}
