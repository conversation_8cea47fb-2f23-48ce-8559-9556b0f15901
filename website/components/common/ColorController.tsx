'use client';

import { COLOR_PALETTES, ColorPalette } from '@/lib/constants';
import { getGameMeta } from '@/lib/utils';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

// ------------------------------------------------------------ UTILS ------------------------------------------------------------

function applyColorPalette(colors: (typeof COLOR_PALETTES)[keyof typeof COLOR_PALETTES]) {
  Object.entries(colors).forEach(([property, value]) => {
    document.documentElement.style.setProperty(property, value);
  });
}

// ------------------------------------------------------------ COLOR CONTROLLER ------------------------------------------------------------

export function ColorController() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const gameType = searchParams.get('gameType');
    const gameMeta = getGameMeta(pathname, gameType);

    applyColorPalette(gameMeta.theme);
  }, [pathname, searchParams]);

  return null;
}
