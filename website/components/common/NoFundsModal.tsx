'use client';
import { Wallet as WalletIcon } from '@phosphor-icons/react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog/Dialog';
import { Button } from '@/components/ui/button/Button';
import { AnimatedFaceIcon } from '@/components/ui/icons/animated/AnimatedFaceIcon';
import { useGlobalStore } from '@/lib/store';

export function NoFundsModal() {
  // ------------------------------------------------ STATE
  const noFunds = useGlobalStore((state) => state.noFunds);
  const toggleModals = useGlobalStore((state) => state.toggleModals);

  // ------------------------------------------------ HANDLERS
  const handleAddFunds = () => {
    toggleModals({ noFunds: false, bridge: true });
  };

  return (
    <Dialog open={noFunds} onOpenChange={(open) => toggleModals({ noFunds: open })}>
      <DialogContent className='bg-black/60 border-white/10 backdrop-blur-md'>
        <DialogHeader className='flex flex-col items-center justify-center gap-2'>
          <AnimatedFaceIcon
            className='w-24 h-24 text-gc-400'
            animation='headache'
            isAnimating={noFunds}
          />
          <DialogTitle className='text-3xl font-bold text-center'>You have no ETH</DialogTitle>
        </DialogHeader>
        <div className='space-y-6 text-white/80'>
          <p className='text-center'>Bridge some cash to start playing!</p>

          <div className='space-y-3'>
            <Button onClick={handleAddFunds} variant='primary' size='lg' className='w-full'>
              Add funds
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
