import { Skull } from '@phosphor-icons/react';
import { motion } from 'framer-motion';
import React from 'react';

interface SkullExplosionProps {
  showExplosion: boolean;
  explosionPos: { x: number; y: number } | null;
  duration?: number; // Duration in milliseconds, defaults to 1800ms
}

export const SkullExplosion: React.FC<SkullExplosionProps> = ({
  showExplosion,
  explosionPos,
  duration = 1800,
}) => {
  if (showExplosion) {
    const isRelativeToParent = explosionPos && explosionPos.x === 0 && explosionPos.y === 0;

    return (
      <motion.div
        initial={{ opacity: 1, scale: 1, x: '-50%', y: '-50%' }}
        animate={{ 
          opacity: 0, 
          scale: 16,
          x: '-50%',
          y: '-50%'
        }}
        transition={{
          type: "spring",
          stiffness: 60,
          damping: 30,
        }}
        style={{
          position: isRelativeToParent ? 'absolute' : 'fixed',
          left: isRelativeToParent ? '50%' : explosionPos ? `${explosionPos.x}px` : '50%',
          top: isRelativeToParent ? '50%' : explosionPos ? `${explosionPos.y}px` : '50%',
          zIndex: 50,
          pointerEvents: 'none',
        }}
        className=''
      >
        <Skull className='w-16 h-16 md:w-32 md:h-32 text-red-500 drop-shadow-2xl' />
      </motion.div>
    );
  }
};
