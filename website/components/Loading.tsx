'use client';

import { motion } from 'framer-motion';
import { Icon } from './ui/icons/Icon';

export function LoadingScreen() {
  return (
    <div className='fixed inset-0 bg-black flex items-center justify-center z-50'>
      <motion.div
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{
          scale: 1,
          opacity: 1,
          rotate: [0, 10, -10, 10, -10, 0],
        }}
        transition={{
          duration: 1.5,
          scale: {
            type: 'spring',
            damping: 7,
            stiffness: 100,
          },
          rotate: {
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          },
        }}
      >
        <Icon id="logo" className='w-20 h-20 text-success' />
      </motion.div>
    </div>
  );
}
