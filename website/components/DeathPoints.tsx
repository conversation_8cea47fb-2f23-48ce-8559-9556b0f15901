import { Button } from './ui/button/Button';
import CountUp from 'react-countup';
import { POINTS_ETH_RATIO } from '@/lib/constants';
import { useRef, useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogHeader } from '@/components/ui/dialog/Dialog';
import { Icon } from './ui/icons/Icon';

interface DeathPointsProps {
  points: number;
  className?: string;
}

export function DeathPoints({ points, className }: DeathPointsProps) {
  // For CountUp: preserve previous value for animation
  const prevPoints = useRef(points);
  const start = prevPoints.current;
  if (start !== points) {
    prevPoints.current = points;
  }
  const [open, setOpen] = useState(false);

  // Listen for custom event to open modal externally
  useEffect(() => {
    const handler = () => setOpen(true);
    window.addEventListener('open-death-points', handler);
    return () => {
      window.removeEventListener('open-death-points', handler);
    };
  }, []);

  return (
    <>
      <Button
        size='sm'
        variant='secondary'
        className={className}
        tabIndex={0}
        onClick={() => setOpen(true)}
      >
        <Icon id='dp-icon-face' className='w-4 h-4' />
        <span className='font-semibold'>
          <CountUp start={start} end={points} duration={1} decimals={2} preserveValue />
        </span>
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle className='text-gc-400 text-2xl text-center flex items-center justify-center gap-2 mb-5'>
              <Icon id='dp-icon-face' className='w-5 h-5' />
              <span>Death Points</span>
            </DialogTitle>
            <DialogDescription asChild>
              <div className='text-[1rem] text-white !text-left'>
                <div className='text-gc-400 mb-5'>
                  You earn <span className='font-bold '>Death Points</span> on every game -- cash
                  out or bust!
                </div>
                <ul className='list-disc pl-5 space-y-5'>
                  <li>Points are based on how much ETH is risked at any point in a game</li>
                  <li>Death Points = Amount of ETH risked * {POINTS_ETH_RATIO}</li>
                  <li>
                    If you hit a death tile but would have earned 1 ETH, you get 150 Death Points
                  </li>
                  <li>Death Points will be used for future ecosystem rewards</li>
                </ul>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
