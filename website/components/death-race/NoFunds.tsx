import { Button } from '@/components/ui/button/Button';
import { useBridge } from '@/lib/hooks/flags/useBridge';
import { useGlobalStore } from '@/lib/store';

interface NoFundsProps {
  showText?: boolean;
}

export function NoFunds({ showText }: NoFundsProps) {
  // ------------------------------- STATE
  const toggleModals = useGlobalStore((state) => state.toggleModals);

  // ------------------------------- HOOKS
  const bridgeEnabled = useBridge();

  // ------------------------------- HANDLERS
  const handleAddFunds = () => {
    toggleModals({ bridge: true });
  };

  return (
    <div className='flex flex-col items-center w-full gap-4'>
      {showText && (
        <div className='gap-4'>
          <div className='text-2xl font-bold text-center'>You need ETH to play!</div>
        </div>
      )}
      {bridgeEnabled ? (
        <Button
          size='xl'
          variant='primary'
          className='w-[12rem] sm:w-[16rem]'
          onClick={handleAddFunds}
        >
          Add Funds
        </Button>
      ) : (
        <Button
          size='xl'
          variant='primary'
          className='w-[12rem] sm:w-[16rem]'
          to='https://portal.abs.xyz/profile'
        >
          Add Funds
        </Button>
      )}
    </div>
  );
}
