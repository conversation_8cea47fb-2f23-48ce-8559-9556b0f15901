import { Shuffle, Wallet, X, Warning, Gear, Divide } from '@phosphor-icons/react';
import { Button } from '@/components/ui/button/Button';
import { useEffect, useRef, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  DEATH_RACE_MAX_BET_PERCENTAGE,
  ENABLE_DEMO_MODE,
  DEATH_RACE_GAME_TYPE,
  LASER_PARTY_GAME_TYPE,
} from '@/lib/constants';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog/Dialog';
import { toast } from 'sonner';
import {
  DEATH_RACE_TOTAL_ROWS,
  DEATH_RACE_MIN_TILES,
  DEATH_RACE_MAX_TILES,
  DEATH_FUN_MIN_BET_ETH,
} from '@/lib/constants';
// import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { usePrivy } from '@privy-io/react-auth';
import { useGetUser } from '@/lib/client/queries';
import { isAdmin } from '@/lib/utils';
import { formatCurrency } from '@/lib/client/wallet';
import { abstractClientConfig } from '@/lib/utils/abstract/config';
import { useGambling } from '@/lib/hooks/useGambling';
import { formatEther, parseEther } from 'viem';
import { GameRow } from './types';
import { StartGameButton } from './StartGameButton';
import Clickable from '../ui/button/Clickable';
import { Icon } from '../ui/icons/Icon';
import { GridSizeSelector } from '../laser-party/components/GridSizeSelector';
import { TextInput } from '../ui/input/TextInput';
import { NumberInput } from '../ui/input/NumberInput';
import { useAbstractPrivyLogin } from '@abstract-foundation/agw-react/privy';

interface GameControlsProps {
  currentBet: bigint;
  isStartDisabled: boolean;
  gameCreationStatus: string;
  transactionInProgress?: boolean;
  gameType: typeof DEATH_RACE_GAME_TYPE | typeof LASER_PARTY_GAME_TYPE;
  onBetChange: (bet: bigint) => void;
  onStartGame: () => void;
  onTransactionComplete?: () => void;
  shuffleRows: (customRows?: GameRow[]) => void;
  onTryDemo?: () => void;
  onShowFaq?: () => void;
  // Laser Party specific props
  currentGridSize?: number;
  onGridSizeChange?: (size: number) => void;
}

const PRESET_BETS = [parseEther('0.001'), parseEther('0.01'), parseEther('0.1'), parseEther('1')];

export function GameControls({
  currentBet,
  gameCreationStatus,
  isStartDisabled,
  transactionInProgress = false,
  gameType,
  onBetChange,
  onStartGame,
  onTransactionComplete,
  shuffleRows,
  onTryDemo,
  onShowFaq,
  currentGridSize = 10,
  onGridSizeChange,
}: GameControlsProps) {
  // ------------------------------------------------ HOOKS
  const { authenticated } = usePrivy();
  const { login } = useAbstractPrivyLogin();
  const { data: userData } = useGetUser();
  const { role } = userData ?? {};
  const admin = isAdmin(role);
  const { isPotLoading, maxBetAmount, userBalance, isUserBalanceLoading } = useGambling(gameType);
  const balance = userBalance || BigInt(0);

  // ------------------------------------------------ STATE
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [lastSelectedPreset, setLastSelectedPreset] = useState(PRESET_BETS[0]);
  const [showTileCountDialog, setShowTileCountDialog] = useState(false);
  const [tileCount, setTileCount] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // ------------------------------------------------ HANDLERS
  const handlePresetBetClick = useCallback(
    (amount: bigint) => {
      if (maxBetAmount !== undefined && amount > maxBetAmount) {
        return;
      }
      setLastSelectedPreset(amount);
      setShowCustomInput(false);
      onBetChange(amount);
    },
    [balance, maxBetAmount, onBetChange]
  );

  const handleCustomInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (!/^[0-9]*\.?[0-9]*$/.test(value)) {
      return;
    }
    setInputValue(value);
    let parsed: bigint;
    try {
      parsed = value ? parseEther(value) : BigInt(0);
    } catch {
      return;
    }
    if (parsed < DEATH_FUN_MIN_BET_ETH) {
      return;
    }
    if (maxBetAmount !== undefined && parsed > maxBetAmount) {
      return;
    }
    if (parsed > (balance ?? BigInt(0))) {
      return;
    }
    onBetChange(parsed);
  };

  const handleCustomInputToggle = () => {
    if (showCustomInput) {
      const validPreset = PRESET_BETS.find(
        (p) => p <= (balance || 0) && (maxBetAmount === undefined || p <= maxBetAmount)
      );
      handlePresetBetClick(validPreset ?? PRESET_BETS[0]);
    } else {
      const newInput = currentBet > BigInt(0) ? formatEther(currentBet) : '';
      setInputValue(newInput);
    }
    setShowCustomInput(!showCustomInput);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isStartDisabled && !transactionInProgress) {
      onStartGame();
    }
  };

  const handleTileCountSubmit = () => {
    const count = parseInt(tileCount);
    if (isNaN(count) || count < DEATH_RACE_MIN_TILES || count > DEATH_RACE_MAX_TILES) {
      toast.error(`Tile count must be between ${DEATH_RACE_MIN_TILES} and ${DEATH_RACE_MAX_TILES}`);
      return;
    }

    const newRows = Array(DEATH_RACE_TOTAL_ROWS)
      .fill(null)
      .map(() => ({
        tiles: count,
        deathTileIndex: null,
        chosenTileIndex: undefined,
      }));

    // onRefreshRows(newRows);
    setShowTileCountDialog(false);
    setTileCount('');
  };

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    if (balance !== null && !showCustomInput && currentBet === BigInt(0)) {
      const validPreset = PRESET_BETS.find(
        (p) => p <= (balance || 0) && (maxBetAmount === undefined || p <= maxBetAmount)
      );
      handlePresetBetClick(validPreset ?? PRESET_BETS[0]);
    }
  }, [balance, maxBetAmount, showCustomInput, currentBet]);

  useEffect(() => {
    if (showCustomInput && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showCustomInput]);

  useEffect(() => {
    const isPresetBet = PRESET_BETS.includes(currentBet);
    if (!isPresetBet && currentBet > 0 && !showCustomInput) {
      setShowCustomInput(true);
      setInputValue(formatEther(currentBet));
    }
  }, [currentBet, showCustomInput]);

  // ---------------------- Refactored className for custom bet input
  const customInputClassName = cn(
    'w-full text-sm bg-slate-950/80',
    'shadow-[inset_0_2px_4px_rgba(0,0,0,0.3)]',
    (() => {
      let parsed: bigint;
      try {
        parsed = inputValue ? parseEther(inputValue) : BigInt(0);
      } catch {
        return 'text-red-500 border-red-500 focus:border-red-500';
      }
      if (parsed > balance) return 'text-red-500 border-red-500 focus:border-red-500';
      if (maxBetAmount && parsed > maxBetAmount)
        return 'text-red-500 border-red-500 focus:border-red-500';
      return 'text-white border-white/60 focus:border-gc-400';
    })(),
    '[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
  );

  // ---------------------- Refactored check for custom input over max bet
  const isCustomInputOverMaxBet = (() => {
    if (!maxBetAmount) return false;
    try {
      return parseEther(inputValue) > maxBetAmount;
    } catch {
      return false;
    }
  })();

  // --- Error state helpers for custom input ---
  let parsedInputValue: bigint | null = null;
  try {
    parsedInputValue = inputValue ? parseEther(inputValue) : BigInt(0);
  } catch {
    parsedInputValue = null;
  }
  const customInputError =
    showCustomInput &&
    parsedInputValue !== null &&
    (parsedInputValue < DEATH_FUN_MIN_BET_ETH ||
      (maxBetAmount !== undefined && parsedInputValue > maxBetAmount) ||
      parsedInputValue > (balance || BigInt(0)));
  const isCustomInputAboveMax =
    showCustomInput &&
    parsedInputValue !== null &&
    maxBetAmount !== undefined &&
    parsedInputValue > maxBetAmount;

  // ------------------------------------------------ RENDER
  if (!authenticated) {
    return (
      <div className='flex flex-col items-center w-full'>
        <div className='flex justify-center items-center w-full gap-4 px-5'>
          <Button
            onClick={login}
            size='lg'
            variant='primary'
            className={` ${ENABLE_DEMO_MODE ? 'md:w-[12rem]' : 'md:w-[16rem]'}`}
          >
            Sign in
          </Button>
          {ENABLE_DEMO_MODE && (
            <>
              <div className='text-white/80 font-bold text-sm'>or</div>
              <Button onClick={onTryDemo} size='lg' variant='white' className='md:w-[12rem]'>
                Play Demo
              </Button>
            </>
          )}
        </div>
        {ENABLE_DEMO_MODE && (
          <Clickable
            onClick={onShowFaq}
            className='mt-3 text-xs text-white/80 underline underline-offset-2 hover:text-white transition-colors duration-150'
          >
            How does this work?
          </Clickable>
        )}
      </div>
    );
  }

  if (isUserBalanceLoading || isPotLoading) {
    return (
      <div className='flex justify-center w-full'>
        <div className='text-white/60'>Loading balance...</div>
      </div>
    );
  }

  const isCurrentBetExceedsMax = maxBetAmount !== undefined && currentBet > maxBetAmount;

  const isTestnet = abstractClientConfig.chainId === 'abstract-testnet';
  const showFaucet = isTestnet && balance === BigInt(0);

  return (
    <TooltipProvider delayDuration={0}>
      <form onSubmit={handleSubmit} className='flex flex-col items-center w-full'>
        <div className='flex flex-col md:flex-row items-center gap-4 w-full md:justify-center'>
          <div className='flex flex-col justify-center items-center w-full md:w-auto relative gap-3'>
            {/* Testnet Claim Button */}
            {showFaucet && (
              <div className='absolute inset-0 bg-gc-950 z-30 rounded-xl flex flex-col items-center justify-center space-y-1'>
                <p className='text-white/80'>You need devnet SOL to play. </p>
                <Clickable
                  className='block text-gc-400 underline'
                  href='https://faucet.solana.com/'
                >
                  Get some from the faucet!
                </Clickable>
              </div>
            )}

            <div className='flex flex-col items-center justify-center w-full'>
              <div className='flex items-center gap-3 py-1.5 w-full'>
                <div className='flex items-center flex-col w-full md:flex-row gap-3 flex-1 md:flex-initial'>
                  {/* Desktop-Only Shuffle Button */}
                  {gameType === DEATH_RACE_GAME_TYPE && (
                    <Button
                      type='button'
                      onClick={() => shuffleRows()}
                      size='lg'
                      variant='white'
                      className='hidden md:flex shrink-0 mr-5'
                      disabled={transactionInProgress || gameCreationStatus === 'creation_failed'}
                    >
                      <span>Shuffle Tiles</span>
                    </Button>
                  )}

                  {/* Desktop-Only Grid Size Button for Laser Party */}
                  {gameType === LASER_PARTY_GAME_TYPE && onGridSizeChange && (
                    <GridSizeSelector
                      currentGridSize={currentGridSize}
                      onGridSizeChange={onGridSizeChange}
                      disabled={transactionInProgress || gameCreationStatus === 'creation_failed'}
                      className='hidden md:flex mr-5'
                    />
                  )}

                  <div className='flex flex-row items-center justify-center gap-3 w-full'>
                    {/* Bet Amount Inputs */}
                    <span className='text-gc-400 font-medium uppercase text-sm'>Bet</span>
                    {showCustomInput ? (
                      <div className='flex flex-1 md:w-[13rem] relative'>
                        <TextInput
                          ref={inputRef}
                          type='text'
                          inputMode='decimal'
                          value={inputValue}
                          onChange={handleCustomInputChange}
                          className={customInputClassName}
                          size='lg'
                        />
                        {maxBetAmount &&
                          (() => {
                            try {
                              return parseEther(inputValue) > maxBetAmount;
                            } catch {
                              return false;
                            }
                          })() && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className='absolute right-9 top-1/2 -translate-y-1/2 text-red-500'>
                                  <Warning className='w-4 h-4' />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent className='bg-red-500 text-white border-0'>
                                <p>Bet exceeds {DEATH_RACE_MAX_BET_PERCENTAGE * 100}% pot limit</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        <button
                          type='button'
                          onClick={handleCustomInputToggle}
                          className='absolute right-2 top-1/2 -translate-y-1/2 text-white/60 hover:text-white'
                        >
                          <X className='w-4 h-4' />
                        </button>
                      </div>
                    ) : (
                      <div className='flex-1 md:w-[13rem]'>
                        <div className='outline outline-gc-400 rounded-md sm:p-1 shadow-[inset_0_2px_4px_rgba(0,0,0,0.3)] h-[2rem] sm:h-11'>
                          <div
                            className={`relative grid ${PRESET_BETS.length === 4 ? 'grid-cols-4' : 'grid-cols-3'} h-full`}
                          >
                            <div
                              className='absolute inset-1 bg-gc-400 rounded transition-all duration-200 ease-out z-0'
                              style={{
                                width: `calc(${PRESET_BETS.length === 4 ? '25%' : '33.333%'} - 0.5rem)`,
                                left: `calc(${PRESET_BETS.indexOf(currentBet)} * ${PRESET_BETS.length === 4 ? '25%' : '33.333%'} + 0.25rem)`,
                              }}
                            />

                            {PRESET_BETS.map((amount) => {
                              const isOverBalance = amount > (balance || 0);
                              const isOverMaxBet =
                                maxBetAmount !== undefined && amount > maxBetAmount;
                              const isDisabled =
                                amount < DEATH_FUN_MIN_BET_ETH ||
                                isOverBalance ||
                                isOverMaxBet ||
                                transactionInProgress ||
                                gameCreationStatus === 'creation_failed';

                              return (
                                <Tooltip key={amount.toString()}>
                                  <TooltipTrigger asChild>
                                    <button
                                      type='button'
                                      onClick={() => handlePresetBetClick(amount)}
                                      disabled={isDisabled}
                                      className={cn(
                                        'relative z-10 text-center rounded transition-colors duration-200 font-bold text-sm h-full mx-0.5 first:ml-0 last:mr-0',
                                        currentBet === amount
                                          ? 'text-gray-900'
                                          : 'text-white hover:text-gc-400',
                                        isDisabled && 'opacity-50 cursor-not-allowed'
                                      )}
                                    >
                                      {amount === parseEther('0.001')
                                        ? '.001'
                                        : amount === parseEther('0.01')
                                          ? '.01'
                                          : formatEther(amount)}
                                    </button>
                                  </TooltipTrigger>
                                  {isOverMaxBet && amount !== DEATH_FUN_MIN_BET_ETH && (
                                    <TooltipContent className='bg-red-500 text-white border-0'>
                                      <p>
                                        Bet exceeds {DEATH_RACE_MAX_BET_PERCENTAGE * 100}% pot limit
                                      </p>
                                    </TooltipContent>
                                  )}
                                </Tooltip>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Start Game Button + Admin Set Tiles Button */}
                    <div className='flex items-center gap-2'>
                      <StartGameButton
                        className='hidden md:flex'
                        isStartDisabled={isStartDisabled}
                        showCustomInput={showCustomInput}
                        parsedInputValue={parsedInputValue}
                        balance={balance}
                        isCurrentBetExceedsMax={isCurrentBetExceedsMax}
                        transactionInProgress={transactionInProgress}
                        currentBet={currentBet}
                        maxBetAmount={maxBetAmount}
                        gameCreationStatus={gameCreationStatus}
                        isCustomInputAboveMax={isCustomInputAboveMax}
                      />

                      {/* Mobile-Only Shuffle Button */}
                      {gameType === DEATH_RACE_GAME_TYPE && (
                        <Button
                          type='button'
                          onClick={() => shuffleRows()}
                          variant='white'
                          size='icon'
                          className='w-auto aspect-square md:hidden'
                          disabled={
                            transactionInProgress || gameCreationStatus === 'creation_failed'
                          }
                        >
                          <Shuffle className='!w-5 !h-5' />
                        </Button>
                      )}

                      {/* Mobile-Only Grid Size Button for Laser Party */}
                      {gameType === LASER_PARTY_GAME_TYPE && onGridSizeChange && (
                        <GridSizeSelector
                          currentGridSize={currentGridSize}
                          onGridSizeChange={onGridSizeChange}
                          disabled={
                            transactionInProgress || gameCreationStatus === 'creation_failed'
                          }
                          className='md:hidden h-8'
                        />
                      )}

                      {admin && (
                        <Dialog open={showTileCountDialog} onOpenChange={setShowTileCountDialog}>
                          <DialogTrigger asChild>
                            <Button
                              type='button'
                              variant='white'
                              className='aspect-square shrink-0 hidden md:flex'
                              disabled={transactionInProgress}
                            >
                              <Gear style={{ width: '1.25rem', height: '1.25rem' }} weight='bold' />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className='bg-black border-gc-400/20'>
                            <DialogHeader>
                              <DialogTitle className='text-gc-400'>Customize Row Tiles</DialogTitle>
                              <DialogDescription className='text-white/60'>
                                Enter the number of tiles you want in each row (
                                {DEATH_RACE_MIN_TILES}-{DEATH_RACE_MAX_TILES})
                              </DialogDescription>
                            </DialogHeader>
                            <div className='py-4'>
                              <NumberInput
                                min={DEATH_RACE_MIN_TILES}
                                max={DEATH_RACE_MAX_TILES}
                                value={tileCount}
                                onChange={(e) => setTileCount(e.target.value)}
                                className='bg-slate-950/80 text-white border-white/60 focus:border-gc-400'
                                placeholder={`Enter number (${DEATH_RACE_MIN_TILES}-${DEATH_RACE_MAX_TILES})`}
                              />
                            </div>
                            <DialogFooter>
                              <Button
                                variant='outline'
                                onClick={() => setShowTileCountDialog(false)}
                              >
                                Cancel
                              </Button>
                              <Button variant='primary' onClick={handleTileCountSubmit}>
                                Apply
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Wallet + Custom Bet Link */}
              <div className='flex justify-center md:justify-end gap-3 text-xs mt-0.5 md:mt-1.5 md:pr-28 w-full'>
                <button
                  type='button'
                  onClick={() => {}}
                  className='text-gc-400 hover:text-gc-500 transition-colors duration-200 flex items-center gap-1'
                  title='Set maximum allowed bet'
                >
                  <Wallet className='w-3 h-3' />
                  <div className='flex items-center gap-0.5'>
                    <Icon id='eth' className='w-2.5 h-2.5 inline align-middle' />
                    <span>{formatCurrency(balance, false)}</span>
                  </div>
                </button>
                <span>/</span>
                <button
                  type='button'
                  onClick={handleCustomInputToggle}
                  className={cn(
                    'transition-colors duration-200 uppercase',
                    showCustomInput
                      ? 'text-white/30 hover:text-white/40'
                      : 'text-gc-400 hover:text-gc-500'
                  )}
                  title='Toggle custom bet input'
                  disabled={transactionInProgress || gameCreationStatus === 'creation_failed'}
                >
                  custom bet
                </button>
              </div>

              <StartGameButton
                className='md:hidden h-[2.5rem] w-[12rem] mt-5 text-lg'
                isStartDisabled={isStartDisabled}
                showCustomInput={showCustomInput}
                parsedInputValue={parsedInputValue}
                balance={balance}
                isCurrentBetExceedsMax={isCurrentBetExceedsMax}
                transactionInProgress={transactionInProgress}
                currentBet={currentBet}
                maxBetAmount={maxBetAmount}
                gameCreationStatus={gameCreationStatus}
                isCustomInputAboveMax={isCustomInputAboveMax}
              />
            </div>
          </div>
        </div>
      </form>
    </TooltipProvider>
  );
}
