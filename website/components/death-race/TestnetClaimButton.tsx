import { Button } from '@/components/ui/button/Button';
import { TESTNET_CLAIM_AMOUNT } from '@/lib/constants';
import { formatEther } from 'viem';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { getUserOptions } from '@/lib/client/queries';

export function TestnetClaimButton({ onClaim }: { onClaim: () => void }) {
  const queryClient = useQueryClient();

  const { mutateAsync: claimTestnetFunds, isPending: isClaiming } = useMutation({
    mutationKey: ['claimTestnetFunds'],
    mutationFn: async () => {
      const response = await fetch('/api/abstract/testnet-claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to claim testnet funds');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Successfully claimed testnet funds!');
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
    },
    onError: (error) => {
      console.error('Error claiming testnet funds:', error);
      toast.error('Failed to claim testnet funds', {
        description: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    },
  });

  const handleClaim = async () => {
    try {
      await claimTestnetFunds();
      // Only call onClaim after successful claim
      onClaim();
    } catch (error) {
      // Error is already handled by the mutation
      console.error('Error in handleClaim:', error);
    }
  };

  return (
    <div className='flex flex-col items-center w-full gap-4'>
      <div className='gap-4'>
        <div className='text-xl font-bold text-center'>You need some test ETH!</div>
        <div className='text-sm text-red-500 text-center'>
          You can claim just once, so <u>make it count</u>
        </div>
      </div>
      <Button
        onClick={handleClaim}
        disabled={isClaiming}
        size='xl'
        className='bg-gc-400 hover:bg-gc-500 text-gray-900 px-4 w-full sm:w-[16rem] font-bold'
      >
        {isClaiming ? 'Claiming...' : `Claim ${formatEther(TESTNET_CLAIM_AMOUNT)} Test ETH`}
      </Button>
    </div>
  );
}
