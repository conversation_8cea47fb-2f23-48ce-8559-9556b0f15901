import { Check } from '@phosphor-icons/react';
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ALGO_VERSION } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { Button } from '../ui/button/Button';

interface VerificationTooltipProps {
  commitmentHash: string | null;
  gameSeed: string | null;
  rows: any[];
  selectedTiles?: any;
  text?: string | false;
  className?: string;
  gameType?: string;
}

export function VerificationTooltip({
  commitmentHash,
  gameSeed,
  rows,
  selectedTiles,
  text,
  className,
  gameType,
}: VerificationTooltipProps) {
  // Create verification URL for the checkmark icon
  const getVerificationUrl = () => {
    // External verify tool URL
    const baseUrl = 'https://death-fun.github.io/death-fun-provably-fair/index.html';
    const params = new URLSearchParams();
    params.append('version', ALGO_VERSION);
    // rows as comma-separated tile counts
    const rowCounts = (rows || []).map((row: any) => row.tiles).join(',');
    params.append('rows', rowCounts);
    if (gameSeed) params.append('seed', gameSeed);
    if (commitmentHash) params.append('hash', commitmentHash);
    if (selectedTiles && selectedTiles.length > 0) {
      // Handle different selectedTiles formats based on data structure
      // Check if it's a nested array (array of arrays) vs flat array
      const isNestedArray = Array.isArray(selectedTiles[0]);

      if (isNestedArray) {
        // For nested arrays: selectedTiles is number[][] (array of coordinates)
        // Format as "x1,y1;x2,y2;x3,y3"
        const formattedTiles = selectedTiles.map((coords: number[]) => coords.join(',')).join(';');
        params.append('selectedTiles', formattedTiles);
      } else {
        // For flat arrays: selectedTiles is number[] (simple array)
        // Format as "1,2,3,4"
        params.append('selectedTiles', selectedTiles.join(','));
      }
    }
    if (gameType) {
      params.append('game', gameType);
    }
    return `${baseUrl}?${params.toString()}`;
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
        <Button
            variant='outline'
            size='xs'
            to={getVerificationUrl()}
            className={cn('uppercase', className)}
          >
            <Check className='w-3 h-3 text-white' />
            {text !== false && (
              <span>
                {typeof text === 'string' ? text : 'Verify'}
              </span>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side='top'>
          <p className='max-w-xs'>Click here to verify that your game was provably fair.</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
