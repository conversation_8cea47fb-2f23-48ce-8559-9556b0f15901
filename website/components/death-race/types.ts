import { TargetDimension } from "@/lib/types/laserParty";

// Base row data stored in the database
export interface GameRow {
  tiles: number;
  deathTileIndex: number | null;
  chosenTileIndex?: number;
}

// Row data with multiplier for display
export interface DisplayRow extends GameRow {
  multiplier: number; // The cumulative multiplier up to this row
}

export interface LPDisplayRow extends DisplayRow {
  dimension: TargetDimension;
}

export interface GameState {
  rows: DisplayRow[];
  currentRowIndex: number;
  currentBet: bigint;
  isGameOver: boolean;
  hasWon: boolean;
  finalMultiplier: number;
}
