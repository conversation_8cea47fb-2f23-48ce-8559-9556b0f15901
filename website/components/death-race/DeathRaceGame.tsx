'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { GameBoard } from './GameBoard';
import { GameControls } from './GameControls';
import { GameStatus } from '../common/GameStatus';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button/Button';
import {
  getActiveGameOptions,
  getRowConfigOptions,
  useGetUser,
  getUserOptions,
  getLeaderboardEntriesOptions,
} from '@/lib/client/queries';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { useGambling } from '@/lib/hooks/useGambling';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useGame } from '@/lib/hooks/useGame';
import superjson from 'superjson';
import { calculateRowMultipliers } from '@/lib/utils/game';
import { useDemoGame } from '@/lib/hooks/useDemoGame';
import { parseEther } from 'viem';
import { TestnetClaimButton } from './TestnetClaimButton';
import { NoFunds } from './NoFunds';
import {
  DEATH_FUN_MIN_BET_ETH,
  DEATH_RACE_GAME_TYPE,
  IS_MAINNET,
  TESTNET_CLAIM_THRESHOLD,
} from '@/lib/constants';
import { useAbstractSession } from '@/lib/hooks/useAbstractSession';
import { useSounds } from '@/lib/hooks/useSounds';
import { MuteButton } from '../common/MuteButton';
import { useShowUI } from '@/lib/hooks/useShowUI';

// Define preset bets as a file-level constant
const PRESET_BETS = [parseEther('0.001'), parseEther('0.01'), parseEther('0.1')];

export function DeathRaceGame() {
  // ------------------------------------------------ STATE
  const [betAmount, setBetAmount] = useState<bigint>(PRESET_BETS[0]);
  const [demoMode, setDemoMode] = useState(false);
  const rowsContainerRef = useRef<HTMLDivElement>(null);
  const [toastId, setToastId] = useState<string | number>('');
  const [testnetClaimed, setTestnetClaimed] = useState(false);
  const [isCashingOut, setIsCashingOut] = useState(false);
  const showUI = useShowUI(500);

  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { walletAddress, sessionConfig } = userData ?? {};
  const { sendCreateGameTransaction, userBalance, maxBetAmount, isUserBalanceLoading } =
    useGambling(DEATH_RACE_GAME_TYPE);
  const queryClient = useQueryClient();
  const demoGame = useDemoGame();
  const realGame = useGame();
  const gameHook = demoMode ? demoGame : realGame;
  const {
    currentGame,
    previousGame,
    isLoading,
    payoutData,
    processingTileIndex,
    rowConfig,
    isFreshRowConfig,
    isSelectingTile,
    selectTile,
    cashOut,
    shuffleRows,
  } = gameHook;
  const { handleCreateSessionAndUpdateUser, publicClient, isValidSession } = useAbstractSession();
  const { playGameStart, playShuffle } = useSounds();

  // ------------------------------------------------ MEMO
  const gameToUse =
    ['pending_onchain', 'creation_failed'].includes(currentGame?.status ?? '') && previousGame
      ? previousGame
      : currentGame;
  let rows, selectedTiles, currentRowIndex;
  if (gameToUse) {
    if (isFreshRowConfig) {
      rows = rowConfig;
      selectedTiles = undefined;
      currentRowIndex = -1;
    } else {
      rows = gameToUse?.rows;
      selectedTiles = gameToUse?.selectedTiles;
      currentRowIndex = gameToUse?.currentRowIndex;
    }
  } else {
    rows = rowConfig;
    selectedTiles = undefined;
    currentRowIndex = -1;
  }
  const gameId = gameToUse?.id;
  const isGameOver =
    !!gameToUse &&
    !['active', 'pending_onchain', 'cashout_pending', 'cashout_failed'].includes(gameToUse.status);
  const hasWon = gameToUse?.status === 'won';
  const finalMultiplier = gameToUse?.finalMultiplier ?? 1;
  const currentBet =
    gameToUse?.status === 'active'
      ? BigInt(gameToUse.betAmount)
      : demoMode
        ? BigInt(gameToUse?.betAmount ?? 0)
        : BigInt(betAmount);
  const commitmentHash = gameToUse?.commitmentHash;
  const gameSeed = gameToUse?.gameSeed;

  // Calculate payoutAmount as bet * multiplier
  const multipliers = (
    Array.isArray(rows) && rows.length > 0 ? calculateRowMultipliers(rows) : []
  ) as number[];
  const currentMultiplier = isGameOver
    ? finalMultiplier
    : currentRowIndex > 0
      ? multipliers[currentRowIndex - 1]
      : 1;

  const payoutAmount = isGameOver
    ? (BigInt(gameToUse.betAmount) * BigInt(Math.round(currentMultiplier * 10000))) / BigInt(10000)
    : currentBet && currentMultiplier
      ? (currentBet * BigInt(Math.round(currentMultiplier * 10000))) / BigInt(10000)
      : BigInt(0);

  const shouldShowGameStatus =
    !!gameId ||
    payoutData.isPending ||
    currentGame?.status === 'cashout_pending' ||
    currentGame?.status === 'cashout_failed';
  const testnetClaimStatus = userData?.testnetClaimStatus;

  const hasNoFunds =
    IS_MAINNET && walletAddress && !isUserBalanceLoading && userBalance < DEATH_FUN_MIN_BET_ETH;

  const isTestnetClaimEligible =
    !IS_MAINNET &&
    walletAddress &&
    userBalance < TESTNET_CLAIM_THRESHOLD &&
    testnetClaimStatus === false &&
    !isUserBalanceLoading &&
    !testnetClaimed;

  const shouldShowGameControls =
    (!gameId || isGameOver) && !payoutData.isPending && currentGame?.status !== 'cashout_pending';

  // ------------------------------------------------ MUTATIONS
  const { mutate: createGame, isPending: isCreatingGame } = useMutation({
    mutationKey: ['createGame'],
    mutationFn: async () => {
      const localToastId = toast.loading('Preparing game...');
      setToastId(localToastId);

      // Always use the rows currently being displayed
      const rowsToUse = rows;
      const queryParams = new URLSearchParams({ gameType: DEATH_RACE_GAME_TYPE });
      const prepareResponse = await fetch(`/api/abstract/games/create?${queryParams.toString()}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: superjson.stringify({
          betAmount: betAmount,
          rowConfig: rowsToUse.map((row: any) => row.tiles),
        }),
        credentials: 'include',
      });
      if (!prepareResponse.ok) {
        const errorData = await prepareResponse.json();
        throw new Error(
          `Failed to prepare game: ${errorData.error || prepareResponse.statusText}, ${JSON.stringify(
            errorData.details
          )}`
        );
      }
      const { preliminaryGameId } = await prepareResponse.json();
      return { nextGameId: preliminaryGameId };
    },
    onSuccess: async () => {
      toast.success('Game created', { id: toastId });
      playGameStart();
      await queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
      queryClient.setQueryData(getRowConfigOptions().queryKey, (oldData: any) => ({
        ...oldData,
        fresh: false,
      }));
    },
    onError: (error) => {
      console.error('Error creating game:', error);
      toast.error('Error creating game', { id: toastId, description: error.message });
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: getUserOptions().queryKey });
    },
  });

  const isStartDisabled =
    betAmount <= BigInt(0) ||
    isCreatingGame ||
    isLoading ||
    currentGame?.status === 'pending_onchain';

  // ------------------------------------------------ HANDLERS
  const handleBetChange = useCallback((bet: bigint) => {
    setBetAmount(bet);
  }, []);

  // --- WRAPPED SHUFFLE HANDLER ---
  const handleShuffleRows = useCallback(
    (customRows?: any) => {
      playShuffle();
      shuffleRows(customRows); // Shuffle tiles
      // Smooth scroll to bottom after a short delay to allow UI update
      setTimeout(() => {
        if (rowsContainerRef.current) {
          rowsContainerRef.current.scrollTo({
            top: rowsContainerRef.current.scrollHeight,
            behavior: 'smooth',
          });
        }
      }, 50);
    },
    [shuffleRows, rowsContainerRef]
  );

  const handleStartGame = useCallback(async () => {
    if (!walletAddress || !config) {
      toast.error('Wallet not connected.');
      return;
    }
    if (!publicClient) {
      toast.error('public client not initialized');
      return;
    }
    if (betAmount <= 0) {
      toast.error('Bet amount must be greater than 0.');
      return;
    }
    if (
      !sessionConfig ||
      sessionConfig.callPolicies[0].target ===
        process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS ||
      !(await isValidSession(sessionConfig))
    ) {
      try {
        await handleCreateSessionAndUpdateUser();
      } catch (error) {
        console.error('Error creating session:', error);
        toast.error('Error creating session', { id: toastId });
        return;
      }
    }

    createGame();
  }, [walletAddress, betAmount, sendCreateGameTransaction, config, currentGame?.id]);

  const handleTileClick = useCallback(
    async (rowIndex: number, tileIndex: number) => {
      if (rowIndex === currentRowIndex && !isLoading) {
        selectTile(tileIndex);
      }
    },
    [currentRowIndex, isLoading, selectTile]
  );

  const handleCashOut = useCallback(async () => {
    if (payoutData.isPending) return;
    setIsCashingOut(true);
    await cashOut();
  }, [cashOut]);

  // ------------------------------------------------ EFFECTS

  useEffect(() => {
    if (isGameOver) {
      setIsCashingOut(false);
      queryClient.invalidateQueries({
        queryKey: getLeaderboardEntriesOptions(DEATH_RACE_GAME_TYPE).queryKey,
      });
    }
  }, [isGameOver, queryClient]);

  // Refetch active game when walletAddress changes (e.g., after login)
  useEffect(() => {
    if (walletAddress) {
      queryClient.invalidateQueries({
        queryKey: getActiveGameOptions(DEATH_RACE_GAME_TYPE).queryKey,
      });
    }
  }, [walletAddress, queryClient]);

  // Update bet as soon as we have currentGame and balances
  const targetBetRef = useRef(false);
  useEffect(() => {
    // Wait for data to be loaded
    if (!userBalance || targetBetRef.current || !currentGame || !currentGame.betAmount) return;
    targetBetRef.current = true;

    // First try to use the current game's bet amount
    let targetBet: bigint | null = null;

    targetBet = BigInt(currentGame.betAmount);

    // Check if the target bet is affordable and within limits
    if (targetBet && targetBet > 0 && targetBet <= userBalance && targetBet <= maxBetAmount) {
      if (betAmount !== targetBet) {
        setBetAmount(targetBet);
      }
      return;
    }
  }, [currentGame, previousGame, userBalance, maxBetAmount]);

  const createGameRef = useRef(true);
  useEffect(() => {
    if (currentGame?.status === 'pending_onchain') {
      const loadingToast = toast.loading('Preparing game...', {
        id: toastId,
      });
      setToastId(loadingToast);
      createGameRef.current = false;
    }
    if (currentGame?.status === 'active' && !createGameRef.current) {
      createGameRef.current = true;
      toast.success('Game created', { id: toastId });
    }
  }, [currentGame, toastId]);

  useEffect(() => {
    if (currentGame?.status === 'creation_failed' && !isCreatingGame) {
      toast.error('Game creation failed', { id: toastId });
    }
  }, [currentGame, toastId]);

  // ------------------------------------------------ RENDER
  return (
    <div
      className={cn(
        'relative flex flex-col h-full max-h-full overflow-hidden bg-black opacity-0',
        showUI && 'animate-fade-in'
      )}
    >
      <MuteButton className='hidden lg:block absolute bottom-4 right-4 z-50 bg-black/70 rounded-full p-2 hover:bg-black/90 transition-colors' />

      <div className='relative flex-1'>
        <GameBoard
          rowsContainerRef={rowsContainerRef}
          gameId={gameId || ''}
          rows={rows}
          currentRowIndex={currentRowIndex}
          isGameOver={isGameOver}
          isCashingOut={
            isCashingOut ||
            currentGame?.status === 'cashout_pending' ||
            currentGame?.status === 'cashout_failed'
          }
          hasWon={hasWon}
          processingTileIndex={processingTileIndex}
          onTileClick={handleTileClick}
          selectedTiles={selectedTiles ?? undefined}
        />
      </div>

      <div
        className={cn(
          'flex flex-col justify-center space-y-4 shrink-0 border-0 border-t-2 md:border-2 opacity-0 container max-w-full md:max-w-2xl md:mb-6 md:rounded-3xl mx-auto p-4 py-6 md:py-4 transition-all duration-150',
          showUI && 'animate-slide-up',
          isGameOver ? 'h-[17rem] md:h-[15rem]' : 'h-[12rem] md:h-[10.5rem]',
          isGameOver && !hasWon
            ? 'border-red-400/20 bg-red-900/20'
            : 'border-gc-400/20 bg-gc-900/20'
        )}
      >
        {/* // TODO: extract to common component */}
        {shouldShowGameStatus && (
          <GameStatus
            isGameOver={isGameOver}
            hasWon={hasWon}
            currentBet={currentBet}
            finalMultiplier={finalMultiplier}
            onCashOut={handleCashOut}
            commitmentHash={commitmentHash ?? null}
            gameSeed={gameSeed ?? null}
            isPayoutPending={payoutData.isPending || currentGame?.status === 'cashout_pending'}
            isCashingOut={isCashingOut || currentGame?.status === 'cashout_pending'}
            isSelectingTile={isSelectingTile}
            payoutAmount={payoutAmount}
            payoutError={
              payoutData.error || currentGame?.status === 'cashout_failed'
                ? 'Payout failed'
                : undefined
            }
            onRetryPayout={handleCashOut}
            currentRowIndex={currentGame?.currentRowIndex ?? currentRowIndex}
            rows={currentGame?.rows ?? rows}
            updatedAt={currentGame?.updatedAt}
            gameBetAmount={currentGame?.betAmount ? BigInt(currentGame.betAmount) : undefined}
            processingTileIndex={processingTileIndex}
            selectedTiles={selectedTiles ?? undefined}
            gameType={DEATH_RACE_GAME_TYPE}
          />
        )}

        {/* // TODO: extract to common component */}
        {shouldShowGameControls && demoMode && (
          <div className='flex flex-row gap-4 w-full justify-center items-center px-10'>
            <Button size='lg' variant='primary' onClick={() => demoGame.startDemo()}>
              Play Again
            </Button>
            <div className='text-white/80 font-bold text-sm'>or</div>
            <Button size='lg' variant='white' onClick={() => setDemoMode(false)}>
              Exit Demo
            </Button>
          </div>
        )}

        {/* // TODO: extract to common component */}
        {shouldShowGameControls && !demoMode && (
          <>
            {hasNoFunds ? (
              <NoFunds showText={!gameToUse} />
            ) : isTestnetClaimEligible ? (
              <TestnetClaimButton onClaim={() => setTestnetClaimed(true)} />
            ) : (
              <GameControls
                onStartGame={handleStartGame}
                isStartDisabled={isStartDisabled}
                gameCreationStatus={currentGame?.status ?? ''}
                currentBet={currentBet}
                gameType={DEATH_RACE_GAME_TYPE}
                onBetChange={handleBetChange}
                shuffleRows={handleShuffleRows}
                onTryDemo={() => {
                  setDemoMode(true);
                }}
                onShowFaq={() => window.dispatchEvent(new Event('open-faq'))}
                transactionInProgress={isCreatingGame || currentGame?.status === 'pending_onchain'}
              />
            )}
          </>
        )}

        <MuteButton className='lg:hidden absolute bottom-2.5 right-2.5 z-5 !mt-0 p-2 transition-colors' />
      </div>
    </div>
  );
}
