'use client';

import { useRef, useEffect, useState } from 'react';
import { Skull } from '@phosphor-icons/react';
import { formatMultiplier, calculateRowMultipliers } from '@/lib/utils/game';
import { GameRow, DisplayRow } from './types';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useSounds } from '@/lib/hooks/useSounds';
import { TopGradient } from '../common/TopGradient';
import { BottomGradient } from '../common/BottomGradient';
import { SkullExplosion } from '../common/SkullExplosion';

// Separate component for the skull tile
function SkullTile({
  isVisible,
  onClick,
  disabled,
  isProcessing,
  isDeathTile,
  isChosenTile,
  isDeathTileChosen,
  ...rest
}: {
  isVisible: boolean;
  onClick: () => void;
  disabled: boolean;
  isProcessing: boolean;
  isDeathTile: boolean;
  isChosenTile: boolean;
  isDeathTileChosen: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'w-[13.5%] aspect-square rounded bg-gray-800',
        'disabled:cursor-not-allowed',
        'flex items-center justify-center',
        isProcessing && 'animate-shake',
        isDeathTileChosen && 'bg-red-500 hover:bg-red-500',
        isChosenTile && !isDeathTile && 'bg-gc-400 hover:bg-gc-400',
        !isDeathTileChosen && !isChosenTile && 'hover:bg-gray-700'
      )}
      {...rest}
    >
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: isVisible ? 1 : 0, scale: isVisible ? 1 : 0.8 }}
          transition={{
            duration: 0.3,
            delay: 0.2,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
        >
          <Skull className='w-5 h-5 md:w-8 md:h-8' />
        </motion.div>
      )}
    </button>
  );
}

interface GameBoardProps {
  rowsContainerRef: React.RefObject<HTMLDivElement | null>;
  gameId: string;
  rows: DisplayRow[];
  currentRowIndex: number;
  isGameOver: boolean;
  isCashingOut: boolean;
  onTileClick: (rowIndex: number, tileIndex: number) => void;
  processingTileIndex: number | null;
  hasWon: boolean;
  selectedTiles?: number[];
}

export function GameBoard({
  rowsContainerRef,
  gameId,
  rows = [],
  currentRowIndex,
  isGameOver,
  isCashingOut,
  onTileClick,
  processingTileIndex,
  hasWon,
  selectedTiles,
}: GameBoardProps) {
  // ------------------------------------------------ STATE
  const [mounted, setMounted] = useState(false);
  const [visibleSkulls, setVisibleSkulls] = useState<Set<string>>(new Set());

  // --- Skull explosion state ---
  const explosionPlayedRef = useRef<{ [id: string]: boolean }>({});
  const [showExplosion, setShowExplosion] = useState(false);
  const [explosionPos, setExplosionPos] = useState<{ x: number; y: number } | null>(null);
  const deathTileRef = useRef<HTMLButtonElement | null>(null);
  const hasClickedTileRef = useRef(false);

  // Inject chosenTileIndex from selectedTiles if present
  const rowsWithChosen = rows.map((row, index) => {
    if (selectedTiles && typeof selectedTiles[index] === 'number') {
      return { ...row, chosenTileIndex: selectedTiles[index] };
    }
    return row;
  });

  // Calculate multipliers for each row
  const multipliers = calculateRowMultipliers(rowsWithChosen);
  const rowsWithMultipliers = rowsWithChosen.map((row, index) => ({
    ...row,
    multiplier: multipliers[index],
  }));

  // ------------------------------------------------ HOOKS
  const { playDeathTile, playWinTile, reset } = useSounds();

  // ------------------------------------------------ EFFECTS
  // Handle initial mount and row changes
  useEffect(() => {
    // On first mount, scroll to bottom immediately
    if (rowsContainerRef.current) {
      rowsContainerRef.current.scrollTop = rowsContainerRef.current.scrollHeight;
    }
    // Wait for header and controls to animate in (700ms)
    const timer = setTimeout(() => {
      setMounted(true);
    }, 400);
    return () => clearTimeout(timer);
  }, [rowsContainerRef]);

  // Scroll to active row when it changes
  useEffect(() => {
    if (currentRowIndex >= 0 && rowsContainerRef.current) {
      const container = rowsContainerRef.current;
      const rows = container.getElementsByClassName('game-row');
      const currentRow = rows[currentRowIndex];

      if (currentRow) {
        currentRow.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }, [currentRowIndex]);

  // Handle skull visibility
  useEffect(() => {
    const newVisibleSkulls = new Set<string>();

    rows.forEach((row, rowIndex) => {
      if ((isGameOver || rowIndex < currentRowIndex) && row.deathTileIndex !== null) {
        const skullKey = `${gameId}-${rowIndex}-${row.deathTileIndex}`;
        newVisibleSkulls.add(skullKey);
      }
    });

    // Replace the entire set instead of merging
    setVisibleSkulls(newVisibleSkulls);
  }, [rows, currentRowIndex, isGameOver, gameId]);

  // --- Detect death tile selection for explosion ---
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout> | undefined;
    // Only trigger if game is over, not won, and the last chosen tile is the death tile
    if (
      hasClickedTileRef.current &&
      isGameOver &&
      !hasWon &&
      selectedTiles &&
      currentRowIndex < rows.length &&
      rows[currentRowIndex]?.deathTileIndex === selectedTiles[currentRowIndex] &&
      !explosionPlayedRef.current[gameId]
    ) {
      // Get the position of the death tile
      if (deathTileRef.current) {
        const rect = deathTileRef.current.getBoundingClientRect();
        setExplosionPos({ x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 });
      } else {
        setExplosionPos(null);
      }
      explosionPlayedRef.current[gameId] = true;
      setShowExplosion(true);

      // Hide after animation (1800 ms)
      timeoutId = setTimeout(() => setShowExplosion(false), 1800);
      playDeathTile();
    }
    // Reset explosion if gameId changes
    if (!isGameOver) {
      explosionPlayedRef.current[gameId] = false;
      setShowExplosion(false);
      setExplosionPos(null);
    }
    // Cleanup on re-run/unmount
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isGameOver, hasWon, selectedTiles, currentRowIndex, rows, gameId, playDeathTile]);

  // Play win sound after a successful non-death tile selection
  useEffect(() => {
    if (
      hasClickedTileRef.current &&
      !isGameOver &&
      selectedTiles &&
      currentRowIndex > 0 &&
      selectedTiles[currentRowIndex - 1] !== undefined &&
      rows[currentRowIndex - 1]?.deathTileIndex !== undefined &&
      selectedTiles[currentRowIndex - 1] !== rows[currentRowIndex - 1]?.deathTileIndex
    ) {
      playWinTile();
    }
  }, [selectedTiles, currentRowIndex, rows, playWinTile, isGameOver]);

  return (
    <div className='flex-1 relative h-full'>
      <SkullExplosion showExplosion={showExplosion} explosionPos={explosionPos} />

      <TopGradient />

      {/* Scrollable content */}
      <div ref={rowsContainerRef} className='absolute inset-0 overflow-y-auto'>
        <div className='relative max-w-4xl mx-auto h-full'>
          <motion.div
            initial={{ opacity: 0, y: '2rem' }}
            animate={{ 
              opacity: mounted ? 1 : 0, 
              y: mounted ? 0 : '2rem' 
            }}
            transition={{
              duration: 0.5,
              delay: 0.7, // Wait for header/controls
              ease: [0.25, 0.46, 0.45, 0.94]
            }}
            className='flex flex-col-reverse gap-4 py-12 sm:py-24 px-2 md:px-4 min-h-full justify-end'
          >
            {rowsWithMultipliers.map((row, rowIndex) => (
              <motion.div
                key={rowIndex}
                initial={{ opacity: 0, y: '1rem' }}
                animate={{ 
                  opacity: mounted ? 1 : 0, 
                  y: mounted ? 0 : '1rem' 
                }}
                transition={{
                  duration: 0.3,
                  delay: 0.7 + (rowIndex * 0.05), // Staggered animation
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
                className='game-row flex items-center w-full px-2'
              >
                <div className='w-14 md:w-24 text-right shrink-0 pr-2 md:pr-4 text-gray-400 font-bold text-xs md:text-base'>
                  {formatMultiplier(row.multiplier)}
                </div>
                <div
                  className={cn(
                    'flex-1 p-2 md:p-4 rounded-lg bg-gray-900',
                    // Show highlights for any game being displayed (active or completed)
                    gameId &&
                      // Show primary ring for active game
                      ((rowIndex === currentRowIndex && !isGameOver && 'ring-2 ring-gc-500') ||
                        // Show red ring for busted game
                        (isGameOver &&
                          !hasWon &&
                          rowIndex === currentRowIndex &&
                          'ring-2 ring-red-500') ||
                        // Show green ring for won game
                        (isGameOver &&
                          hasWon &&
                          rowIndex === currentRowIndex - 1 &&
                          'ring-2 ring-gc-500'))
                  )}
                >
                  <div className='flex justify-center w-full'>
                    <div className='flex justify-center gap-1.5 md:gap-3 w-full max-w-[36rem]'>
                      <div className='flex justify-center space-x-1 md:space-x-3 w-full'>
                        {Array.from({ length: row.tiles }).map((_, idx) => {
                          const isProcessing =
                            currentRowIndex === rowIndex && processingTileIndex === idx;
                          const showSkull =
                            (isGameOver || rowIndex < currentRowIndex) &&
                            idx === row.deathTileIndex;
                          const skullKey = `${gameId}-${rowIndex}-${row.deathTileIndex}`;
                          // Add ref only to the current busted death tile
                          const isBustedDeathTile =
                            isGameOver &&
                            !hasWon &&
                            rowIndex === currentRowIndex &&
                            idx === row.deathTileIndex &&
                            selectedTiles &&
                            selectedTiles[rowIndex] === idx;
                          return (
                            <SkullTile
                              key={idx}
                              onClick={() => {
                                hasClickedTileRef.current = true;
                                onTileClick(rowIndex, idx);
                              }}
                              disabled={rowIndex !== currentRowIndex || isGameOver || isCashingOut}
                              isVisible={showSkull}
                              isProcessing={isProcessing}
                              isDeathTile={idx === row.deathTileIndex}
                              isChosenTile={idx === row.chosenTileIndex}
                              isDeathTileChosen={
                                idx === row.deathTileIndex && row.chosenTileIndex === idx
                              }
                              {...(isBustedDeathTile ? { ref: deathTileRef } : {})}
                            />
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
                <div className='w-0 md:w-24 shrink-0' /> {/* Balancing padding */}
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      <BottomGradient />

      {/* Commented out shuffle indicator */}
      {/* <div className={cn(
        'absolute inset-x-0 top-0 z-10 flex items-center justify-center py-1 text-xs font-bold text-black transition-all duration-300 bg-gradient-to-r from-gc-300 to-green-400',
        hasShuffled ? 'opacity-100' : 'opacity-0 -translate-y-full'
      )}>
        SHUFFLED
      </div> */}
    </div>
  );
}
