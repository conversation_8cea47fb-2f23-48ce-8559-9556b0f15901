
import { cn } from '@/lib/utils';
import { DEATH_FUN_MIN_BET_ETH } from '@/lib/constants';
import React from 'react';
import { Button } from '../ui/button/Button';

interface StartGameButtonProps {
  isStartDisabled: boolean;
  showCustomInput: boolean;
  parsedInputValue: bigint | null;
  balance: bigint;
  isCurrentBetExceedsMax: boolean;
  transactionInProgress: boolean;
  currentBet: bigint;
  maxBetAmount: bigint | undefined;
  gameCreationStatus: string;
  isCustomInputAboveMax: boolean;
  className?: string;
}

export function StartGameButton({
  isStartDisabled,
  showCustomInput,
  parsedInputValue,
  balance,
  isCurrentBetExceedsMax,
  transactionInProgress,
  currentBet,
  maxBetAmount,
  gameCreationStatus,
  isCustomInputAboveMax,
  className,
}: StartGameButtonProps) {
  return (
    <Button
      type='submit'
      disabled={
        isStartDisabled ||
        (showCustomInput
          ? parsedInputValue === null ||
            parsedInputValue > (balance || BigInt(0)) ||
            isCurrentBetExceedsMax ||
            transactionInProgress ||
            parsedInputValue < DEATH_FUN_MIN_BET_ETH ||
            (maxBetAmount !== undefined && parsedInputValue > maxBetAmount)
          : currentBet > (balance || BigInt(0)) ||
            isCurrentBetExceedsMax ||
            transactionInProgress ||
            currentBet < DEATH_FUN_MIN_BET_ETH)
      }
      size='lg'
      className={cn(
        'font-bold shrink-0 h-11 w-[5.5rem]',
        isCurrentBetExceedsMax
          ? 'bg-red-500 hover:bg-red-600 text-black'
          : (
                showCustomInput
                  ? parsedInputValue !== null &&
                    (parsedInputValue > (balance || BigInt(0)) ||
                      parsedInputValue < DEATH_FUN_MIN_BET_ETH ||
                      (maxBetAmount !== undefined && parsedInputValue > maxBetAmount))
                  : currentBet > (balance || BigInt(0)) || currentBet < DEATH_FUN_MIN_BET_ETH
              )
            ? 'bg-red-500 hover:bg-red-600 text-black'
            : transactionInProgress
              ? 'bg-gray-500 cursor-not-allowed'
              : 'bg-gc-400 hover:bg-gc-500 text-gray-900',
        className
      )}
    >
      {isCurrentBetExceedsMax || isCustomInputAboveMax ? (
        <span className='flex flex-col leading-tight text-xs'>
          <span>Exceeds</span>
          <span>Max Bid</span>
        </span>
      ) : showCustomInput &&
        parsedInputValue !== null &&
        parsedInputValue < DEATH_FUN_MIN_BET_ETH ? (
        <span className='flex flex-col leading-tight text-xs'>
          <span>Below</span>
          <span>Min Bet</span>
        </span>
      ) : (
          showCustomInput
            ? parsedInputValue !== null && parsedInputValue > (balance || BigInt(0))
            : currentBet > (balance || BigInt(0))
        ) ? (
        <span className='flex flex-col leading-tight text-xs'>
          Not enough funds
        </span>
      ) : transactionInProgress ? (
        <span className='flex flex-col leading-tight text-xs'>
          <span>Starting</span>
        </span>
      ) : gameCreationStatus === 'creation_failed' ? (
        'Retry'
      ) : (
        'Start'
      )}
    </Button>
  );
}
