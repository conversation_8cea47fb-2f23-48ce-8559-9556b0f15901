'use client';

import { useEffect } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { REFERRAL_STORAGE_KEY } from '@/lib/client/constants';
import { useSearchParams } from 'next/navigation';

export function ReferralHandler() {
  const params = useSearchParams();

  const [, setReferralCode] = useLocalStorage<string | null>(REFERRAL_STORAGE_KEY, null);

  useEffect(() => {
    const referralCode = params.get('r');

    if (referralCode) {
      // Remove any quotes from the code
      const cleanCode = referralCode.replace(/['"]/g, '');
      console.log('Found referral code in URL:', cleanCode);
      setReferralCode(cleanCode);
    }
  }, [setReferralCode]);

  return null;
}
