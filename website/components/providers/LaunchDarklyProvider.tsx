'use client';

import { useGetUser } from '@/lib/client/queries';
import { IS_DF_PREVIEW_ENV } from '@/lib/constants';
import { usePrivy } from '@privy-io/react-auth';
import { LDProvider, useLDClient } from 'launchdarkly-react-client-sdk';
import { useEffect, useState } from 'react';

interface LaunchDarklyProviderProps {
  children: React.ReactNode;
}

export const LaunchDarklyProvider: React.FC<LaunchDarklyProviderProps> = ({ children }) => {
  return (
    <LDProvider
      clientSideID={
        (IS_DF_PREVIEW_ENV
          ? process.env.NEXT_PUBLIC_LD_CLIENT_ID_PREVIEW
          : process.env.NEXT_PUBLIC_LD_CLIENT_ID) || ''
      }
      context={{
        kind: 'multi',
        setup: {
          key: 'setup-ctx-to-not-throw-error',
          kind: 'setup',
        },
      }}
      reactOptions={{
        useCamelCaseFlagKeys: false,
      }}
      options={{
        eventsUrl:
          typeof window !== 'undefined' ? `${window.location.origin}/ld-events` : undefined,
      }}
    >
      <LaunchDarklyController>{children}</LaunchDarklyController>
    </LDProvider>
  );
};

const LaunchDarklyController: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { authenticated, ready } = usePrivy();
  const { data: userData, isPending } = useGetUser();
  const client = useLDClient();
  const [clientReady, setClientReady] = useState(false);
  const [userIdentified, setUserIdentified] = useState(false);

  // --- wait for privy and launchdarkly to be ready
  useEffect(() => {
    if (ready) {
      client?.waitUntilReady().then(() => {
        setClientReady(true);
      });
    }
  }, [client, ready]);

  // --- wait for identify
  useEffect(() => {
    const cb = async () => {
      if (!authenticated) {
        await client?.identify({
          key: 'anonymous',
          kind: 'user',
        });
        setUserIdentified(true);
      } else if (!isPending && userData) {
        await client?.identify({
          key: userData.id,
          kind: 'user',
          name: userData.username,
          custom: {
            role: userData.role,
          },
        });
        setUserIdentified(true);
      }
    };

    if (clientReady) {
      cb();
    }
  }, [clientReady, userData, client, isPending, authenticated]);

  if (!clientReady || !userIdentified) {
    return null;
  }

  return <>{children}</>;
};
