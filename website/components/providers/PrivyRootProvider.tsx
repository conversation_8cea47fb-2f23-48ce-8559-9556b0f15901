'use client';

import { QueryClient } from '@tanstack/react-query';
import { AbstractPrivyProvider } from '@abstract-foundation/agw-react/privy';
import { DEFAULT_ROW_CONFIG } from '@/lib/constants';
import { abstractClientConfig } from '@/lib/utils/abstract/config';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: async ({ queryKey }) => {
        if (queryKey[0] === 'currentRowConfig') {
          return DEFAULT_ROW_CONFIG;
        }
        return null;
      },
    },
  },
});

export default function PrivyRootProvider({ children }: { children: React.ReactNode }) {
  return (
    <AbstractPrivyProvider
      appId={process.env.NEXT_PUBLIC_PRIVY_APP_ID!}
      chain={abstractClientConfig.chain}
      queryClient={queryClient}
      config={{
        externalWallets: {
          walletConnect: {
            enabled: false,
          },
        },
      }}
    >
      {children}
    </AbstractPrivyProvider>
  );
}
