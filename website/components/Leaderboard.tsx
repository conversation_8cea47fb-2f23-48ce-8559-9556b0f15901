'use client';

import { useEffect, useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useView } from '@/lib/context/ViewContext';
import { useGetUser, useGetLeaderboardEntries } from '@/lib/client/queries';
import { isAdmin } from '@/lib/utils';
import { formatCurrency } from '@/lib/client/wallet';
import { abstractClientConfig as config } from '@/lib/utils/abstract/config';
import { DAILY_LEADERBOARD_ENABLED } from '@/lib/constants';
import type { LeaderboardEntry as ApiLeaderboardEntry } from '@/lib/types/api';
import DailyLeaderboardCountdown from './DailyLeaderboardCountdown';
import { Gamification } from './Gamification';
import { GAME_TYPES } from '@/lib/constants';
import { useShowUI } from '@/lib/hooks/useShowUI';

interface LeaderboardRow extends ApiLeaderboardEntry {}

type TimeFilter = 'daily' | '7d' | '30d' | 'all_time' | 'points';

export function Leaderboard({ gameType }: { gameType: (typeof GAME_TYPES)[number] }) {
  // ------------------------------------------------ HOOKS
  const { data: userData } = useGetUser();
  const { role } = userData ?? {};
  const admin = isAdmin(role);

  // Inline the daily leaderboard enabled logic
  let dailyLeaderboardEnabled = false;
  if (DAILY_LEADERBOARD_ENABLED === true) {
    dailyLeaderboardEnabled = true;
  } else if (DAILY_LEADERBOARD_ENABLED === 'admin') {
    dailyLeaderboardEnabled = admin;
  }

  const router = useRouter();
  const { activeView } = useView();

  // ------------------------------------------------ STATE
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all_time');
  const [mounted, setMounted] = useState(false);
  const [initialTabSet, setInitialTabSet] = useState(false);
  const showUI = useShowUI(100, !initialTabSet);

  // ------------------------------------------------ REACT QUERY
  const {
    data: entries = [],
    isLoading,
    isError,
    error,
  } = useGetLeaderboardEntries(gameType, timeFilter) || {
    data: [],
    isLoading: false,
    isError: false,
    error: undefined,
    refetch: () => {},
  };

  // ------------------------------------------------ EFFECTS

  useEffect(() => {
    // Mark as mounted to prevent hydration errors
    setMounted(true);
  }, []);

  // Separate effect for setting default tab after mount
  useEffect(() => {
    if (mounted) {
      // If we have user data and daily leaderboard is enabled, switch to daily
      if (userData && dailyLeaderboardEnabled) {
        setTimeFilter('daily');
      }
      // Always set initialTabSet to true so the leaderboard shows even for non-logged-in users
      setInitialTabSet(true);
    }
  }, [mounted, userData, dailyLeaderboardEnabled]);

  // Handle row click for admin navigation
  const handleRowClick = (entry: LeaderboardRow) => {
    if (admin && entry.username) {
      router.push(`/account?username=${entry.username}&gameType=${gameType}`);
    }
  };

  // Use formatCurrency from context where appropriate
  const formatDisplayAmount = useCallback(
    (amountLamportsOrWei: number) => {
      if (!config) return '0'; // Config needed for decimals
      // TEMP: formatCurrency expects bigint, API returns number. Convert.
      // TODO: Update API to return bigint string or handle conversion better.
      const amountBigInt = BigInt(Math.round(amountLamportsOrWei));
      return formatCurrency(amountBigInt, true);
    },
    [config, formatCurrency]
  );

  // ------------------------------------------------ RENDER

  // Prevent hydration errors and visible tab jumping by waiting for everything to be ready
  if (!mounted || !initialTabSet) {
    return (
      <Card
        className={cn(
          'flex-col min-h-0 bg-black border-0 rounded-none',
          'hidden lg:flex lg:w-[20rem]',
          activeView === 'leaderboard' && 'flex w-full'
        )}
      ></Card>
    );
  }

  return (
    <Card
      className={cn(
        'flex-col min-h-0 bg-black border-0 border-l-2 border-white/10 rounded-none opacity-0',
        'hidden lg:flex flex-1', // Always hidden on mobile, always shown on lg screens
        activeView === 'leaderboard' && 'flex w-full', // Show on mobile when activeView is leaderboard
        showUI && 'animate-fade-in'
      )}
    >
      {/* Gamification Display */}
      {userData && <Gamification />}

      <CardHeader className='p-2 text-white text-base uppercase font-fancy text-center tracking-wide'>
        Leaderboard
      </CardHeader>
      <CardContent className='p-0 flex-1 min-h-0'>
        <Tabs
          value={timeFilter}
          onValueChange={(value) => setTimeFilter(value as TimeFilter)}
          className='flex flex-col h-full'
        >
          <TabsList className='w-full bg-black border-b border-white/10 rounded-none p-0 h-auto'>
            {dailyLeaderboardEnabled && (
              <TabsTrigger
                value='daily'
                className='flex-1 rounded-none text-xs font-bold uppercase data-[state=active]:bg-gc-400 data-[state=active]:text-black'
              >
                Daily
              </TabsTrigger>
            )}
            <TabsTrigger
              value='all_time'
              className='flex-1 rounded-none text-xs font-bold uppercase data-[state=active]:bg-gc-400 data-[state=active]:text-black'
            >
              All Time
            </TabsTrigger>
            <TabsTrigger
              value='points'
              className='flex-1 rounded-none text-xs font-bold uppercase data-[state=active]:bg-gc-400 data-[state=active]:text-black'
            >
              Death Points
            </TabsTrigger>
          </TabsList>

          <DailyLeaderboardCountdown />

          <TabsContent value={timeFilter} className='p-0 mt-0 overflow-auto'>
            <div className='text-sm'>
              <table className='w-full'>
                <TableBody className='relative' key={timeFilter}>
                  {isLoading ? (
                    // Loading skeleton
                    Array.from({ length: 5 }).map((_, i) => (
                      <TableRow key={i} className='border-white/10'>
                        <TableCell className='pl-4'>
                          <Skeleton className='h-4 w-8' />
                        </TableCell>
                        <TableCell className='pl-4'>
                          <Skeleton className='h-4 w-32' />
                        </TableCell>
                        <TableCell className='text-right pl-4'>
                          <Skeleton className='h-4 w-20 ml-auto' />
                        </TableCell>
                      </TableRow>
                    ))
                  ) : entries.length > 0 ? (
                    <AnimatePresence mode='popLayout' key={timeFilter}>
                      {entries.map((entry: LeaderboardRow) => {
                        const isProfit = entry.profit_loss > 0;
                        const isLoss = entry.profit_loss < 0;
                        const rowBgClass = entry.is_current_user
                          ? cn(
                              'sticky top-0 bottom-0 z-10',
                              'bg-gray-800 hover:bg-gray-800 border-b-0'
                            )
                          : 'border-white/10 hover:bg-white/5';

                        const textClass = entry.is_current_user
                          ? 'text-white font-bold'
                          : 'text-white';

                        return (
                          <motion.tr
                            key={entry.username || entry.rank}
                            layout
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            transition={{
                              type: 'spring',
                              stiffness: 500,
                              damping: 30,
                              mass: 1,
                            }}
                            className={cn(
                              rowBgClass,
                              admin && entry.username && 'cursor-pointer hover:bg-white/10'
                            )}
                            onClick={() => handleRowClick(entry)}
                            title={
                              admin && entry.username
                                ? `View ${entry.username}'s account`
                                : undefined
                            }
                          >
                            <TableCell className={cn('font-medium pl-4 text-xs', textClass)}>
                              {entry.rank}
                            </TableCell>
                            <TableCell
                              className={cn(
                                'truncate min-w-0 shrink flex-1 pl-4 text-xs',
                                textClass
                              )}
                            >
                              {entry.username || 'Unknown'}
                            </TableCell>
                            <TableCell
                              className={cn(
                                'text-right whitespace-nowrap pl-4 text-xs',
                                textClass,
                                isProfit ? 'text-success' : isLoss ? 'text-red-500' : 'text-white'
                              )}
                            >
                              {timeFilter === 'points' ? (
                                (entry.points ?? 0).toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })
                              ) : (
                                <>
                                  {isProfit ? '+' : ''}
                                  {formatDisplayAmount(entry.profit_loss)}
                                </>
                              )}
                            </TableCell>
                          </motion.tr>
                        );
                      })}
                    </AnimatePresence>
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className='text-center text-white/60 pl-4'>
                        {isError
                          ? error instanceof Error
                            ? error.message
                            : 'Failed to fetch leaderboard'
                          : 'No data available'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
