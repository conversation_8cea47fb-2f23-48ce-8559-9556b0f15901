'use client';

/**
 * @deprecated
 */

import { useSyncWagmiConfig } from '@lifi/wallet-management';
import {
  ChainType,
  type ExtendedChain,
  useAvailableChains,
  WidgetWalletConfig,
} from '@lifi/widget';
import type { Provider as SolanaAppKitProvider } from '@reown/appkit-adapter-solana';
import { SolanaAdapter } from '@reown/appkit-adapter-solana';
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi';
import type { AppKitNetwork, ChainNamespace } from '@reown/appkit-common';
import { defineChain, solana } from '@reown/appkit/networks';
import { type AppKit, createAppKit, useAppKit, useAppKitProvider } from '@reown/appkit/react';
import type { Adapter, WalletName } from '@solana/wallet-adapter-base';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { ConnectionProvider, useWallet, WalletProvider } from '@solana/wallet-adapter-react';
import { clusterApiUrl } from '@solana/web3.js';
import type { FC, PropsWithChildren, ReactNode } from 'react';
import { useEffect, useRef } from 'react';
import { Config, WagmiProvider } from 'wagmi';
import 'core-js/actual/structured-clone';

const endpoint = clusterApiUrl(WalletAdapterNetwork.Mainnet);
/**
 * Wallets that implement either of these standards will be available automatically.
 *
 *   - Solana Mobile Stack Mobile Wallet Adapter Protocol
 *     (https://github.com/solana-mobile/mobile-wallet-adapter)
 *   - Solana Wallet Standard
 *     (https://github.com/solana-labs/wallet-standard)
 *
 * If you wish to support a wallet that supports neither of those standards,
 * instantiate its legacy wallet adapter here. Common legacy adapters can be found
 * in the npm package `@solana/wallet-adapter-wallets`.
 */
const wallets: Adapter[] = [];

export const SolanaWalletProvider: FC<PropsWithChildren> = ({ children }) => {
  return (
    <ConnectionProvider endpoint={endpoint}>
      {/* @ts-ignore */}
      <WalletProvider wallets={wallets}>
        <SolanaReownHandler />
        {children}
      </WalletProvider>
    </ConnectionProvider>
  );
};

export const SolanaReownHandler: FC = () => {
  const { walletProvider: solanaProvider } = useAppKitProvider<SolanaAppKitProvider>('solana');
  const { disconnect, select } = useWallet();
  useEffect(() => {
    if (solanaProvider?.name) {
      select(solanaProvider.name as WalletName);
    }
    return () => {
      disconnect();
    };
  }, [disconnect, select, solanaProvider?.name]);
  return null;
};

interface MultichainWalletProvider {
  setWalletConfig: (walletConfig: WidgetWalletConfig) => void;
  children: ReactNode;
}
export const MultichainWalletProvider: FC<PropsWithChildren<MultichainWalletProvider>> = ({
  children,
  setWalletConfig,
}) => {
  const { chains, isLoading } = useAvailableChains();
  const wagmi = useRef<WagmiAdapter | undefined>(undefined);
  const modal = useRef<AppKit | undefined>(undefined);
  const reownProjectId = process.env.NEXT_PUBLIC_REOWN_PROJECT_ID;
  const { wagmiConfig } = wagmi.current ?? {};
  useSyncWagmiConfig(wagmiConfig as Config, [], chains);

  if (!chains?.length || isLoading || !reownProjectId) {
    return null;
  }

  if (reownProjectId && (!wagmi.current || !modal.current)) {
    const networks: [AppKitNetwork, ...AppKitNetwork[]] = [solana];
    const evmChains = chains?.filter((chain) => chain.chainType === ChainType.EVM);
    const evmNetworks = chainToAppKitNetworks(evmChains ?? []);
    networks.push(...evmNetworks);
    const chainImages = getChainImagesConfig(evmChains ?? []);

    const wagmiAdapter = new WagmiAdapter({
      networks: evmNetworks,
      projectId: reownProjectId,
      ssr: false,
    });
    const solanaAdapter = new SolanaAdapter();

    const appKit = createAppKit({
      allowUnsupportedChain: true,
      adapters: [wagmiAdapter, solanaAdapter],
      networks,
      projectId: reownProjectId,
      // metadata,
      chainImages,
      features: {
        socials: false,
        email: false,
      },
      themeMode: 'dark',
    });
    wagmi.current = wagmiAdapter;
    modal.current = appKit;
  }

  return (
    <WagmiProvider config={wagmiConfig as Config} reconnectOnMount={false}>
      <SolanaWalletProvider>
        <MultichainHandler setWalletConfig={setWalletConfig} />
        {children}
      </SolanaWalletProvider>
    </WagmiProvider>
  );
};

interface MultichainHandlerProps {
  setWalletConfig: (walletConfig: WidgetWalletConfig) => void;
}

export const MultichainHandler: FC<MultichainHandlerProps> = ({ setWalletConfig }) => {
  const { open } = useAppKit();
  const initialized = useRef<boolean>(false);
  useEffect(() => {
    if (!open) return;
    if (initialized.current) return;
    initialized.current = true;
    setWalletConfig({
      onConnect: () => {
        open();
      },
    });
  }, [setWalletConfig, open]);

  return null;
};

// ------------------------------------------------------------ APPKIT ------------------------------------------------------------

export type AppKitSupportedChainTypes = Exclude<ChainType, ChainType.MVM>;

export const ChainTypeSpaceMap: Record<AppKitSupportedChainTypes, ChainNamespace> = {
  [ChainType.EVM]: 'eip155',
  [ChainType.UTXO]: 'bip122',
  [ChainType.SVM]: 'solana',
};

export type ChainImages = Record<number, string>;

export const chainToAppKitNetworks = (chains: ExtendedChain[]): AppKitNetwork[] =>
  chains.map((chain) =>
    defineChain({
      id: chain.id,
      blockExplorers: {
        default: {
          name: `${chain.name} explorer`,
          url: chain.metamask.blockExplorerUrls[0],
        },
      },
      name: chain.metamask.chainName,
      rpcUrls: {
        default: {
          http: chain.metamask.rpcUrls,
        },
      },
      nativeCurrency: chain.metamask.nativeCurrency,
      chainNamespace: ChainTypeSpaceMap[chain.chainType as AppKitSupportedChainTypes],
      caipNetworkId: `${ChainTypeSpaceMap[chain.chainType as AppKitSupportedChainTypes]}:${chain.id}`,
      assets: {
        imageId: `${ChainTypeSpaceMap[chain.chainType as AppKitSupportedChainTypes]}:${chain.id}`,
        imageUrl: chain.logoURI!,
      },
    })
  );

export const getChainImagesConfig = (chains: ExtendedChain[]): ChainImages => {
  const chainImages: ChainImages = {};
  for (const chain of chains) {
    chainImages[chain.id] = chain.logoURI || '';
  }
  return chainImages;
};
