'use client';

import { useGetUser } from '@/lib/client/queries';
import type { FormState, WidgetConfig } from '@lifi/widget';
import { ChainType, LiFiWidget, useWidgetEvents, ChainId } from '@lifi/widget';
import { useEffect, useRef } from 'react';
import { abstract } from 'wagmi/chains';
import { createRoot } from 'react-dom/client';
import { useQueryClient } from '@tanstack/react-query';
import { analytics } from '@/lib/client/analytics';
import { useGlobalStore } from '@/lib/store';
import { useUserBalance } from '@/lib/hooks/useUserBalance';
import { formatEther, formatUnits } from 'viem';
import { getBalanceQueryKey } from 'wagmi/query';

interface BridgeProps {}

export const Bridge = ({}: BridgeProps) => {
  // ------------------------------------------------ STATE
  const formRef = useRef<FormState>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const rootRef = useRef<ReturnType<typeof createRoot> | null>(null);
  const bridge = useGlobalStore((state) => state.bridge);
  const toggleModals = useGlobalStore((state) => state.toggleModals);
  const bridgeInProgress = useRef(false);

  // ------------------------------------------------ HOOKS
  const widgetEvents = useWidgetEvents();
  const { data: user } = useGetUser();
  const { walletAddress, username } = user ?? {};
  const queryClient = useQueryClient();
  const { userBalance, isUserBalanceLoading } = useUserBalance();

  // ------------------------------------------------ HANDLERS
  // Handle backdrop click to close modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (
      !bridgeInProgress.current &&
      (e.target === e.currentTarget ||
        (e.target as HTMLElement).id.includes('widget-app-expanded-container-«'))
    ) {
      toggleModals({ bridge: false });
    }
  };
  // ------------------------------------------------ CONFIG
  const config = {
    appearance: 'dark',
    theme: {
      container: {
        border: '1px solid rgb(234, 234, 234)',
        borderRadius: '16px',
        zIndex: 60,
      },
    },
    variant: 'compact',
    subvariantOptions: {
      split: 'bridge',
    },
    toChain: abstract.id,
    toToken: abstract.nativeCurrency.symbol,
    toAddress: { name: username, address: walletAddress, chainType: ChainType.EVM },
    disabledUI: ['toAddress', 'toToken'],
    hiddenUI: ['poweredBy'],
    walletConfig: {
      usePartialWalletManagement: true,
    },
  } as Partial<WidgetConfig>;

  // ------------------------------------------------ EFFECTS
  useEffect(() => {
    widgetEvents.on('routeExecutionStarted', () => {
      bridgeInProgress.current = true;
    });
    widgetEvents.on('routeExecutionFailed', () => {
      bridgeInProgress.current = false;
    });
    widgetEvents.on('routeExecutionCompleted', (event) => {
      // this fires before the execution is fully completed, hence the timeout
      setTimeout(() => {
        bridgeInProgress.current = false;
        queryClient.invalidateQueries({
          queryKey: getBalanceQueryKey({ address: walletAddress as `0x${string}` }),
        });
        analytics.fundsBridged({
          fromChain: ChainId[event.fromChainId],
          fromToken: event.fromToken.name,
          fromAmount: Number(formatUnits(BigInt(event.fromAmount), event.fromToken.decimals)),
          fromAmountUSD: Number(event.fromAmountUSD),
          fromAddress: event.fromAddress,
          toToken: event.toToken.name,
          toAmount: Number(formatUnits(BigInt(event.toAmount), event.toToken.decimals)),
          toAmountUSD: Number(event.toAmountUSD),
          walletBalance:
            Number(formatEther(userBalance?.value || BigInt(0))) +
            Number(formatUnits(BigInt(event.toAmount), event.toToken.decimals)),
        });
      }, 500);
    });
    return () => {
      widgetEvents.off('routeExecutionStarted');
      widgetEvents.off('routeExecutionFailed');
      widgetEvents.off('routeExecutionCompleted');
    };
  }, [widgetEvents]);

  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !bridgeInProgress.current) {
        toggleModals({ bridge: false });
      }
    };
    if (bridge) {
      window.addEventListener('keydown', handleEscapeKey);
    }
    return () => {
      window.removeEventListener('keydown', handleEscapeKey);
    };
  }, [bridge]);

  useEffect(() => {
    if (!bridge) return;
    if (isUserBalanceLoading) return;

    // Create container
    const container = document.createElement('div');
    container.id = 'lifi-widget-root';
    document.body.appendChild(container);
    containerRef.current = container;

    // Create new React root (completely separate from your app's context)
    rootRef.current = createRoot(container);

    // Render widget in the new root (no WagmiProvider context here)
    rootRef.current.render(
      <div className='fixed inset-0 z-50' role='dialog' aria-modal='true' aria-label='Bridge funds'>
        <div
          className='flex min-h-full items-center justify-center bg-black/60 backdrop-blur-md'
          onClick={handleBackdropClick}
        >
          
          <div className='max-md:scale-[85%]'>
            <LiFiWidget
            config={config}
            integrator='death.fun'
            formRef={formRef}
            onClose={() => {
              if (!bridgeInProgress.current) {
                toggleModals({ bridge: false });
              }
            }}
          />
           </div>
        </div>
      </div>
    );

    analytics.addFundsClicked({
      walletBalance: Number(formatEther(userBalance?.value || BigInt(0))),
    });

    return () => {
      // Cleanup
      if (rootRef.current) {
        rootRef.current.unmount();
      }
      if (containerRef.current) {
        document.body.removeChild(containerRef.current);
      }
    };
  }, [bridge, isUserBalanceLoading]);

  return null;
};
