import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button/Button';
import * as phosphorIcons from '@phosphor-icons/react';

interface ChallengeCardProps {
  challenge: {
    id: number;
    title: string;
    description?: string | null;
    link: string;
    type: 'standard' | 'permanent' | 'abstractUpvote';
    points: number;
    icon?: string | null;
    published: boolean;
    gameType?: string | null;
  };
  isEditing: boolean;
  onEdit: () => void;
  onUnpublish: () => void;
  isUpdatingChallenge: boolean;
  children?: React.ReactNode;
}

export function ChallengeCard({
  challenge,
  isEditing,
  onEdit,
  onUnpublish,
  isUpdatingChallenge,
  children,
}: ChallengeCardProps) {
  return (
    <Card className='bg-black/40 border border-gc-900/40'>
      <CardContent className='p-4 flex flex-col gap-2'>
        {isEditing ? (
          children
        ) : (
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2'>
            <div className='flex items-center gap-2 flex-1 overflow-hidden'>
              {(() => {
                const Icon = (challenge.icon && (phosphorIcons as any)[challenge.icon]) || null;
                return Icon ? <Icon size={22} className='text-gc-400 flex-shrink-0' /> : null;
              })()}
              <div>
                <div className='font-bold text-gc-400'>{challenge.title}</div>
                {challenge.description && (
                  <div className='text-xs text-muted-foreground mb-1'>{challenge.description}</div>
                )}
                <div className='text-xs text-muted-foreground truncate mb-1'>{challenge.link}</div>
                <div className='text-xs uppercase'>
                  {challenge.type} | {challenge.points} Points |{' '}
                  {challenge.published ? 'Published' : 'Unpublished'}
                </div>
              </div>
            </div>
            <div className='flex gap-2'>
              <Button size='sm' variant='outline' onClick={onEdit}>
                Edit
              </Button>
              <Button
                size='sm'
                variant='destructive'
                onClick={onUnpublish}
                disabled={isUpdatingChallenge}
              >
                {challenge.published && !isUpdatingChallenge ? 'Unpublish' : 'Publish'}
                {challenge.published && isUpdatingChallenge && 'Unpublishing...'}
                {!challenge.published && isUpdatingChallenge && 'Publishing...'}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
