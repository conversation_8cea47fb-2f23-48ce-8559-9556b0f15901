import { Button } from '@/components/ui/button/Button';
import * as phosphorIcons from '@phosphor-icons/react';
import { TextInput } from '../../../ui/input/TextInput';
import { NumberInput } from '../../../ui/input/NumberInput';
import { SelectInput } from '../../../ui/input/SelectInput';

interface ChallengeFormData {
  id: number;
  title: string;
  description: string;
  link: string;
  type: 'standard' | 'permanent' | 'abstractUpvote';
  points: number;
  icon: string;
  published: boolean;
  gameType: string;
}

interface ChallengeFormProps {
  form: ChallengeFormData;
  setForm: (form: ChallengeFormData | ((prev: ChallengeFormData) => ChallengeFormData)) => void;
  onSubmit: () => void;
  onCancel: () => void;
  isSubmitting: boolean;
  submitLabel?: string;
}

export function ChallengeForm({
  form,
  setForm,
  onSubmit,
  onCancel,
  isSubmitting,
  submitLabel = 'Save',
}: ChallengeFormProps) {
  return (
    <form
      className='grid grid-cols-1 gap-2 items-center'
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}
    >
      <TextInput
        value={form.title}
        onChange={(e) => setForm((f) => ({ ...f, title: e.target.value }))}
        placeholder='Title'
        label='Title'
        required
      />
      <TextInput
        value={form.description}
        onChange={(e) => setForm((f) => ({ ...f, description: e.target.value }))}
        placeholder='Description (optional)'
        label='Description'
      />
      {form.type !== 'abstractUpvote' && (
        <TextInput
          value={form.link}
          onChange={(e) => setForm((f) => ({ ...f, link: e.target.value }))}
          placeholder='Link'
          label='Link'
          required
        />
      )}
      <div className='flex flex-row gap-2'>
        <SelectInput
          value={form.type}
          onChange={(e) => {
            const newType = e.target.value as 'standard' | 'permanent' | 'abstractUpvote';
            setForm((f) => ({
              ...f,
              type: newType,
              // Auto-populate link for abstractUpvote
              link: newType === 'abstractUpvote' ? 'https://portal.abs.xyz/vote' : f.link,
            }));
          }}
          label='Type'
          className='w-full'
        >
          <option value='standard'>Standard</option>
          <option value='permanent'>Permanent</option>
          <option value='abstractUpvote'>Abstract Upvote</option>
        </SelectInput>
        <SelectInput
          value={form.gameType}
          onChange={(e) => setForm((f) => ({ ...f, gameType: e.target.value }))}
          label='Game Type'
          className='w-full'
        >
          <option value=''>All Games</option>
          <option value='death_race'>Death Race</option>
          <option value='laser_party'>Laser Party</option>
        </SelectInput>
      </div>
      <div className='flex flex-row gap-2 items-end'>
        <NumberInput
          value={form.points}
          onChange={(e) => setForm((f) => ({ ...f, points: Number(e.target.value) }))}
          placeholder='Points'
          label='Points'
          required
        />
        <label className='flex flex-col gap-1 flex-1 min-w-[10rem]'>
          <span className='text-xs font-semibold text-gc-400'>Icon (Phosphor name)</span>
          <div className='flex items-center gap-2'>
            <TextInput
              value={form.icon}
              onChange={(e) => setForm((f) => ({ ...f, icon: e.target.value }))}
              placeholder='e.g. ArrowUp'
            />
            {(() => {
              const Icon = (form.icon && (phosphorIcons as any)[form.icon]) || null;
              return Icon && <Icon size={24} className='text-gc-400' />;
            })()}
          </div>
        </label>
      </div>
      <label className='flex items-center gap-2 text-xs'>
        <input
          type='checkbox'
          checked={form.published}
          onChange={(e) => setForm((f) => ({ ...f, published: e.target.checked }))}
          className='accent-gc-400 h-4 w-4'
        />
        Published
      </label>
      <div className='col-span-full flex gap-2 mt-2'>
        <Button type='submit' variant='primary' disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : submitLabel}
        </Button>
        <Button type='button' variant='outline' onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </form>
  );
}
