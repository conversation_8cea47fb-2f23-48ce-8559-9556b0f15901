import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button/Button';
import { useGetAdminChallenges } from '@/lib/client/queries';
import { toast } from 'sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ChallengeCard } from './challenges/challenge-card/ChallengeCard';
import { ChallengeForm } from './challenges/challenge-form/ChallengeForm';

export function ChallengesAdmin() {
  // --------------------------------------------- STATE
  const [editing, setEditing] = useState<null | string | number>(null);
  const [form, setForm] = useState({
    id: 0,
    title: '',
    description: '',
    link: '',
    type: 'standard' as 'standard' | 'permanent' | 'abstractUpvote',
    points: 100,
    icon: '',
    published: true,
    gameType: '',
  });
  // --------------------------------------------- HOOKS
  const queryClient = useQueryClient();
  const { data: challenges = [], isPending: isChallengesLoading } = useGetAdminChallenges();

  // --------------------------------------------- MEMO
  const orderedChallenges = [...challenges].sort((a, b) =>
    a.published === b.published ? 0 : a.published ? -1 : 1
  );

  // --------------------------------------------- MUTATIONS
  const { mutate: createChallenges, isPending: isCreatingChallenges } = useMutation({
    mutationFn: async (
      challenges: Array<{
        title: string;
        description?: string | null;
        points: number;
        published: boolean;
        link?: string;
        type: 'standard' | 'permanent' | 'abstractUpvote';
        icon?: string;
        gameType?: string;
      }>
    ) => {
      const response = await fetch('/api/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(challenges),
      });
      if (!response.ok) {
        throw new Error('Failed to create challenges');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/challenges'], exact: false });
      toast.success('Challenge created successfully');
      setEditing(null);
    },
    onError: (error) => {
      toast.error('Failed to create challenges');
      console.error('Error creating challenges:', error);
      setEditing(null);
    },
  });

  const { mutate: updateChallenge, isPending: isUpdatingChallenge } = useMutation({
    mutationFn: async (challenge: {
      id: number;
      title?: string;
      description?: string | null;
      points?: number;
      published?: boolean;
      link?: string;
      type?: 'standard' | 'permanent' | 'abstractUpvote';
      icon?: string;
      gameType?: string;
    }) => {
      const response = await fetch('/api/challenges', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(challenge),
      });
      if (!response.ok) {
        throw new Error('Failed to update challenge');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/challenges'], exact: false });
      toast.success('Challenge updated successfully');
      setEditing(null);
    },
    onError: (error) => {
      toast.error('Failed to update challenge');
      console.error('Error updating challenge:', error);
      setEditing(null);
    },
  });

  const { mutate: deleteChallenge, isPending: isDeletingChallenge } = useMutation({
    mutationFn: async (challengeId: number) => {
      const response = await fetch('/api/challenges', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ id: challengeId }),
      });
      if (!response.ok) {
        throw new Error('Failed to delete challenge');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/challenges'], exact: false });
      toast.success('Challenge deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete challenge');
      console.error('Error deleting challenge:', error);
    },
  });

  // --------------------------------------------- HANDLERS
  const handleEdit = (id: number) => {
    const c = challenges.find((c) => c.id === id);
    if (c) {
      setForm({
        id: c.id,
        title: c.title,
        description: c.description || '',
        link: c.link,
        type: c.type,
        points: c.points,
        icon: c.icon || '',
        published: c.published,
        gameType: c.gameType || '',
      });
      setEditing(id);
    }
  };

  const handleUnpublish = async (challenge: (typeof challenges)[number]) => {
    if (
      !window.confirm(
        `Are you sure you want to ${challenge.published ? 'unpublish' : 'publish'} this challenge?`
      )
    )
      return;
    updateChallenge({ id: challenge.id, published: !challenge.published });
    if (editing === challenge.id) setEditing(null);
  };

  const handleAdd = () => {
    setForm({
      id: 0,
      title: '',
      description: '',
      link: '',
      type: 'standard',
      points: 100,
      icon: '',
      published: true,
      gameType: '',
    });
    setEditing('new');
  };

  const handleSave = async () => {
    if (editing === 'new') {
      // Create new challenge
      const { id, ...challengeData } = form;
      // Normalize gameType: convert empty string to undefined
      const normalizedData = {
        ...challengeData,
        gameType: challengeData.gameType === '' ? undefined : challengeData.gameType,
      };
      createChallenges([normalizedData]);
    } else {
      // Update existing challenge
      // Normalize gameType: convert empty string to undefined
      const normalizedForm = {
        ...form,
        gameType: form.gameType === '' ? undefined : form.gameType,
      };
      updateChallenge(normalizedForm);
    }
  };

  // --------------------------------------------- RENDER
  if (isChallengesLoading) {
    return (
      <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
        <CardHeader>
          <CardTitle className='text-white'>Manage Challenges</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-center py-8'>Loading challenges...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='mb-6 bg-gc-900/20 text-white border-gc-400/20'>
      <CardHeader>
        <CardTitle className='text-white'>Manage Challenges</CardTitle>
      </CardHeader>
      <CardContent>
        <div>
          <div className='mb-4'>
            <Button variant='primary-outline' onClick={handleAdd}>
              + Add Challenge
            </Button>
          </div>
          <div className='space-y-4'>
            {editing === 'new' && (
              <Card className='bg-black/40 border border-gc-900/40'>
                <CardContent className='p-4 flex flex-col gap-2'>
                  <ChallengeForm
                    form={form}
                    setForm={setForm}
                    onSubmit={handleSave}
                    onCancel={() => setEditing(null)}
                    isSubmitting={isCreatingChallenges}
                    submitLabel="Save"
                  />
                </CardContent>
              </Card>
            )}
            {orderedChallenges.map((c) => (
              <ChallengeCard
                key={c.id}
                challenge={c}
                isEditing={editing === c.id}
                onEdit={() => handleEdit(c.id)}
                onUnpublish={() => handleUnpublish(c)}
                isUpdatingChallenge={isUpdatingChallenge}
              >
                <ChallengeForm
                  form={form}
                  setForm={setForm}
                  onSubmit={handleSave}
                  onCancel={() => setEditing(null)}
                  isSubmitting={isUpdatingChallenge}
                  submitLabel="Save"
                />
              </ChallengeCard>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
