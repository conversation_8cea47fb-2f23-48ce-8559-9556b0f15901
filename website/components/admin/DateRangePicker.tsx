'use client';

import * as React from 'react';
import { Calendar as CalendarIcon } from '@phosphor-icons/react';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button/Button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface DateRangePickerProps {
  dateRange?: DateRange;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
  className?: string;
}

const quickOptions = [
  {
    label: 'All Time',
    getValue: () => undefined,
  },
  {
    label: 'Today',
    getValue: () => ({
      from: startOfDay(new Date()),
      to: endOfDay(new Date()),
    }),
  },
  {
    label: 'Yesterday',
    getValue: () => ({
      from: startOfDay(subDays(new Date(), 1)),
      to: endOfDay(subDays(new Date(), 1)),
    }),
  },
  {
    label: 'Last 7 Days',
    getValue: () => ({
      from: startOfDay(subDays(new Date(), 6)),
      to: endOfDay(new Date()),
    }),
  },
  {
    label: 'Last 30 Days',
    getValue: () => ({
      from: startOfDay(subDays(new Date(), 29)),
      to: endOfDay(new Date()),
    }),
  },
];

export function DateRangePicker({ dateRange, onDateRangeChange, className }: DateRangePickerProps) {
  const getDisplayText = () => {
    if (!dateRange?.from) return 'All Time';

    if (dateRange.to) {
      return `${format(dateRange.from, 'LLL dd, y')} - ${format(dateRange.to, 'LLL dd, y')}`;
    }

    return format(dateRange.from, 'LLL dd, y');
  };

  return (
    <div className={cn('grid gap-2 flex-1 sm:flex-none', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id='date'
            variant={'outline'}
            className={cn(
              'flex-1 sm:w-[300px] justify-start text-left font-normal overflow-hidden'
            )}
          >
            <CalendarIcon className='mr-2 h-4 w-4 shrink-0' />
            <span className='truncate'>{getDisplayText()}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-auto p-0' align='end'>
          <div className='flex'>
            {/* Quick Options */}
            <div className='p-3 border-r shrink-0'>
              <div className='flex flex-col space-y-1 min-w-0'>
                {quickOptions.map((option) => (
                  <Button
                    key={option.label}
                    variant='ghost'
                    size='sm'
                    className='justify-start text-left h-8 whitespace-nowrap px-3 w-auto'
                    onClick={() => onDateRangeChange(option.getValue())}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Calendar */}
            <div>
              <Calendar
                initialFocus
                mode='range'
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={onDateRangeChange}
                numberOfMonths={1}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
