'use client';

import { Button } from '@/components/ui/button/Button';
import { Download } from '@phosphor-icons/react';
import { useState } from 'react';
import { toast } from 'sonner';

interface GameStats {
  wallet_address: string;
  transactions: number;
  total_eth_bet: number;
}

export function AbstractXPExport() {
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/export-abstract-xp', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `abstract-xp-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Export completed successfully');
    } catch (error) {
      toast.error('Failed to export data');
      console.error('Export error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      disabled={isLoading}
      variant='primary'
    >
      <Download className='w-4 h-4 mr-2' />
      {isLoading ? 'Exporting...' : 'Abstract XP Export'}
    </Button>
  );
}
