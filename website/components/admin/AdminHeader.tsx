'use client';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetUser } from '@/lib/client/queries';
import { isSuperAdmin } from '@/lib/utils';
export function AdminHeader() {
  const pathname = usePathname();
  const current = pathname.startsWith('/admin/tools') ? 'tools' : 'stats';
  const { data: userData } = useGetUser();
  const role = userData?.role;

  if (!isSuperAdmin(role)) {
    return null;
  }

  return (
    <div>
      <Tabs value={current} className='w-full max-w-xs'>
        <TabsList className='w-full flex'>
          <TabsTrigger asChild value='stats' className='flex-1'>
            <Link href='/admin/stats' className='w-full'>
              Stats
            </Link>
          </TabsTrigger>
          <TabsTrigger asChild value='tools' className='flex-1'>
            <Link href='/admin/tools' className='w-full'>
              Tools
            </Link>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
