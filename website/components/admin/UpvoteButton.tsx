'use client';

import { Button } from '@/components/ui/button/Button';
import { useAbstractVoting } from '@/lib/hooks/useAbstractVoting';
import { ThumbsUp } from '@phosphor-icons/react';

export function UpvoteButton() {
  const { voteForApp, isVoting, canVote, hasAbstractVoted } = useAbstractVoting();

  const handleUpvote = () => {
    voteForApp();
  };

  return (
    <Button
      onClick={handleUpvote}
      disabled={!canVote || isVoting}
      variant={ hasAbstractVoted ? 'primary' : 'default' }
    >
      <ThumbsUp className='w-4 h-4' />
      {isVoting ? 'Voting...' : hasAbstractVoted ? 'Already Voted ✓' : 'Upvote on Abstract'}
    </Button>
  );
}
