'use client';
import { useEffect, useMemo, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns-tz';
import { DAILY_LEADERBOARD_TOTAL_POINTS, DAILY_LEADERBOARD_NUM_WINNERS } from '@/lib/constants';
import { getNextLeaderboardResetUTC } from '@/lib/utils/daily-leaderboard';
import { Info } from '@phosphor-icons/react';
import * as Tooltip from '@radix-ui/react-tooltip';
import { Button } from './ui/button/Button';

function useNow() {
  const [now, setNow] = useState(() => Date.now());
  useEffect(() => {
    const interval = setInterval(() => setNow(Date.now()), 1000);
    return () => clearInterval(interval);
  }, []);
  return now;
}

function getLocalResetTimeString() {
  const resetUTC = getNextLeaderboardResetUTC();
  // Format in user's local timezone
  return format(resetUTC, 'h:mm a zzz', {
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });
}

export default function DailyLeaderboardCountdown() {
  const queryClient = useQueryClient();
  const now = useNow();

  const { phase, countdown } = useMemo(() => {
    const nowDate = new Date(now);
    const nextReset = getNextLeaderboardResetUTC(nowDate);
    const secondsToReset = Math.max(0, Math.floor((nextReset.getTime() - now) / 1000));
    const lastReset = getNextLeaderboardResetUTC(new Date(now - 24 * 60 * 60 * 1000));
    const secondsSinceLastReset = Math.floor((now - lastReset.getTime()) / 1000);
    if (secondsSinceLastReset >= 0 && secondsSinceLastReset < 60) {
      return {
        phase: 'distributing',
        countdown: {
          hours: 0,
          minutes: 0,
          seconds: 60 - secondsSinceLastReset,
          isZero: false,
          diff: 60 - secondsSinceLastReset,
        },
      };
    }
    return {
      phase: 'countdown',
      countdown: {
        hours: Math.floor(secondsToReset / 3600),
        minutes: Math.floor((secondsToReset % 3600) / 60),
        seconds: secondsToReset % 60,
        isZero: secondsToReset === 0,
        diff: secondsToReset,
      },
    };
  }, [now]);

  useEffect(() => {
    // Make sure we're restting the leaderboard data after 50 seconds of distributing
    if (phase === 'distributing' && countdown.seconds === 50) {
      queryClient.invalidateQueries({ queryKey: ['/users'] });
    }
  }, [phase, countdown.seconds, queryClient]);

  return (
    <Tooltip.Provider>
      <div className='relative flex items-center gap-3 text-gc-400 font-bold py-2 px-4 my-0 shadow-lg'>
        <div className='flex flex-col flex-1'>
          <div className='text-[0.78rem] text-center flex items-center justify-center gap-1'>
            <span className='inline-flex items-center gap-1'>
              <span className='font-bold'>{DAILY_LEADERBOARD_TOTAL_POINTS.toLocaleString()}</span>
            </span>
            <span>DP split among daily top {DAILY_LEADERBOARD_NUM_WINNERS} by rank</span>
          </div>
          <div className='font-extrabold tracking-tight text-center font-mono text-xl tabular-nums'>
            {phase === 'distributing' ? (
              <span className='text-gc-300'>Distributing rewards…</span>
            ) : (
              <>
                {countdown.hours.toString().padStart(2, '0')}:
                {countdown.minutes.toString().padStart(2, '0')}:
                {countdown.seconds.toString().padStart(2, '0')}
              </>
            )}
          </div>
        </div>
        <Tooltip.Root delayDuration={100}>
          <Tooltip.Trigger asChild>
            <Button
              variant='ghost'
              size='none'
              className='absolute bottom-3 right-2'
              aria-label='Daily leaderboard info'
            >
              <Info size={16} className='text-gc-400' />
            </Button>
          </Tooltip.Trigger>
          <Tooltip.Portal>
            <Tooltip.Content
              side='left'
              align='center'
              className='z-50 max-w-xs rounded-md bg-black text-white text-xs px-3 py-2 shadow-lg border border-gc-500 font-normal'
            >
              Every day at {getLocalResetTimeString()},{' '}
              {DAILY_LEADERBOARD_TOTAL_POINTS.toLocaleString()} Death Points are split between the
              top {DAILY_LEADERBOARD_NUM_WINNERS} on the leaderboard and then the leaderboard
              resets. 1st place gets 360 DP, and it goes down from there.
              <Tooltip.Arrow className='fill-black' />
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </div>
    </Tooltip.Provider>
  );
}
