import { useState, useEffect } from 'react';
import { LinkSimple, ArrowRight, CaretDown, CaretUp, ThumbsUp } from '@phosphor-icons/react';
import * as phosphorIcons from '@phosphor-icons/react';
import { Button } from '@/components/ui/button/Button';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { getChallengesOptions, useGetChallenges } from '@/lib/client/queries';
import { useAbstractVoting } from '@/lib/hooks/useAbstractVoting';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import { usePrivy } from '@privy-io/react-auth';
import { useQuery } from '@tanstack/react-query';
import { useShowUI } from '@/lib/hooks/useShowUI';

interface ChallengesProps {
  gameType?: string;
}

export function Challenges({ gameType }: ChallengesProps = {}) {
  // --------------------------------------------- STATE
  const [showCompleted, setShowCompleted] = useState(false);
  const showUI = useShowUI(500);

  // --------------------------------------------- HOOKS
  const queryClient = useQueryClient();
  const { authenticated, login } = usePrivy();
  const { data: challenges = [], isLoading } = useGetChallenges();
  const { voteForApp, isVoting, hasAbstractVoted } = useAbstractVoting(gameType);

  // --------------------------------------------- MEMO
  // Filter challenges based on game type
  const filteredChallenges = challenges.filter((c) => {
    // If challenge has no gameType (null), it shows on all games
    if (!c.gameType) return true;
    // If we have a gameType prop, only show challenges for that game type
    if (gameType) return c.gameType === gameType;
    // If no gameType prop provided, show all challenges
    return true;
  });

  const incompleteChallenges = filteredChallenges.filter((c) => c.status !== 'completed');
  const completedChallenges = filteredChallenges.filter((c) => c.status === 'completed');
  const allChallengesCompleted = filteredChallenges.length > 0 && incompleteChallenges.length === 0;

  // --------------------------------------------- MUTATIONS
  const { mutate: attemptChallenge, isPending: isAttemptingChallenge } = useMutation({
    mutationKey: ['attemptChallenge'],
    mutationFn: async (challengeId: number) => {
      const response = await fetch('/api/challenges/attempt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ id: challengeId }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to attempt challenge');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getChallengesOptions(true).queryKey,
        exact: false,
      });
      queryClient.invalidateQueries({ queryKey: ['/users'] });
    },
    onError: (error) => {
      toast.error(error.message);
      queryClient.invalidateQueries({
        queryKey: getChallengesOptions(true).queryKey,
        exact: false,
      });
    },
  });

  const { mutate: verifyChallenge, isPending: isVerifyingChallenge } = useMutation({
    mutationKey: ['verifyChallenge'],
    mutationFn: async (challengeId: number) => {
      queryClient.setQueryData(getChallengesOptions(true).queryKey, (old: any) => {
        return old.map((c: any) => (c.id === challengeId ? { ...c, status: 'verifying' } : c));
      });

      const response = await fetch('/api/challenges/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ id: challengeId }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to verify challenge');
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success('Nice! Death Points awarded.');
      queryClient.invalidateQueries({
        queryKey: getChallengesOptions(true).queryKey,
        exact: false,
      });
      queryClient.invalidateQueries({ queryKey: ['/users'] });
    },
    onError: (error) => {
      toast.error(error.message);
      queryClient.invalidateQueries({
        queryKey: getChallengesOptions(true).queryKey,
        exact: false,
      });
    },
  });

  // --------------------------------------------- HANDLERS
  const handleVerify = async (challenge: (typeof challenges)[number]) => {
    if (isVerifyingChallenge || isVoting || challenge.status !== 'pending') return;

    // If user is not authenticated, trigger login instead of verifying
    if (!authenticated) {
      login();
      return;
    }

    switch (challenge?.type) {
      case 'standard':
        verifyChallenge(challenge.id);
        return;
      case 'abstractUpvote':
        if (!hasAbstractVoted) {
          voteForApp();
          return;
        }
        verifyChallenge(challenge.id);
        return;
      case 'permanent':
        toast.info('Permanent challenges require manual verification by admins.');
        return;
      default:
        return;
    }
  };

  const handleAttempt = async (challenge: (typeof challenges)[number]) => {
    if (isAttemptingChallenge || isVoting || challenge.status !== 'incomplete') return;

    // If user is not authenticated, trigger login instead of attempting challenge
    if (!authenticated) {
      login();
      return;
    }

    switch (challenge?.type) {
      case 'standard':
        attemptChallenge(challenge.id);
        window.open(challenge.link, '_blank', 'noopener,noreferrer');
        break;
      case 'abstractUpvote':
        attemptChallenge(challenge.id);
        if (hasAbstractVoted) {
          toast.success('Already Voted! Now verify to get your points.');
          return;
        }
        voteForApp();
        break;
      case 'permanent':
        if (challenge.link) {
          window.open(challenge.link, '_blank', 'noopener,noreferrer');
        }
        break;
      default:
        return;
    }
  };

  // --------------------------------------------- EFFECTS

  // Invalidate challenges query when user logs in to get their actual status
  useEffect(() => {
    if (authenticated) {
      queryClient.invalidateQueries({
        queryKey: getChallengesOptions(true).queryKey,
        exact: false,
      });
    }
  }, [authenticated, queryClient]);

  // --------------------------------------------- RENDER
  // Don't render anything if loading or no filtered challenges exist
  if (isLoading || filteredChallenges.length === 0) {
    return null;
  }

  return (
    <>
      <style jsx>{`
        @keyframes subtlePulse {
          0%,
          100% {
            opacity: 1;
          }
          50% {
            opacity: 0.75;
          }
        }
      `}</style>
      <div
        className={cn(
          'w-full flex flex-col p-4 border-t-2 border-l-2 border-border bg-gc-900/20',
          showUI ? 'animate-fade-in' : 'opacity-0'
        )}
      >
        {!allChallengesCompleted && (
          <h2 className='text-gc-400 font-fancy text-center mb-2'>Want more Death Points?</h2>
        )}
        <div className='flex flex-col'>
          <AnimatePresence initial={false}>
            {incompleteChallenges.map((challenge) => {
              const isCompleted = challenge.status === 'completed';
              const isVerifying = challenge.status === 'verifying';
              const showVerifyBtn = challenge.status === 'pending';
              const isPermanent = challenge.type === 'permanent';
              const isAbstractUpvote = challenge.type === 'abstractUpvote';

              // Get icon from challenge data or default based on type
              const Icon = (() => {
                if (challenge.icon && (phosphorIcons as any)[challenge.icon]) {
                  const IconComponent = (phosphorIcons as any)[challenge.icon];
                  return <IconComponent size={20} className='text-gc-400' />;
                }

                if (challenge.type === 'abstractUpvote') {
                  return (
                    <ThumbsUp
                      size={22}
                      className={cn('text-gc-400', !isCompleted && 'drop-shadow-lg')}
                      style={
                        !isCompleted ? { animation: 'subtlePulse 2s ease-in-out infinite' } : {}
                      }
                    />
                  );
                }

                return <LinkSimple size={20} className='text-gc-400' />;
              })();

              return (
                <motion.div
                  key={challenge.id}
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  style={{ overflow: 'hidden' }}
                  transition={{ type: 'spring', stiffness: 500, damping: 40, duration: 0.3 }}
                >
                  <div
                    className={cn(
                      'flex focus:outline-none rounded-md my-1 cursor-pointer',
                      'w-full hover:bg-white/5',
                      isCompleted
                        ? 'bg-muted border border-primary/40 opacity-80'
                        : 'bg-background border border-border',
                      isVerifying && 'opacity-60 pointer-events-none',
                      isAbstractUpvote && isVoting && 'opacity-60 pointer-events-none',
                      // Special styling for abstractUpvote challenges
                      isAbstractUpvote &&
                        !isCompleted &&
                        'bg-gc-500/10 border-gc-400/50 shadow-lg shadow-gc-400/20',
                      isAbstractUpvote && !isCompleted && 'hover:bg-gc-500/20'
                    )}
                    style={{
                      textDecoration: 'none',
                      ...(isAbstractUpvote &&
                        !isCompleted && {
                          animation: 'subtlePulse 2s ease-in-out infinite',
                        }),
                    }}
                    tabIndex={-1}
                    onClick={(e: React.MouseEvent) => {
                      if (challenge.status === 'pending') {
                        handleVerify(challenge);
                      } else {
                        handleAttempt(challenge);
                      }
                    }}
                  >
                    <div className='flex text-sm items-center justify-between h-10 pr-2 w-full'>
                      <span className='shrink-0 flex items-center justify-center w-10'>{Icon}</span>
                      <span className='relative flex-1 min-w-0 truncate text-[0.8rem] font-bold'>
                        <span
                          className={cn(
                            'text-foreground',
                            isCompleted && 'text-muted-foreground',
                            // Special styling for abstractUpvote challenges
                            isAbstractUpvote && !isCompleted && 'text-gc-300 font-extrabold'
                          )}
                          style={{ minWidth: 0 }}
                        >
                          {challenge.title}
                        </span>
                        {isCompleted && (
                          <motion.span
                            layoutId={`strikeout-${challenge.id}`}
                            initial={{ width: 0 }}
                            animate={{ width: '100%' }}
                            transition={{ duration: 0.4, ease: 'easeInOut' }}
                            className='absolute left-0 right-0 top-1/2 h-[2px] bg-gc-400 rounded pointer-events-none'
                            style={{ transform: 'translateY(-50%)' }}
                          />
                        )}
                      </span>
                      {showVerifyBtn && !isCompleted && !isPermanent ? (
                        <Button
                          size='xs'
                          variant='outline'
                          className={cn('ml-4 shrink-0', isVerifying && 'animate-pulse')}
                          disabled={isVerifying || isAttemptingChallenge}
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleVerify(challenge);
                          }}
                        >
                          {isVerifying ? (
                            <span className='inline-block w-4 h-4 border-2 border-gc-400 border-t-transparent border-solid rounded-full animate-spin' />
                          ) : (
                            'Verify'
                          )}
                        </Button>
                      ) : (
                        <>
                          <span
                            className={cn(
                              'ml-4 font-mono font-bold shrink-0 text-xs',
                              isAbstractUpvote && !isCompleted
                                ? 'text-gc-300 text-sm font-extrabold drop-shadow-lg'
                                : 'text-gc-400'
                            )}
                          >
                            +{challenge.points}
                          </span>
                          {!isCompleted && !isAbstractUpvote && (
                            <ArrowRight size={18} className='ml-2 text-muted-foreground shrink-0' />
                          )}
                          {!isCompleted && isAbstractUpvote && (
                            <ArrowRight size={18} className='ml-2 text-muted-foreground shrink-0' />
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>

          {completedChallenges.length > 0 && (
            <div>
              <Button variant='ghost' size='xs' onClick={() => setShowCompleted((v) => !v)}>
                {showCompleted ? 'Hide completed challenges' : 'Show completed challenges'}
                {showCompleted ? <CaretUp size={16} /> : <CaretDown size={16} />}
              </Button>
              <AnimatePresence initial={false}>
                {showCompleted && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ type: 'tween', duration: 0.25 }}
                    className='flex flex-col'
                  >
                    {completedChallenges.map((challenge) => {
                      const Icon = (() => {
                        if (challenge.icon && (phosphorIcons as any)[challenge.icon]) {
                          const IconComponent = (phosphorIcons as any)[challenge.icon];
                          return <IconComponent size={20} className='text-gc-400' />;
                        }

                        return <LinkSimple size={20} className='text-gc-400' />;
                      })();

                      return (
                        <motion.div
                          key={challenge.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 20 }}
                          transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 40,
                            duration: 0.3,
                          }}
                        >
                          <div className='flex items-center gap-3 min-w-0 group flex-1 pointer-events-none text-sm justify-between rounded-md h-10 pr-2 w-full bg-muted border border-primary/40 opacity-80 my-1'>
                            <span className='shrink-0 flex items-center justify-center w-10'>
                              {Icon}
                            </span>
                            <span
                              className='truncate text-muted-foreground flex-1 line-through'
                              style={{ minWidth: 0 }}
                            >
                              {challenge.title}
                            </span>
                            <span className='ml-4 font-mono text-gc-400 font-bold shrink-0'>
                              +{challenge.points}
                            </span>
                          </div>
                        </motion.div>
                      );
                    })}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
