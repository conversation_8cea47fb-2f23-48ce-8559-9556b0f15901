import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Plus, Minus, Copy, CaretUp, CaretDown } from '@phosphor-icons/react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { formatEther } from 'viem';
import { formatWithDecimals } from '@/lib/client/wallet';
import { VerificationTooltip } from '@/components/death-race/VerificationTooltip';
import { GameWithOptionalUsername } from '@/lib/types/game';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Icon } from './ui/icons/Icon';
import { But<PERSON> } from './ui/button/Button';

interface GameHistoryProps {
  games: GameWithOptionalUsername[];
  totalPages: number;
  currentPage: number;
  loading: boolean;
  onPageChange: (page: number) => void;
  showUsername?: boolean;
  sortBy?: 'created_at' | 'profit' | 'final_multiplier';
  sortDirection?: 'asc' | 'desc';
  onSortChange?: (column: 'created_at' | 'profit' | 'final_multiplier') => void;
  pageSize?: number;
}

function TableSkeleton({ rows = 15, cols = 10 }: { rows?: number; cols?: number }) {
  return (
    <>
      {Array.from({ length: rows }).map((_, i) => (
        <TableRow key={i}>
          {Array.from({ length: cols }).map((_, j) => (
            <TableCell key={j}>
              <Skeleton className='h-6 w-20' />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );
}

// Helper for sort arrow
function SortArrows({
  active,
  direction,
}: {
  active: boolean;
  direction: 'asc' | 'desc' | undefined;
}) {
  return (
    <span className='inline-flex flex-col ml-1'>
      <CaretUp
        size={14}
        className={
          'transition ' + (active && direction === 'asc' ? 'text-gc-400' : 'text-white/50')
        }
      />
      <CaretDown
        size={14}
        className={
          'transition -mt-1 ' + (active && direction === 'desc' ? 'text-gc-400' : 'text-white/50')
        }
      />
    </span>
  );
}

export function GameHistory({
  games,
  totalPages,
  currentPage,
  loading,
  onPageChange,
  showUsername = false,
  sortBy,
  sortDirection,
  onSortChange,
  pageSize = 10,
}: GameHistoryProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const wasGameWon = (game: GameWithOptionalUsername) => {
    return (
      game.status === 'won' ||
      game.status === 'payout_pending' ||
      (game.finalMultiplier !== null && game.finalMultiplier > 0)
    );
  };

  const getCompletedRows = (game: GameWithOptionalUsername) => {
    return game.currentRowIndex;
  };

  // Determine number of columns for skeleton
  const skeletonCols = showUsername ? 10 : 11;
  const rowsToRender = games.length < pageSize ? pageSize : games.length;

  return (
    <Card>
      <CardHeader className='border-b border-gc-400/20 py-3'>
        <CardTitle className='text-white'>Game History</CardTitle>
      </CardHeader>
      <CardContent className='p-0'>
        <div className='overflow-x-auto'>
          <Table>
            <TableHeader>
              <TableRow className='border-gc-400/20'>
                <TableHead className='text-white whitespace-nowrap px-5'>ID</TableHead>
                {showUsername && (
                  <TableHead className='text-white whitespace-nowrap px-5'>User</TableHead>
                )}
                <TableHead className='text-white whitespace-nowrap px-5 pl-6'>
                  {onSortChange ? (
                   <Button
                   variant='ghost'
                   size='none'
                   className='flex gap-0 group focus:outline-none'
                      onClick={() => onSortChange('created_at')}
                      aria-sort={
                        sortBy === 'created_at'
                          ? sortDirection === 'asc'
                            ? 'ascending'
                            : 'descending'
                          : 'none'
                      }
                    >
                      Date
                      <SortArrows active={sortBy === 'created_at'} direction={sortDirection} />
                    </Button>
                  ) : (
                    'Date'
                  )}
                </TableHead>
                <TableHead className='text-white whitespace-nowrap px-5'>Status</TableHead>
                <TableHead className='text-white whitespace-nowrap px-5'>Bet</TableHead>
                <TableHead className='text-white whitespace-nowrap px-5'>
                  {onSortChange ? (
                    <Button
                      variant='ghost'
                      size='none'
                      className='flex gap-0 group'
                      onClick={() => onSortChange('profit')}
                      aria-sort={
                        sortBy === 'profit'
                          ? sortDirection === 'asc'
                            ? 'ascending'
                            : 'descending'
                          : 'none'
                      }
                    >
                      Result
                      <SortArrows active={sortBy === 'profit'} direction={sortDirection} />
                    </Button>
                  ) : (
                    'Result'
                  )}
                </TableHead>
                <TableHead className='text-white whitespace-nowrap px-5'>
                  {onSortChange ? (
                    <Button
                      variant='ghost'
                      size='none'
                      className='flex gap-0 group'
                      onClick={() => onSortChange('final_multiplier')}
                      aria-sort={
                        sortBy === 'final_multiplier'
                          ? sortDirection === 'asc'
                            ? 'ascending'
                            : 'descending'
                          : 'none'
                      }
                    >
                      Multiplier
                      <SortArrows
                        active={sortBy === 'final_multiplier'}
                        direction={sortDirection}
                      />
                    </Button>
                  ) : (
                    'Multiplier'
                  )}
                </TableHead>
                <TableHead className='text-white w-full text-left px-5'>Rows</TableHead>
                <TableHead className='text-white whitespace-nowrap text-center px-5'>
                  Payout Tx
                </TableHead>
                {!showUsername && (
                  <>
                    <TableHead className='text-white whitespace-nowrap text-center px-5'>
                      Hash
                    </TableHead>
                    <TableHead className='text-white whitespace-nowrap text-center px-5'>
                      Seed
                    </TableHead>
                  </>
                )}
                <TableHead className='text-white whitespace-nowrap px-5 pr-6'>Verify</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading && games.length === 0 ? (
                <TableSkeleton rows={pageSize} cols={skeletonCols} />
              ) : (
                Array.from({ length: rowsToRender }).map((_, i) => {
                  const game = games[i];
                  if (game) {
                    const multiplier = game.finalMultiplier ?? 0;
                    const betAmount = BigInt(game.betAmount);
                    const scaledMultiplier = BigInt(Math.floor(multiplier * 1000));
                    const winAmount = (betAmount * scaledMultiplier) / BigInt(1000);
                    const profit = winAmount - betAmount;
                    const gameWon = wasGameWon(game);
                    const completedRows = getCompletedRows(game);

                    // Format date to "time ago" and full date for tooltip
                    const createdAt = new Date(game.createdAt);
                    const now = new Date();
                    const diffMs = now.getTime() - createdAt.getTime();
                    const diffSec = Math.floor(diffMs / 1000);
                    const diffMin = Math.floor(diffSec / 60);
                    const diffHour = Math.floor(diffMin / 60);
                    const diffDay = Math.floor(diffHour / 24);
                    const diffMonth = Math.floor(diffDay / 30);
                    const diffYear = Math.floor(diffMonth / 12);

                    let timeAgo;
                    if (diffSec < 60) timeAgo = `${diffSec}s ago`;
                    else if (diffMin < 60) timeAgo = `${diffMin}m ago`;
                    else if (diffHour < 24) timeAgo = `${diffHour}h ago`;
                    else if (diffDay < 30) timeAgo = `${diffDay}d ago`;
                    else if (diffMonth < 12) timeAgo = `${diffMonth}M ago`;
                    else timeAgo = `${diffYear}y ago`;

                    const fullDate = createdAt.toLocaleString();

                    // Game ID column (onchain_game_id)
                    const gameIdCell = (
                      <TableCell className='font-mono text-xs text-white whitespace-nowrap px-5 w-0'>
                        <div className='flex items-center'>
                          {game.onchainGameId || '-'}
                          {/* Copy support link button */}
                          {game.onchainGameId && (
                            <Button
                              variant='ghost'
                              size='none'
                              className='hover:text-gc-400 ml-2 '
                              onClick={() =>
                                copyToClipboard(
                                  `${window.location.origin}/stats?gameType=${game.gameType}&username=${encodeURIComponent(String(game.username ?? ''))}&gameId=${encodeURIComponent(String(game.onchainGameId))}`
                                )
                              }
                              aria-label='Copy support link'
                            >
                              <Copy className='w-4 h-4 opacity-50' />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    );

                    return (
                      <TableRow key={game.id} className='border-gc-400/20'>
                        {gameIdCell}
                        {showUsername && (
                          <TableCell className='font-medium text-white whitespace-nowrap px-5'>
                            {game.username ? (
                              <Link
                                href={`/stats?username=${encodeURIComponent(game.username)}`}
                                className='underline text-gc-400 hover:text-gc-300 transition-colors'
                              >
                                {game.username}
                              </Link>
                            ) : (
                              <span className='text-white/50 italic'>Unknown</span>
                            )}
                          </TableCell>
                        )}
                        <TableCell className='font-medium text-white whitespace-nowrap px-5 pl-6'>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>{timeAgo}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{fullDate}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className='text-white whitespace-nowrap px-5'>
                          {(() => {
                            const status = game.status;
                            const formatStatus = (status: string) => {
                              return status
                                .replace(/_/g, ' ')
                                .split(' ')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(' ');
                            };

                            switch (status) {
                              case 'won':
                                return <span className='text-gc-400 font-medium'>Won</span>;
                              case 'lost':
                                return <span className='text-red-500 font-medium'>Lost</span>;
                              case 'active':
                                return <span className='text-white font-medium'>Active</span>;
                              default:
                                return (
                                  <span className='text-gray-400 font-medium'>
                                    {formatStatus(status)}
                                  </span>
                                );
                            }
                          })()}
                        </TableCell>
                        <TableCell className='text-white whitespace-nowrap px-5 flex items-center'>
                          <Icon id="eth" className='w-3.5 h-3.5 mr-1' />
                          {formatEther(BigInt(betAmount)) + ' ETH'}
                        </TableCell>
                        <TableCell className='whitespace-nowrap px-5'>
                          {gameWon ? (
                            <span className='text-success flex items-center'>
                              <Plus className='w-4 h-4 mr-1' />
                              <Icon id="eth" className='w-3.5 h-3.5 inline align-middle mr-1' />
                              {formatWithDecimals(profit)}
                            </span>
                          ) : (
                            <span className='text-red-500 flex items-center'>
                              <Minus className='w-4 h-4 mr-1' />
                              <Icon id="eth" className='w-3.5 h-3.5 inline align-middle mr-1' />
                              {formatWithDecimals(BigInt(betAmount))}
                            </span>
                          )}
                        </TableCell>
                        <TableCell className='text-white whitespace-nowrap px-5'>
                          {game.finalMultiplier ? `${game.finalMultiplier.toFixed(2)}x` : '0x'}
                        </TableCell>
                        <TableCell className='text-white text-left w-full px-5'>
                          {completedRows}
                        </TableCell>
                        <TableCell className='text-center whitespace-nowrap px-5 text-gc-400'>
                          {(() => {
                            const signature = game.payoutTxSignature || game.refundTxSignature;
                            if (!signature) return '-';
                            const baseUrl =
                              process.env.NEXT_PUBLIC_ABSTRACT_EXPLORER_URL ||
                              'https://explorer.abstract.money';
                            const explorerUrl = `${baseUrl}/tx/${signature}`;
                            return (
                              <Button
                                variant='link'
                                size='none'
                                to={explorerUrl}
                              >
                                View Tx
                              </Button>
                            );
                          })()}
                        </TableCell>
                        {!showUsername && (
                          <>
                            <TableCell className='text-center whitespace-nowrap px-5'>
                              {game.commitmentHash ? (
                                <Button
                                variant='ghost'
                                size='none'
                                className='hover:text-gc-400'
                                  onClick={() => copyToClipboard(game.commitmentHash!)}
                                >
                                  <Copy className='w-4 h-4 opacity-50' />
                                </Button>
                              ) : (
                                <span className='text-white/30'>-</span>
                              )}
                            </TableCell>
                            <TableCell className='text-center whitespace-nowrap px-5'>
                              {game.gameSeed ? (
                                <Button
                                variant='ghost'
                                size='none'
                                className='hover:text-gc-400'
                                  onClick={() => copyToClipboard(game.gameSeed!)}
                                >
                                  <Copy className='w-4 h-4 opacity-50' />
                                </Button>
                              ) : (
                                <span className='text-white/30'>-</span>
                              )}
                            </TableCell>
                          </>
                        )}
                        <TableCell className='whitespace-nowrap px-5 pr-6'>
                          {game.gameSeed ? (
                            <VerificationTooltip
                              commitmentHash={game.commitmentHash}
                              gameSeed={game.gameSeed}
                              rows={game.rows || []}
                              selectedTiles={game.selectedTiles || []}
                              gameType={game.gameType}
                            />
                          ) : (
                            <span className='text-white/30'>-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  } else {
                    // Render a skeleton row for empty slots
                    return (
                      <TableRow key={`skeleton-${i}`}>
                        {Array.from({ length: skeletonCols }).map((_, j) => (
                          <TableCell key={j}>
                            <Skeleton className='h-6 w-20' />
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                  }
                })
              )}
            </TableBody>
          </Table>
          {/* Loading overlay if loading and previous data exists */}
          {loading && games.length > 0 && (
            <div className='absolute inset-0 bg-background/80 flex items-center justify-center z-10 pointer-events-none'>
              <span className='text-white/80 text-lg'>Loading…</span>
            </div>
          )}
        </div>
        {totalPages > 1 && (
          <div className='flex justify-center p-4 border-t border-gc-400/20'>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                    className={`${currentPage <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'} text-white`}
                  />
                </PaginationItem>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  return (
                    <PaginationItem key={i}>
                      <PaginationLink
                        onClick={() => onPageChange(pageNum)}
                        isActive={currentPage === pageNum}
                        className={`cursor-pointer ${currentPage === pageNum ? 'bg-gc-500 text-black' : 'text-white'}`}
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
                    className={`${currentPage >= totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'} text-white`}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
