import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Button } from './ui/button/Button';

export default function DistributeDailyRewardsButton() {
  const queryClient = useQueryClient();

  const { mutate: distributeDailyRewards, isPending } = useMutation({
    mutationKey: ['distributeDailyRewards'],
    mutationFn: async () => {
      const response = await fetch('/api/cron/daily-leaderboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to distribute daily rewards');
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success('Daily rewards distributed successfully!');
      // Invalidate leaderboard queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['/api/leaderboard'] });
      queryClient.invalidateQueries({ queryKey: ['/users'] });
    },
    onError: (error) => {
      console.error('Error distributing daily rewards:', error);
      toast.error('Failed to distribute daily rewards', {
        description: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    },
  });

  const handleClick = () => {
    if (
      !window.confirm(
        'Are you sure you want to manually distribute daily rewards and reset the leaderboard? This action is usually performed automatically by the system.'
      )
    ) {
      return;
    }
    distributeDailyRewards();
  };

  return (
    <Button
      variant='primary-outline'
      onClick={handleClick}
      disabled={isPending}
      className='z-10'
    >
      {isPending ? 'Running...' : 'Distribute Daily Rewards'}
    </Button>
  );
}
