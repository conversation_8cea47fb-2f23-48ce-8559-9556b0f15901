import { Database } from '@/lib/types/database';
import { createClient } from '@supabase/supabase-js';
import { Abi, createPublicClient, Hash, http } from 'viem';
import { abstract } from 'viem/chains';
import DeathRaceGameAbiData from '@/lib/blockchain/abi/DeathRaceGame.json';

// --- clients
const supabaseServerClient = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false,
    },
  }
);
const rpcUrl = process.env.ABSTRACT_RPC_URL!;
const publicClient = createPublicClient({
  chain: abstract,
  transport: http(rpcUrl),
});
const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS!;
const apiKey = process.env.ABSCAN_API_KEY!;

const getLogs = async (onChainGameId: bigint, gameCreatedAt: Date) => {
  const createdAt = Math.floor(gameCreatedAt.getTime() / 1000) - 20; // 20 seconds before
  const url = `https://api.abscan.org/api?module=block&action=getblocknobytime&timestamp=${createdAt}&apikey=${apiKey}&closest=before`;
  try {
    const response = await fetch(url);
    const data = (await response.json()) as { status: string; messsage: string; result: string };
    if (!data) {
      console.error(`no data found for game ${onChainGameId}`);
      return null;
    }
    const blockNumber = data.result;

    const logs = await publicClient.getLogs({
      address: contractAddress as Hash,
      event: {
        anonymous: false,
        inputs: [
          { indexed: true, internalType: 'uint256', name: 'onChainGameId', type: 'uint256' },
          { indexed: false, internalType: 'uint256', name: 'amount', type: 'uint256' },
          { indexed: true, internalType: 'address', name: 'recipient', type: 'address' },
        ],
        name: 'PayoutSent',
        type: 'event',
      },
      args: { onChainGameId },
      fromBlock: BigInt(blockNumber),
      toBlock: BigInt(blockNumber) + BigInt(2000),
    });
    if (logs.length > 0) {
      // Return the first (should only be one per game)
      return logs[0].transactionHash as Hash;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching logs for game ${onChainGameId}: ${error}`);
    return null;
  }
};

const main = async () => {
  const dryRun = true; // set to false to run the script
  let done = false;
  let page = 1;
  let offset = 0;
  let totalRecordsProcessed = 0;

  while (!done) {
    const gamesCashedOutButActive: {
      onChainGameId: string;
      game: Database['public']['Tables']['games']['Row'];
      blockchainGame: any;
    }[] = [];
    const { data: games, error } = await supabaseServerClient
      .from('games')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: true })
      .range(offset, offset + 1000)
      .limit(1000);
    if (error) {
      console.error('Database error fetching games:', error);
      done = true;
      break;
    }
    if (!games || games.length === 0) {
      done = true;
    }

    console.log(`Processing page ${page} games count: ${games.length}`);

    const onChainGameIds = await publicClient.multicall({
      contracts: games.map((game) => ({
        address: contractAddress as `0x${string}`,
        abi: DeathRaceGameAbiData.abi as Abi,
        functionName: 'preliminaryToOnChainId',
        args: [game.id],
      })),
    });

    const onChainGameId2Game = games.reduce(
      (acc, game, idx) => {
        acc[(onChainGameIds[idx].result as bigint).toString()] = game;
        return acc;
      },
      {} as Record<string, (typeof games)[number]>
    );

    const blockchainGames = await publicClient.multicall({
      contracts: onChainGameIds.map((data) => ({
        address: contractAddress as `0x${string}`,
        abi: DeathRaceGameAbiData.abi as Abi,
        functionName: 'getGameDetails',
        args: [data.result as bigint],
      })),
    });

    const onchainGameId2BlockchainGame = blockchainGames.reduce(
      (acc, game, idx) => {
        acc[(onChainGameIds[idx].result as bigint).toString()] = game;
        return acc;
      },
      {} as Record<string, (typeof blockchainGames)[number]>
    );

    blockchainGames.forEach((game, idx) => {
      const onChainGameId = (onChainGameIds[idx].result as bigint).toString();
      if (game.status === 'success' && (game.result as any).status === 1) {
        gamesCashedOutButActive.push({
          onChainGameId,
          game: onChainGameId2Game[onChainGameId],
          blockchainGame: game,
        });
      }
    });

    if (!dryRun) {
      for (const game of gamesCashedOutButActive) {
        console.log(`processing game db id ${game.game.id} onchain game id ${game.onChainGameId}`);
        const txHash = await getLogs(BigInt(game.onChainGameId), new Date(game.game.created_at));
        if (!txHash) {
          console.error(
            `No txHash found for game ${game.game.id} onchain game id ${game.onChainGameId}`
          );
          continue;
        }
        console.log(`txHash: ${txHash}`);
        const { error: updateError } = await supabaseServerClient
          .from('games')
          .update({ status: 'won', payout_tx_signature: txHash })
          .eq('id', game.game.id);
        if (updateError) {
          console.error(`Error updating game ${game.game.id}: ${updateError.message}`);
        }
      }
    }
    totalRecordsProcessed += gamesCashedOutButActive.length;

    page++;
    offset += 1000;
  }

  console.log(`Games cashed out but active: ${totalRecordsProcessed}`);
};

main();
