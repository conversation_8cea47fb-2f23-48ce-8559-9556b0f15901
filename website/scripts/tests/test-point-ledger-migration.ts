#!/usr/bin/env tsx

import { supabaseScriptClient } from '../../lib/server/supabase/script-client';

const supabase = supabaseScriptClient;

// Test data
const TEST_WALLET = '******************************************';
const TEST_WALLET_2 = '******************************************';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: any;
}

const results: TestResult[] = [];

async function pause(seconds: number = 3): Promise<void> {
  console.log(`⏸️  Pausing for ${seconds} seconds to monitor database...`);
  await new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}

async function runTest(name: string, testFn: () => Promise<void>): Promise<void> {
  try {
    console.log(`🧪 Running: ${name}`);
    await testFn();
    results.push({ name, passed: true });
    console.log(`✅ ${name}`);
    await pause(2); // Pause after each test
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    results.push({ name, passed: false, error: errorMsg });
    console.log(`❌ ${name}: ${errorMsg}`);
    await pause(3); // Longer pause on errors
  }
}

async function setupTestUsers(): Promise<{ userId1: string; userId2: string }> {
  // Clean up any existing test users thoroughly
  await cleanupTestUsers();

  // Generate unique referral codes for this test run
  const timestamp = Date.now().toString().slice(-6);
  const ref1 = `TEST${timestamp}1`;
  const ref2 = `TEST${timestamp}2`;

  // Create test users with zero points initially
  const { data: user1, error: error1 } = await supabase
    .from('users')
    .insert({
      wallet_address: TEST_WALLET,
      referral_code: ref1,
      points: 0, // Start with 0 points
      multiplier: 1.5,
      streak: 25,
    })
    .select('id')
    .single();

  const { data: user2, error: error2 } = await supabase
    .from('users')
    .insert({
      wallet_address: TEST_WALLET_2,
      referral_code: ref2,
      points: 0, // Start with 0 points
      multiplier: 1.0,
      streak: 1,
    })
    .select('id')
    .single();

  if (error1 || error2 || !user1 || !user2) {
    throw new Error(`Failed to create test users: ${error1?.message || error2?.message}`);
  }

  // Now add initial points through the ledger system to ensure proper tracking
  await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: user1.id,
    p_points_to_add: 100,
    p_source: 'initial_balance',
    p_context: { reason: 'test_setup' },
  });

  await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: user2.id,
    p_points_to_add: 50,
    p_source: 'initial_balance',
    p_context: { reason: 'test_setup' },
  });

  console.log(`✅ Initial points awarded through ledger system`);

  return { userId1: user1.id, userId2: user2.id };
}

async function cleanupTestUsers(): Promise<void> {
  console.log('🧹 Cleaning up test data...');

  // Get test user IDs first
  const { data: testUsers } = await supabase
    .from('users')
    .select('id')
    .in('wallet_address', [TEST_WALLET, TEST_WALLET_2]);

  if (testUsers && testUsers.length > 0) {
    const userIds = testUsers.map((u) => u.id);

    // Delete points_ledger entries first (due to foreign key constraints)
    const { error: ledgerError } = await supabase
      .from('points_ledger')
      .delete()
      .in('user_id', userIds);

    if (ledgerError) {
      console.log(`⚠️  Failed to delete points_ledger entries: ${ledgerError.message}`);
    } else {
      console.log(`✅ Deleted points_ledger entries for ${userIds.length} test users`);
    }

    // Delete challenge submissions if any
    const { error: challengeError } = await supabase
      .from('challenge_submissions')
      .delete()
      .in('user_id', userIds);

    if (challengeError) {
      console.log(`⚠️  Failed to delete challenge submissions: ${challengeError.message}`);
    }

    // Now delete the users
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .in('wallet_address', [TEST_WALLET, TEST_WALLET_2]);

    if (userError) {
      console.log(`⚠️  Failed to delete test users: ${userError.message}`);
    } else {
      console.log(`✅ Deleted ${testUsers.length} test users`);
    }
  } else {
    console.log('ℹ️  No test users found to clean up');
  }

  // Also clean up any test challenges
  const { error: testChallengeError } = await supabase
    .from('challenges')
    .delete()
    .eq('title', 'Test Challenge');

  if (testChallengeError) {
    console.log(`⚠️  Failed to delete test challenges: ${testChallengeError.message}`);
  }
}

async function testAddPointsToUserAndLog(userId: string): Promise<void> {
  // Get initial points before the test
  const { data: initialUser, error: initialError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId)
    .single();

  if (initialError) throw new Error(`Failed to get initial points: ${initialError.message}`);
  const initialPoints = initialUser.points;

  // Test basic point addition
  const { data, error } = await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: userId,
    p_points_to_add: 25.5,
    p_source: 'test_award',
    p_context: { test: true, amount: 25.5 },
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Verify the points were incremented correctly
  const expectedTotal = initialPoints + 25.5;
  if (data !== expectedTotal) {
    throw new Error(`Expected ${initialPoints} + 25.5 = ${expectedTotal}, but got ${data}`);
  }

  // Double-check database state matches
  const { data: finalUser, error: finalError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId)
    .single();

  if (finalError) throw new Error(`Failed to get final points: ${finalError.message}`);
  if (finalUser.points !== expectedTotal) {
    throw new Error(`Database shows ${finalUser.points} but expected ${expectedTotal}`);
  }

  console.log(
    `  💡 Points incremented: ${initialPoints} + 25.5 = ${data}. Check points_ledger table...`
  );
  await pause(3);

  // Verify ledger entries were created
  const { data: ledgerEntries, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('*')
    .eq('user_id', userId)
    .eq('source', 'test_award');

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (!ledgerEntries || ledgerEntries.length !== 1) {
    throw new Error(`Expected 1 ledger entry, got ${ledgerEntries?.length || 0}`);
  }

  // Verify total entry was created
  const { data: totalEntry, error: totalError } = await supabase
    .from('points_ledger')
    .select('*')
    .eq('user_id', userId)
    .eq('source', 'test_award:total');

  if (totalError) throw new Error(`Total entry query failed: ${totalError.message}`);
  if (!totalEntry || totalEntry.length !== 1) {
    throw new Error(`Expected 1 total entry, got ${totalEntry?.length || 0}`);
  }
  if (totalEntry[0].points !== 25.5) {
    throw new Error(`Expected total 25.50, got ${totalEntry[0].points}`);
  }
}

async function testAddPointsZeroAmount(userId: string): Promise<void> {
  // Test zero points addition
  const { data, error } = await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: userId,
    p_points_to_add: 0,
    p_source: 'zero_test',
    p_context: { test: true },
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Should return current points without changing anything
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId)
    .single();

  if (userError) throw new Error(`User query failed: ${userError.message}`);
  if (data !== user.points) throw new Error(`Expected ${user.points}, got ${data}`);

  // Verify no ledger entries were created
  const { data: ledgerEntries, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('*')
    .eq('user_id', userId)
    .eq('source', 'zero_test');

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (ledgerEntries && ledgerEntries.length > 0) {
    throw new Error(`Expected 0 ledger entries for zero amount, got ${ledgerEntries.length}`);
  }
}

async function testIncrementUserPointsWithMultiplier(userId: string): Promise<void> {
  // Test multiplier application by user_id
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: userId,
    p_base_points: 10.0,
    p_source: 'gameplay',
    p_context: { game_type: 'death_race', bet_amount: '0.1' },
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Should be 10.00 * 1.50 = 15.00, added to existing points
  // Get current points to calculate expected total dynamically
  const { data: currentUser, error: userError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId)
    .single();

  if (userError) throw new Error(`User query failed: ${userError.message}`);
  const expectedTotal = currentUser.points;
  if (data !== expectedTotal) throw new Error(`Expected ${expectedTotal}, got ${data}`);

  console.log(
    `  💡 Multiplier applied: 10.0 × 1.5 = 15.0 → Total: ${data}. Check context in points_ledger...`
  );
  await pause(3);

  // Verify context includes multiplier info
  const { data: ledgerEntry, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('context')
    .eq('user_id', userId)
    .eq('source', 'gameplay')
    .order('created_at', { ascending: false })
    .limit(1);

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (!ledgerEntry || ledgerEntry.length === 0) {
    throw new Error('No ledger entry found');
  }

  const context = ledgerEntry[0].context as any;
  if (context.streak_multiplier !== 1.5) {
    throw new Error(`Expected multiplier 1.50, got ${context.streak_multiplier}`);
  }
  if (context.base_points !== 10.0) {
    throw new Error(`Expected base_points 10.00, got ${context.base_points}`);
  }
}

async function testIncrementUserPointsByWallet(): Promise<void> {
  // Get initial points for TEST_WALLET_2 user
  const { data: initialUser, error: initialError } = await supabase
    .from('users')
    .select('points, multiplier')
    .eq('wallet_address', TEST_WALLET_2)
    .single();

  if (initialError) throw new Error(`Failed to get initial points: ${initialError.message}`);
  const initialPoints = initialUser.points;
  const multiplier = initialUser.multiplier;

  // Test multiplier application by wallet address
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_wallet_address: TEST_WALLET_2,
    p_base_points: 20.0,
    p_source: 'gameplay',
    p_context: { game_type: 'laser_party' },
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Calculate expected: initial + (base_points * multiplier)
  const expectedIncrement = 20.0 * multiplier;
  const expectedTotal = initialPoints + expectedIncrement;

  if (data !== expectedTotal) {
    throw new Error(
      `Expected ${initialPoints} + (20.0 × ${multiplier}) = ${expectedTotal}, got ${data}`
    );
  }
}

async function testNullUserHandling(): Promise<void> {
  // Test non-existent user_id
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: '00000000-0000-0000-0000-000000000000',
    p_base_points: 10.0,
    p_source: 'test',
    p_context: {},
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);
  if (data !== 0) throw new Error(`Expected 0 for non-existent user, got ${data}`);

  // Test non-existent wallet address
  const { data: data2, error: error2 } = await supabase.rpc(
    'increment_user_points_with_multiplier',
    {
      p_wallet_address: '0xnonexistent',
      p_base_points: 10.0,
      p_source: 'test',
      p_context: {},
    }
  );

  if (error2) throw new Error(`RPC failed: ${error2.message}`);
  if (data2 !== 0) throw new Error(`Expected 0 for non-existent wallet, got ${data2}`);
}

async function testNullMultiplierHandling(): Promise<void> {
  // Test the NOT NULL constraint first
  const testUserId = crypto.randomUUID();
  const timestamp = Date.now().toString().slice(-6);

  // Try to insert user without multiplier (this should fail with NOT NULL constraint)
  const { error: insertError } = await supabase.from('users').insert({
    id: testUserId,
    wallet_address: '0xnullmultiplier' + timestamp,
    referral_code: 'NULLTEST' + timestamp,
    points: 100,
    // Intentionally omitting multiplier to test if constraint works
  });

  if (insertError) {
    // This would happen if we had a strict NOT NULL without DEFAULT
    console.log('  ✓ NOT NULL constraint blocked NULL multiplier');
    if (!insertError.message.includes('null value') && !insertError.message.includes('not-null')) {
      throw new Error(`Expected NOT NULL constraint error, got: ${insertError.message}`);
    }
  } else {
    // Expected behavior - DEFAULT 1.00 prevents NULL multipliers at schema level
    console.log('  ✓ Schema DEFAULT value prevents NULL multipliers');

    // Verify the user got the default multiplier
    const { data: createdUser, error: userError } = await supabase
      .from('users')
      .select('multiplier')
      .eq('id', testUserId)
      .single();

    if (userError) throw new Error(`User query failed: ${userError.message}`);
    if (createdUser.multiplier !== 1.0) {
      throw new Error(`Expected default multiplier 1.00, got ${createdUser.multiplier}`);
    }

    // Clean up the test user
    await supabase.from('users').delete().eq('id', testUserId);

    console.log('  ✓ DEFAULT multiplier value is working correctly');
  }

  // Also test our function's protection against non-existent users
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: '00000000-0000-0000-0000-000000000000',
    p_base_points: 10.0,
    p_source: 'null_test',
    p_context: {},
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);
  if (data !== 0) throw new Error(`Expected 0 for non-existent user, got ${data}`);

  console.log('  ✓ Function protection against non-existent users is working');
}

async function testNegativePointsHandling(userId: string): Promise<void> {
  // Test negative base points (should result in 0)
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: userId,
    p_base_points: -10.0,
    p_source: 'negative_test',
    p_context: { test: true },
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Should not change points (function should convert negative to 0)
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId)
    .single();

  if (userError) throw new Error(`User query failed: ${userError.message}`);
  if (data !== user.points) throw new Error(`Points should not change for negative input`);
}

async function testProcessChallenge(): Promise<void> {
  // Create a test challenge
  const { data: challenge, error: challengeError } = await supabase
    .from('challenges')
    .insert({
      title: 'Test Challenge',
      description: 'A test challenge',
      points: 50,
      type: 'test',
      link: 'https://test.com',
      published: true,
    })
    .select('id')
    .single();

  if (challengeError || !challenge) {
    throw new Error(`Failed to create challenge: ${challengeError?.message}`);
  }

  // Use existing test users instead of creating new ones
  const { data: users } = await supabase
    .from('users')
    .select('id')
    .in('wallet_address', [TEST_WALLET, TEST_WALLET_2])
    .limit(1);

  if (!users || users.length === 0) {
    throw new Error('No test users found for challenge test');
  }

  const userId1 = users[0].id;

  // Create a challenge submission
  await supabase.from('challenge_submissions').insert({
    challenge_id: challenge.id,
    user_id: userId1,
    status: 'pending',
  });

  // Process the challenge
  const { error: processError } = await supabase.rpc('process_challenge', {
    challenge_id: challenge.id,
    user_id: userId1,
  });

  if (processError) throw new Error(`Process challenge failed: ${processError.message}`);

  // Verify submission status updated
  const { data: submission, error: submissionError } = await supabase
    .from('challenge_submissions')
    .select('status')
    .eq('challenge_id', challenge.id)
    .eq('user_id', userId1)
    .single();

  if (submissionError) throw new Error(`Submission query failed: ${submissionError.message}`);
  if (submission.status !== 'completed') {
    throw new Error(`Expected status 'completed', got '${submission.status}'`);
  }

  // Verify points were awarded
  const { data: ledgerEntry, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('points, source, context')
    .eq('user_id', userId1)
    .eq('source', 'challenge');

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (!ledgerEntry || ledgerEntry.length === 0) {
    throw new Error('No challenge ledger entry found');
  }
  if (ledgerEntry[0].points !== 50) {
    throw new Error(`Expected 50 challenge points, got ${ledgerEntry[0].points}`);
  }

  // Clean up
  await supabase.from('challenges').delete().eq('id', challenge.id);
}

async function testProcessChallengeNoSubmission(): Promise<void> {
  // Create a test challenge
  const { data: challenge, error: challengeError } = await supabase
    .from('challenges')
    .insert({
      title: 'Test Challenge No Submission',
      description: 'A test challenge without submission',
      points: 75,
      type: 'test',
      link: 'https://test.com',
      published: true,
    })
    .select('id')
    .single();

  if (challengeError || !challenge) {
    throw new Error(`Failed to create challenge: ${challengeError?.message}`);
  }

  // Use existing test users
  const { data: users } = await supabase
    .from('users')
    .select('id, points')
    .in('wallet_address', [TEST_WALLET, TEST_WALLET_2])
    .limit(1);

  if (!users || users.length === 0) {
    throw new Error('No test users found for challenge test');
  }

  const userId1 = users[0].id;
  const initialPoints = users[0].points;

  // DON'T create a challenge submission - this is the test case

  // Try to process the challenge (should fail gracefully)
  const { error: processError } = await supabase.rpc('process_challenge', {
    challenge_id: challenge.id,
    user_id: userId1,
  });

  // The function should not throw an error, but should handle this gracefully
  if (processError) throw new Error(`Process challenge failed: ${processError.message}`);

  console.log(
    `  💡 Attempted to process challenge without submission. Should see NOTICE in logs about no submission found.`
  );
  await pause(3);

  // Verify no points were awarded
  const { data: finalUser, error: finalUserError } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId1)
    .single();

  if (finalUserError) throw new Error(`Final user query failed: ${finalUserError.message}`);
  if (finalUser.points !== initialPoints) {
    throw new Error(
      `Points should not have changed: expected ${initialPoints}, got ${finalUser.points}`
    );
  }

  // Verify no ledger entry was created for this challenge
  const { data: ledgerEntries, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('*')
    .eq('user_id', userId1)
    .eq('source', 'challenge')
    .order('created_at', { ascending: false })
    .limit(1);

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);

  // If there are ledger entries, check that none are from this specific challenge
  if (ledgerEntries && ledgerEntries.length > 0) {
    const context = ledgerEntries[0].context as any;
    if (context && context.challenge_id === challenge.id) {
      throw new Error('Ledger entry was incorrectly created for challenge without submission');
    }
  }

  // Verify no challenge submission exists
  const { data: submission, error: submissionError } = await supabase
    .from('challenge_submissions')
    .select('*')
    .eq('challenge_id', challenge.id)
    .eq('user_id', userId1);

  if (submissionError) throw new Error(`Submission query failed: ${submissionError.message}`);
  if (submission && submission.length > 0) {
    throw new Error('No submission should exist for this test');
  }

  // Clean up
  await supabase.from('challenges').delete().eq('id', challenge.id);
}

async function testDistributeAndResetDailyLeaderboard(): Promise<void> {
  // Use existing test users instead of creating new ones
  const { data: users } = await supabase
    .from('users')
    .select('id, points')
    .in('wallet_address', [TEST_WALLET, TEST_WALLET_2])
    .order('wallet_address');

  if (!users || users.length !== 2) {
    throw new Error('Expected 2 test users for distribution test');
  }

  const userId1 = users[0].id;
  const userId2 = users[1].id;
  const initialPoints1 = users[0].points;
  const initialPoints2 = users[1].points;

  // Test with user_ids array
  const { error } = await supabase.rpc('distribute_and_reset_daily_leaderboard', {
    p_user_ids: [userId1, userId2],
    points_to_add: [100, 50],
    p_game_type: 'death_race',
  });

  if (error) throw new Error(`Distribution failed: ${error.message}`);

  console.log(`  💡 Daily leaderboard points distributed. Check users table and points_ledger...`);
  await pause(4);

  // Verify points were awarded
  const { data: user1, error: user1Error } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId1)
    .single();

  const { data: user2, error: user2Error } = await supabase
    .from('users')
    .select('points')
    .eq('id', userId2)
    .single();

  if (user1Error || user2Error) {
    throw new Error(`User query failed: ${user1Error?.message || user2Error?.message}`);
  }

  const expectedPoints1 = initialPoints1 + 100;
  const expectedPoints2 = initialPoints2 + 50;

  if (user1.points !== expectedPoints1)
    throw new Error(`Expected user1 to have ${expectedPoints1} points, got ${user1.points}`);
  if (user2.points !== expectedPoints2)
    throw new Error(`Expected user2 to have ${expectedPoints2} points, got ${user2.points}`);

  // Verify ledger entries
  const { data: ledgerEntries, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('*')
    .eq('source', 'daily_leaderboard')
    .in('user_id', [userId1, userId2]);

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (!ledgerEntries || ledgerEntries.length !== 2) {
    throw new Error(`Expected 2 ledger entries, got ${ledgerEntries?.length || 0}`);
  }
}

async function testLegacyWalletDistribution(): Promise<void> {
  // Use existing test users instead of creating new ones
  const { data: users } = await supabase
    .from('users')
    .select('id')
    .in('wallet_address', [TEST_WALLET, TEST_WALLET_2])
    .order('wallet_address');

  if (!users || users.length !== 2) {
    throw new Error('Expected 2 test users for legacy distribution test');
  }

  const userId1 = users[0].id;
  const userId2 = users[1].id;

  // Test with wallet addresses array (legacy version)
  const { error } = await supabase.rpc('distribute_and_reset_daily_leaderboard', {
    wallet_addresses: [TEST_WALLET, TEST_WALLET_2],
    points_to_add: [25, 15],
    p_game_type: 'laser_party',
  });

  if (error) throw new Error(`Legacy distribution failed: ${error.message}`);

  // Verify ledger entries were created with correct context
  const { data: ledgerEntries, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('context')
    .eq('source', 'daily_leaderboard')
    .in('user_id', [userId1, userId2])
    .order('created_at', { ascending: false })
    .limit(2);

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);
  if (!ledgerEntries || ledgerEntries.length !== 2) {
    throw new Error(`Expected 2 ledger entries, got ${ledgerEntries?.length || 0}`);
  }

  // Check context contains game_type
  const contexts = ledgerEntries.map((entry) => entry.context as any);
  if (!contexts.every((ctx) => ctx.game_type === 'laser_party')) {
    throw new Error('Context should contain correct game_type');
  }
}

async function testTotalRecordUpdates(userId: string): Promise<void> {
  // Award points from same source multiple times to test :total record updates
  console.log(`  🔄 Awarding points from 'repeat_test' source multiple times...`);

  await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: userId,
    p_points_to_add: 10,
    p_source: 'repeat_test',
    p_context: { iteration: 1, description: 'first award' },
  });

  await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: userId,
    p_points_to_add: 15,
    p_source: 'repeat_test',
    p_context: { iteration: 2, description: 'second award' },
  });

  // Add a third award to really test the accumulation
  await supabase.rpc('add_points_to_user_and_log', {
    p_user_id: userId,
    p_points_to_add: 5,
    p_source: 'repeat_test',
    p_context: { iteration: 3, description: 'third award' },
  });

  console.log(
    `  💡 Added points 3 times (10 + 15 + 5 = 30) from same source. Check individual vs total records...`
  );
  console.log(
    `  🔍 Query to check records: SELECT * FROM points_ledger WHERE user_id = '${userId}' AND source LIKE 'repeat_test%' ORDER BY created_at;`
  );
  await pause(5);

  // Check that total record was updated correctly
  const { data: totalRecords, error } = await supabase
    .from('points_ledger')
    .select('points, created_at')
    .eq('user_id', userId)
    .eq('source', 'repeat_test:total');

  if (error) throw new Error(`Total record query failed: ${error.message}`);
  if (!totalRecords || totalRecords.length === 0) {
    throw new Error('No total record found');
  }
  if (totalRecords.length > 1) {
    throw new Error(
      `Expected 1 total record, got ${totalRecords.length} (unique constraint failed?)`
    );
  }

  const totalPoints = totalRecords[0].points;
  if (totalPoints !== 30) {
    throw new Error(`Expected total 30 (10+15+5), got ${totalPoints}`);
  }

  // Check that we have 3 individual transaction records
  const { data: individualRecords, error: individualError } = await supabase
    .from('points_ledger')
    .select('points, context, created_at')
    .eq('user_id', userId)
    .eq('source', 'repeat_test')
    .order('created_at', { ascending: true });

  if (individualError)
    throw new Error(`Individual records query failed: ${individualError.message}`);
  if (!individualRecords || individualRecords.length !== 3) {
    throw new Error(`Expected 3 individual records, got ${individualRecords?.length || 0}`);
  }

  // Verify individual record values
  const expectedValues = [10, 15, 5];
  individualRecords.forEach((record, index) => {
    if (record.points !== expectedValues[index]) {
      throw new Error(
        `Individual record ${index + 1}: expected ${expectedValues[index]}, got ${record.points}`
      );
    }
  });

  // Verify the sum of individual records equals the total
  const sumIndividual = individualRecords.reduce((sum, record) => sum + record.points, 0);
  if (sumIndividual !== totalPoints) {
    throw new Error(
      `Sum of individual records (${sumIndividual}) doesn't match total record (${totalPoints})`
    );
  }

  console.log(
    `  ✅ Total record correctly shows ${totalPoints} points from 3 individual transactions`
  );
}

async function testJsonbArrayContextHandling(userId: string): Promise<void> {
  // Test passing an array as context to the multiplier function
  const arrayContext = ['item1', 'item2', 'item3'];

  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: userId,
    p_base_points: 5.0,
    p_source: 'jsonb_array_test',
    p_context: arrayContext,
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Verify the function executed successfully
  console.log(`  💡 Array context handled: ${JSON.stringify(arrayContext)} → Points: ${data}`);

  // Verify the context was wrapped correctly in the ledger
  const { data: ledgerEntry, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('context')
    .eq('user_id', userId)
    .eq('source', 'jsonb_array_test')
    .single();

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);

  const context = ledgerEntry.context as any;
  if (!context.original_context) {
    throw new Error('Expected original_context to be wrapped in object');
  }

  if (JSON.stringify(context.original_context) !== JSON.stringify(arrayContext)) {
    throw new Error(
      `Original context mismatch: expected ${JSON.stringify(arrayContext)}, got ${JSON.stringify(context.original_context)}`
    );
  }

  if (!context.streak_multiplier || !context.base_points) {
    throw new Error('Expected streak_multiplier and base_points to be added to context');
  }
}

async function testJsonbNullContextHandling(userId: string): Promise<void> {
  // Test passing null as context to the multiplier function
  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: userId,
    p_base_points: 3.0,
    p_source: 'jsonb_null_test',
    p_context: null,
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Verify the function executed successfully
  console.log(`  💡 Null context handled → Points: ${data}`);

  // Verify the context was handled correctly in the ledger
  const { data: ledgerEntry, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('context')
    .eq('user_id', userId)
    .eq('source', 'jsonb_null_test')
    .single();

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);

  const context = ledgerEntry.context as any;
  if (!context.streak_multiplier || !context.base_points) {
    throw new Error('Expected streak_multiplier and base_points to be added to context');
  }

  console.log('context', context);
  // When null was passed, it should be wrapped in original_context
  if (context.original_context !== null) {
    throw new Error(`Expected original_context to be null, got ${context.original_context}`);
  }

  // Should contain streak_multiplier, base_points, and original_context
  const expectedKeys = ['streak_multiplier', 'base_points', 'original_context'];
  const actualKeys = Object.keys(context);
  if (
    actualKeys.length !== expectedKeys.length ||
    !expectedKeys.every((key) => actualKeys.includes(key))
  ) {
    throw new Error(
      `Expected context to contain ${expectedKeys.join(', ')}, got ${actualKeys.join(', ')}`
    );
  }
}

async function testJsonbPrimitiveContextHandling(userId: string): Promise<void> {
  // Test passing primitive values as context to the multiplier function
  const primitiveContext = 'simple_string';

  const { data, error } = await supabase.rpc('increment_user_points_with_multiplier', {
    p_user_id: userId,
    p_base_points: 2.0,
    p_source: 'jsonb_primitive_test',
    p_context: primitiveContext,
  });

  if (error) throw new Error(`RPC failed: ${error.message}`);

  // Verify the function executed successfully
  console.log(`  💡 Primitive context handled: "${primitiveContext}" → Points: ${data}`);

  // Verify the context was wrapped correctly in the ledger
  const { data: ledgerEntry, error: ledgerError } = await supabase
    .from('points_ledger')
    .select('context')
    .eq('user_id', userId)
    .eq('source', 'jsonb_primitive_test')
    .single();

  if (ledgerError) throw new Error(`Ledger query failed: ${ledgerError.message}`);

  const context = ledgerEntry.context as any;
  if (context.original_context !== primitiveContext) {
    throw new Error(
      `Expected original_context to be "${primitiveContext}", got "${context.original_context}"`
    );
  }

  if (!context.streak_multiplier || !context.base_points) {
    throw new Error('Expected streak_multiplier and base_points to be added to context');
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting Point Ledger Migration Tests\n');

  let userId1: string, userId2: string;

  try {
    // Setup
    console.log('📋 Setting up test users...');
    const users = await setupTestUsers();
    userId1 = users.userId1;
    userId2 = users.userId2;
    console.log(`✅ Created test users:`);
    console.log(`   User 1: ${userId1} (${TEST_WALLET}) - 100 points, 1.5x multiplier`);
    console.log(`   User 2: ${userId2} (${TEST_WALLET_2}) - 50 points, 1.0x multiplier`);

    console.log('\n🔍 Database monitoring tips:');
    console.log(
      "   • SELECT * FROM users WHERE wallet_address LIKE '0x123%' OR wallet_address LIKE '0x987%';"
    );
    console.log(
      "   • SELECT * FROM points_ledger WHERE user_id IN ('" +
        userId1 +
        "', '" +
        userId2 +
        "') ORDER BY created_at DESC;"
    );
    console.log(
      "   • SELECT source, SUM(points) as total FROM points_ledger WHERE source LIKE '%:total' GROUP BY source;\n"
    );

    await pause(5);

    // Run all tests
    await runTest('Add Points to User and Log', () => testAddPointsToUserAndLog(userId1));
    await runTest('Add Zero Points', () => testAddPointsZeroAmount(userId1));
    await runTest('Increment Points with Multiplier (UUID)', () =>
      testIncrementUserPointsWithMultiplier(userId1)
    );
    await runTest('Increment Points with Multiplier (Wallet)', () =>
      testIncrementUserPointsByWallet()
    );
    await runTest('NULL User Handling', () => testNullUserHandling());
    await runTest('NULL Multiplier Protection', () => testNullMultiplierHandling());
    await runTest('Negative Points Handling', () => testNegativePointsHandling(userId1));
    await runTest('Process Challenge', () => testProcessChallenge());
    await runTest('Process Challenge (No Submission)', () => testProcessChallengeNoSubmission());
    await runTest('Distribute Daily Leaderboard (User IDs)', () =>
      testDistributeAndResetDailyLeaderboard()
    );
    await runTest('Distribute Daily Leaderboard (Wallets)', () => testLegacyWalletDistribution());
    await runTest('Total Record Updates', () => testTotalRecordUpdates(userId1));
    await runTest('JSONB Array Context Handling', () => testJsonbArrayContextHandling(userId1));
    await runTest('JSONB Null Context Handling', () => testJsonbNullContextHandling(userId1));
    await runTest('JSONB Primitive Context Handling', () =>
      testJsonbPrimitiveContextHandling(userId1)
    );
  } finally {
    // Final cleanup
    console.log('\n🔄 Final cleanup...');
    await cleanupTestUsers();
  }

  // Results summary
  const passed = results.filter((r) => r.passed).length;
  const failed = results.filter((r) => !r.passed).length;

  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%\n`);

  if (failed > 0) {
    console.log('❌ Failed Tests:');
    results
      .filter((r) => !r.passed)
      .forEach((r) => {
        console.log(`  • ${r.name}: ${r.error}`);
      });
    process.exit(1);
  } else {
    console.log('🎉 All tests passed! The point ledger migration is working correctly.');
  }
}

main().catch((error) => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
