#!/usr/bin/env tsx

import { supabaseScriptClient } from '../../lib/server/supabase/script-client';

const supabase = supabaseScriptClient;

// Test data
const TEST_WALLET_1 = '******************************************';
const TEST_WALLET_2 = '******************************************';
const TEST_WALLET_3 = '******************************************';
const TEST_WALLET_4 = '******************************************'; // User with no leaderboard records

type BulkUserStatsResponse = Array<{
  user_id: string;
  total_games: number;
  total_bet_amount: string;
  total_profit_loss: string;
}>;

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: Record<string, unknown>;
}

const results: TestResult[] = [];

async function pause(seconds: number = 3): Promise<void> {
  console.log(`⏸️  Pausing for ${seconds} seconds to monitor database...`);
  await new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}

async function runTest(name: string, testFn: () => Promise<void>): Promise<void> {
  try {
    console.log(`🧪 Running: ${name}`);
    await testFn();
    results.push({ name, passed: true });
    console.log(`✅ ${name}`);
    await pause(2); // Pause after each test
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    results.push({ name, passed: false, error: errorMsg });
    console.log(`❌ ${name}: ${errorMsg}`);
    await pause(3); // Longer pause on errors
  }
}

async function setupTestUsers(): Promise<{
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}> {
  // Clean up any existing test data thoroughly
  await cleanupTestData();

  // Generate unique referral codes for this test run
  const timestamp = Date.now().toString().slice(-6);
  const ref1 = `BULK${timestamp}1`;
  const ref2 = `BULK${timestamp}2`;
  const ref3 = `BULK${timestamp}3`;
  const ref4 = `BULK${timestamp}4`;

  // Bulk insert test users
  const { data: users, error } = await supabase
    .from('users')
    .insert([
      {
        wallet_address: TEST_WALLET_1,
        referral_code: ref1,
        points: 0,
        multiplier: 1.5,
        streak: 10,
      },
      {
        wallet_address: TEST_WALLET_2,
        referral_code: ref2,
        points: 0,
        multiplier: 2.0,
        streak: 5,
      },
      {
        wallet_address: TEST_WALLET_3,
        referral_code: ref3,
        points: 0,
        multiplier: 1.0,
        streak: 1,
      },
      {
        wallet_address: TEST_WALLET_4,
        referral_code: ref4,
        points: 0,
        multiplier: 1.0,
        streak: 1,
      },
    ])
    .select('id, wallet_address')
    .order('wallet_address');

  if (error || !users || users.length !== 4) {
    throw new Error(`Failed to create test users: ${error?.message}`);
  }

  console.log(`✅ Created 4 test users in bulk`);

  // Return users in the expected order based on wallet addresses
  return {
    userId1: users[0].id, // TEST_WALLET_1 (0x1111...)
    userId2: users[1].id, // TEST_WALLET_2 (0x2222...)
    userId3: users[2].id, // TEST_WALLET_3 (0x3333...)
    userId4: users[3].id, // TEST_WALLET_4 (0x4444...)
  };
}

async function setupTestLeaderboardData(userIds: {
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}): Promise<void> {
  // Create test leaderboard records
  const { error } = await supabase.from('leaderboard_records').insert([
    // User 1: Has both death_race and laser_party records
    {
      user_id: userIds.userId1,
      game_type: 'death_race',
      total_games: 10,
      total_bet_amount: 100.5,
      total_profit_loss: 25.75,
    },
    {
      user_id: userIds.userId1,
      game_type: 'laser_party',
      total_games: 5,
      total_bet_amount: 50.25,
      total_profit_loss: -10.0,
    },
    // User 2: Has only death_race records
    {
      user_id: userIds.userId2,
      game_type: 'death_race',
      total_games: 20,
      total_bet_amount: 200.0,
      total_profit_loss: 50.0,
    },
    // User 3: Has only laser_party records
    {
      user_id: userIds.userId3,
      game_type: 'laser_party',
      total_games: 3,
      total_bet_amount: 30.0,
      total_profit_loss: -5.5,
    },
    // User 4: No leaderboard records (intentionally omitted)
  ]);

  if (error) {
    throw new Error(`Failed to create test leaderboard data: ${error.message}`);
  }

  console.log(`✅ Created test leaderboard records`);
}

async function cleanupTestData(): Promise<void> {
  console.log('🧹 Cleaning up test data...');

  // Get test user IDs first
  const { data: testUsers } = await supabase
    .from('users')
    .select('id')
    .in('wallet_address', [TEST_WALLET_1, TEST_WALLET_2, TEST_WALLET_3, TEST_WALLET_4]);

  if (testUsers && testUsers.length > 0) {
    const userIds = testUsers.map((u) => u.id);

    // Delete leaderboard_records first (due to foreign key constraints)
    const { error: leaderboardError } = await supabase
      .from('leaderboard_records')
      .delete()
      .in('user_id', userIds);

    if (leaderboardError) {
      console.log(`⚠️  Failed to delete leaderboard records: ${leaderboardError.message}`);
    } else {
      console.log(`✅ Deleted leaderboard records for test users`);
    }

    // Delete users
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .in('wallet_address', [TEST_WALLET_1, TEST_WALLET_2, TEST_WALLET_3, TEST_WALLET_4]);

    if (userError) {
      console.log(`⚠️  Failed to delete test users: ${userError.message}`);
    } else {
      console.log(`✅ Deleted ${testUsers.length} test users`);
    }
  } else {
    console.log('ℹ️  No test users found to clean up');
  }

  // Clean up any remaining test records for our specific test users
  if (testUsers && testUsers.length > 0) {
    const userIds = testUsers.map((u) => u.id);
    const { error: recordError } = await supabase
      .from('leaderboard_records')
      .delete()
      .in('user_id', userIds);

    if (recordError && recordError.code !== 'PGRST116') {
      // Ignore "not found" errors
      console.log(`⚠️  Failed to delete remaining test records: ${recordError.message}`);
    }
  }
}

async function testBulkUserStatsAllGameTypes(userIds: {
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}): Promise<void> {
  const { data, error } = await supabase.rpc('get_bulk_user_stats', {
    p_user_ids: [userIds.userId1, userIds.userId2, userIds.userId4], // Include user with no records
    // p_game_type omitted (undefined) for all game types
  });

  if (error) throw new Error(`RPC error: ${error.message}`);
  if (!data) throw new Error('No data returned');

  const stats = (data as BulkUserStatsResponse) || [];

  console.log(`📊 All game types result:`, JSON.stringify(stats, null, 2));

  // Should return 3 users
  if (stats.length !== 3) {
    throw new Error(`Expected 3 users, got ${stats.length}`);
  }

  // Find user1 stats (should aggregate both game types: 10+5=15 games, 100.50+50.25=150.75 bet, 25.75-10.00=15.75 profit)
  const user1Stats = stats.find((s) => s.user_id === userIds.userId1);
  if (!user1Stats) throw new Error('User1 stats not found');

  if (user1Stats.total_games !== 15) {
    // 10 + 5
    throw new Error(`User1 total_games expected 15, got ${user1Stats.total_games}`);
  }

  if (String(user1Stats.total_bet_amount) !== '151') {
    // 100.50 + 50.25 = 150.75, rounded to 151
    throw new Error(`User1 total_bet_amount expected '151', got '${user1Stats.total_bet_amount}'`);
  }

  if (String(user1Stats.total_profit_loss) !== '16') {
    // 25.75 + (-10.00) = 15.75, rounded to 16
    throw new Error(`User1 total_profit_loss expected '16', got '${user1Stats.total_profit_loss}'`);
  }

  // Find user2 stats (should have only death_race: 20 games, 200.00 bet, 50.00 profit)
  const user2Stats = stats.find((s) => s.user_id === userIds.userId2);
  if (!user2Stats) throw new Error('User2 stats not found');

  if (user2Stats.total_games !== 20) {
    throw new Error(`User2 total_games expected 20, got ${user2Stats.total_games}`);
  }

  // Find user4 stats (should have zeros - no records)
  const user4Stats = stats.find((s) => s.user_id === userIds.userId4);
  if (!user4Stats) throw new Error('User4 stats not found');

  if (user4Stats.total_games !== 0) {
    throw new Error(`User4 total_games expected 0, got ${user4Stats.total_games}`);
  }

  if (user4Stats.total_bet_amount !== '0') {
    throw new Error(`User4 total_bet_amount expected '0', got '${user4Stats.total_bet_amount}'`);
  }
}

async function testBulkUserStatsSpecificGameType(userIds: {
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}): Promise<void> {
  const { data, error } = await supabase.rpc('get_bulk_user_stats', {
    p_user_ids: [userIds.userId1, userIds.userId2, userIds.userId3],
    p_game_type: 'death_race',
  });

  if (error) throw new Error(`RPC error: ${error.message}`);
  if (!data) throw new Error('No data returned');

  const stats = (data as BulkUserStatsResponse) || [];

  console.log(`📊 Specific game type (death_race) result:`, JSON.stringify(stats, null, 2));

  // Should return 3 users
  if (stats.length !== 3) {
    throw new Error(`Expected 3 users, got ${stats.length}`);
  }

  // Find user1 stats (should only include death_race: 10 games, 100.50 bet, 25.75 profit)
  const user1Stats = stats.find((s) => s.user_id === userIds.userId1);
  if (!user1Stats) throw new Error('User1 stats not found');

  if (user1Stats.total_games !== 10) {
    // Only death_race games
    throw new Error(`User1 total_games expected 10, got ${user1Stats.total_games}`);
  }

  if (user1Stats.total_bet_amount !== '101') {
    // 100.50 rounded to 101
    throw new Error(`User1 total_bet_amount expected '101', got '${user1Stats.total_bet_amount}'`);
  }

  // Find user2 stats (should include death_race: 20 games, 200.00 bet, 50.00 profit)
  const user2Stats = stats.find((s) => s.user_id === userIds.userId2);
  if (!user2Stats) throw new Error('User2 stats not found');

  if (user2Stats.total_games !== 20) {
    throw new Error(`User2 total_games expected 20, got ${user2Stats.total_games}`);
  }

  // Find user3 stats (should have zeros - no death_race records)
  const user3Stats = stats.find((s) => s.user_id === userIds.userId3);
  if (!user3Stats) throw new Error('User3 stats not found');

  if (user3Stats.total_games !== 0) {
    throw new Error(`User3 total_games expected 0, got ${user3Stats.total_games}`);
  }
}

async function testBulkUserStatsLaserPartyType(userIds: {
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}): Promise<void> {
  const { data, error } = await supabase.rpc('get_bulk_user_stats', {
    p_user_ids: [userIds.userId1, userIds.userId2, userIds.userId3, userIds.userId4],
    p_game_type: 'laser_party',
  });

  if (error) throw new Error(`RPC error: ${error.message}`);
  if (!data) throw new Error('No data returned');

  const stats = (data as BulkUserStatsResponse) || [];

  console.log(`📊 Laser party game type result:`, JSON.stringify(stats, null, 2));

  // Should return 4 users
  if (stats.length !== 4) {
    throw new Error(`Expected 4 users, got ${stats.length}`);
  }

  // Find user1 stats (should include laser_party: 5 games, 50.25 bet, -10.00 profit)
  const user1Stats = stats.find((s) => s.user_id === userIds.userId1);
  if (!user1Stats) throw new Error('User1 stats not found');

  if (user1Stats.total_games !== 5) {
    throw new Error(`User1 total_games expected 5, got ${user1Stats.total_games}`);
  }

  // Find user2 stats (should have zeros - no laser_party records)
  const user2Stats = stats.find((s) => s.user_id === userIds.userId2);
  if (!user2Stats) throw new Error('User2 stats not found');

  if (user2Stats.total_games !== 0) {
    throw new Error(`User2 total_games expected 0, got ${user2Stats.total_games}`);
  }

  // Find user3 stats (should include laser_party: 3 games, 30.00 bet, -5.50 profit)
  const user3Stats = stats.find((s) => s.user_id === userIds.userId3);
  if (!user3Stats) throw new Error('User3 stats not found');

  if (user3Stats.total_games !== 3) {
    throw new Error(`User3 total_games expected 3, got ${user3Stats.total_games}`);
  }
}

async function testBulkUserStatsEmptyArray(): Promise<void> {
  const { data, error } = await supabase.rpc('get_bulk_user_stats', {
    p_user_ids: [],
    // p_game_type omitted (undefined) for all game types
  });

  if (error) throw new Error(`RPC error: ${error.message}`);
  if (!data) throw new Error('No data returned');

  const stats = (data as BulkUserStatsResponse) || [];

  if (stats.length !== 0) {
    throw new Error(`Expected empty array, got ${stats.length} items`);
  }

  console.log(`📊 Empty array result: []`);
}

async function testPerformanceComparison(userIds: {
  userId1: string;
  userId2: string;
  userId3: string;
  userId4: string;
}): Promise<void> {
  const startTime = Date.now();

  // Test with all users
  const largeUserSet = [userIds.userId1, userIds.userId2, userIds.userId3, userIds.userId4];

  const { data, error } = await supabase.rpc('get_bulk_user_stats', {
    p_user_ids: largeUserSet,
    // p_game_type omitted (undefined) for all game types
  });

  const endTime = Date.now();
  const duration = endTime - startTime;

  if (error) throw new Error(`RPC error: ${error.message}`);
  if (!data) throw new Error('No data returned');

  console.log(`⚡ Performance test completed in ${duration}ms for ${largeUserSet.length} users`);

  // Basic performance check - should complete reasonably quickly
  if (duration > 5000) {
    // 5 seconds threshold
    throw new Error(`Performance test took too long: ${duration}ms`);
  }

  // Verify we got expected number of results
  const stats = (data as BulkUserStatsResponse) || [];
  if (stats.length !== 4) {
    throw new Error(`Expected 4 users in performance test, got ${stats.length}`);
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting Bulk User Stats Function Tests\n');

  let userIds: { userId1: string; userId2: string; userId3: string; userId4: string };

  try {
    // Setup
    console.log('📋 Setting up test users...');
    userIds = await setupTestUsers();
    console.log(`✅ Created test users:`);
    console.log(
      `   User 1: ${userIds.userId1} (${TEST_WALLET_1}) - Will have death_race + laser_party records`
    );
    console.log(
      `   User 2: ${userIds.userId2} (${TEST_WALLET_2}) - Will have death_race records only`
    );
    console.log(
      `   User 3: ${userIds.userId3} (${TEST_WALLET_3}) - Will have laser_party records only`
    );
    console.log(
      `   User 4: ${userIds.userId4} (${TEST_WALLET_4}) - Will have no leaderboard records`
    );

    console.log('\n📊 Setting up test leaderboard data...');
    await setupTestLeaderboardData(userIds);

    console.log('\n🔍 Database monitoring tips:');
    console.log(
      "   • SELECT * FROM users WHERE wallet_address LIKE '0x1111%' OR wallet_address LIKE '0x2222%' OR wallet_address LIKE '0x3333%' OR wallet_address LIKE '0x4444%';"
    );
    console.log(
      "   • SELECT * FROM leaderboard_records WHERE id LIKE 'test-bulk-stats-%' ORDER BY user_id, game_type;"
    );
    console.log(
      `   • SELECT * FROM get_bulk_user_stats(ARRAY['${userIds.userId1}', '${userIds.userId2}'], NULL);`
    );

    await pause(5);

    // Run all tests
    await runTest('Test bulk stats - all game types (aggregation)', () =>
      testBulkUserStatsAllGameTypes(userIds)
    );
    await runTest('Test bulk stats - death_race game type only', () =>
      testBulkUserStatsSpecificGameType(userIds)
    );
    await runTest('Test bulk stats - laser_party game type only', () =>
      testBulkUserStatsLaserPartyType(userIds)
    );
    await runTest('Test bulk stats - empty array', () => testBulkUserStatsEmptyArray());
    await runTest('Test performance with set-based query', () =>
      testPerformanceComparison(userIds)
    );
  } finally {
    // Final cleanup
    console.log('\n🔄 Final cleanup...');
    await cleanupTestData();
  }

  // Results summary
  const passed = results.filter((r) => r.passed).length;
  const failed = results.filter((r) => !r.passed).length;

  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%\n`);

  if (failed > 0) {
    console.log('❌ Failed Tests:');
    results
      .filter((r) => !r.passed)
      .forEach((r) => {
        console.log(`  • ${r.name}: ${r.error}`);
      });
    process.exit(1);
  } else {
    console.log('🎉 All tests passed! The bulk user stats function is working correctly.');
    console.log('🚀 Performance optimization successful - eliminated N+1 query problem!');
  }
}

// Execute main function when script is run directly
main().catch((error) => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
