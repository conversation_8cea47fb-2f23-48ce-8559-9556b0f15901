import { supabaseScriptClient } from '@/lib/server/supabase/script-client';
import fs from 'fs';
import path from 'path';
import { formatEther } from 'viem';
import { DAILY_LEADERBOARD_NUM_WINNERS, DAILY_LEADERBOARD_TOTAL_POINTS } from '@/lib/constants';
import { getDeathPointsDistribution } from '@/lib/utils/daily-leaderboard';

const BATCH_SIZE = 500;
const DECAY_FACTOR = 0.93;

/**
 * Generates daily time buckets from a start date until now.
 */
function generateDailyBuckets(firstInvocation: Date): { start: Date; end: Date }[] {
  const buckets: { start: Date; end: Date }[] = [];
  let currentEnd = firstInvocation;
  let currentStart = new Date(firstInvocation.getTime() - 24 * 60 * 60 * 1000);
  const now = new Date();

  while (currentEnd <= now) {
    buckets.push({
      start: new Date(currentStart),
      end: new Date(currentEnd),
    });
    // Move to the next 24-hour window
    currentStart.setDate(currentStart.getDate() + 1);
    currentEnd.setDate(currentEnd.getDate() + 1);
  }
  return buckets;
}

/**
 * Pre-calculates leaderboard points, returns the aggregated totals,
 * and writes the daily breakdown to a separate CSV file.
 */
async function calculateAndExportLeaderboardData(): Promise<Map<string, number>> {
  const firstInvocationDate = new Date('2025-05-30T16:00:00.000Z'); // 11 AM CDT
  const dailyBuckets = generateDailyBuckets(firstInvocationDate);
  const totalPointsAwarded = new Map<string, number>();
  const dailyPointsData: {
    date: string;
    endTime: string;
    walletAddress: string;
    points: number;
    rank: number;
  }[] = [];
  const allWinnerWallets = new Set<string>();

  console.log(`Calculating leaderboard points across ${dailyBuckets.length} daily periods...`);

  for (const [index, bucket] of dailyBuckets.entries()) {
    const bucketDate = bucket.end.toISOString().split('T')[0];
    const endTime = bucket.end.toISOString();
    console.log(`\n--- Processing Day ${index + 1}/${dailyBuckets.length} (${bucketDate}) ---`);
    const dailyNetGain = new Map<string, number>();
    let page = 0;
    while (true) {
      const from = page * BATCH_SIZE;
      const to = from + BATCH_SIZE - 1;
      const { data: games, error } = await supabaseScriptClient
        .from('games')
        .select('wallet_address, status, bet_amount, final_multiplier')
        .in('status', ['won', 'lost'])
        .gte('created_at', bucket.start.toISOString())
        .lt('created_at', bucket.end.toISOString())
        .range(from, to);

      if (error) {
        console.error(`Error fetching games for day ${index + 1}:`, error);
        break;
      }
      if (!games || games.length === 0) break;

      for (const game of games) {
        if (!game.wallet_address || !game.bet_amount) continue;
        const walletAddress = game.wallet_address.toLowerCase();
        const betAmount = parseFloat(game.bet_amount);
        if (isNaN(betAmount) || betAmount <= 0) continue;
        let profitLoss = 0;
        if (game.status === 'won' && game.final_multiplier) {
          const multiplier = parseFloat(String(game.final_multiplier));
          if (isNaN(multiplier) || multiplier <= 0) continue;
          profitLoss = betAmount * multiplier - betAmount;
        } else if (game.status === 'lost') {
          profitLoss = -betAmount;
        }
        const currentGain = dailyNetGain.get(walletAddress) || 0;
        dailyNetGain.set(walletAddress, currentGain + profitLoss);
      }
      page++;
    }

    const rankedUsers = Array.from(dailyNetGain.entries())
      .filter(([, netGain]) => netGain > 0)
      .sort((a, b) => b[1] - a[1]);

    const pointsDistribution = getDeathPointsDistribution(
      DAILY_LEADERBOARD_TOTAL_POINTS,
      DAILY_LEADERBOARD_NUM_WINNERS,
      DECAY_FACTOR
    );

    const winners = rankedUsers.slice(0, DAILY_LEADERBOARD_NUM_WINNERS);
    console.log(`Found ${winners.length} winner(s) for the day.`);

    for (const [i, [walletAddress]] of winners.entries()) {
      const points = pointsDistribution[i];
      const rank = i + 1;
      if (points > 0) {
        // Aggregate total points for the main export
        const currentTotal = totalPointsAwarded.get(walletAddress) || 0;
        totalPointsAwarded.set(walletAddress, currentTotal + points);

        // Store daily breakdown data
        dailyPointsData.push({
          date: bucketDate,
          endTime,
          walletAddress,
          points,
          rank,
        });
        allWinnerWallets.add(walletAddress);
      }
    }
  }

  // --- Write the Daily Breakdown CSV ---
  if (dailyPointsData.length > 0) {
    console.log('\nGenerating daily leaderboard breakdown CSV...');
    const { data: users, error: usersLookupError } = await supabaseScriptClient
      .from('users')
      .select('wallet_address, username')
      .in('wallet_address', Array.from(allWinnerWallets));

    if (usersLookupError) {
      console.error('Error fetching users for daily breakdown:', usersLookupError);
      // Continue with empty usernames instead of failing
    }

    console.log('users:', users?.length, 'allWinnerWallets:', allWinnerWallets.size);
    if (users?.length && users.length > 0) {
      console.log('user:', users[0]);
    }

    const usernameMap = new Map<string, string>();
    if (users && users.length > 0) {
      console.log(`\n- Found ${users.length} user records for the daily breakdown.`);
      console.log('- Checking a sample of fetched usernames...');
      let sampleCount = 0;
      users.forEach((u) => {
        if (u.wallet_address) {
          const username = u.username || 'N/A';
          if (sampleCount < 5) {
            console.log(`  - Wallet: ${u.wallet_address}, Username from DB: '${username}'`);
            sampleCount++;
          }
          usernameMap.set(u.wallet_address, username);
        }
      });
      if (users.length > 5) {
        console.log('  - ... and so on.');
      }
    } else {
      console.log(
        '\n- Could not find any matching user records for the wallet addresses that won points.'
      );
    }

    const header = 'date,end_time,rank,username,wallet_address,points_earned\n';
    const csvRows = dailyPointsData.map((data) => {
      const username = usernameMap.get(data.walletAddress) || 'N/A';
      const safeUsername = `"${username.replace(/"/g, '""')}"`;
      return `${data.date},${data.endTime},${data.rank},${safeUsername},${data.walletAddress},${data.points}`;
    });

    const csvContent = header + csvRows.join('\n');
    const filePath = path.join(process.cwd(), 'daily_leaderboard_breakdown.csv');
    try {
      fs.writeFileSync(filePath, csvContent);
      console.log(`Successfully exported daily breakdown to ${filePath}`);
    } catch (error) {
      console.error(`Failed to write daily breakdown CSV: ${error}`);
      throw error;
    }
  }

  console.log('\nFinished calculating leaderboard data.\n');
  return totalPointsAwarded;
}

/**
 * Pre-calculates the total challenge points for all users.
 */
async function calculateAllChallengePoints(): Promise<Map<string, number>> {
  console.log('Calculating challenge points for all users...');
  const challengePointsMap = new Map<string, number>();

  // 1. Fetch all challenges to get points per challenge
  const { data: challenges, error: challengesError } = await supabaseScriptClient
    .from('challenges')
    .select('id, points');

  if (challengesError) {
    console.error('Error fetching challenges:', challengesError);
    throw new Error(`Failed to fetch challenges: ${challengesError.message}`);
  }

  const pointsPerChallenge = new Map<number, number>();
  if (challenges) {
    challenges.forEach((c) => {
      if (c.id && c.points) {
        pointsPerChallenge.set(c.id, c.points);
      }
    });
  }

  // 2. Fetch all completed submissions
  const { data: submissions, error: submissionsError } = await supabaseScriptClient
    .from('challenge_submissions')
    .select('user_id, challenge_id')
    .eq('status', 'completed');

  if (submissionsError) {
    console.error('Error fetching challenge submissions:', submissionsError);
    return challengePointsMap;
  }

  // 3. Aggregate points per user
  if (submissions) {
    for (const submission of submissions) {
      if (submission.user_id && submission.challenge_id) {
        const points = pointsPerChallenge.get(submission.challenge_id) || 0;
        if (points > 0) {
          const currentPoints = challengePointsMap.get(submission.user_id) || 0;
          challengePointsMap.set(submission.user_id, currentPoints + points);
        }
      }
    }
  }

  console.log('Finished calculating challenge points.');
  return challengePointsMap;
}

const main = async () => {
  // 1. Calculate and export leaderboard data, get back the aggregated totals
  const leaderboardPointsMap = await calculateAndExportLeaderboardData();
  const challengePointsMap = await calculateAllChallengePoints();

  // 2. Proceed with the primary user export
  console.log(`Starting primary user export...`);
  let page = 0;
  const allCsvRows: string[] = [];
  let totalUsersProcessed = 0;

  try {
    while (true) {
      const from = page * BATCH_SIZE;
      const to = from + BATCH_SIZE - 1;

      const { data: users, error: usersError } = await supabaseScriptClient
        .from('users')
        .select('id, username, wallet_address, points')
        .range(from, to)
        .order('created_at', { ascending: true });

      if (usersError) break;
      if (!users || users.length === 0) break;

      // Get stats for all users in this batch using the bulk function
      const userIds = users.map(user => user.id);
      const { data: bulkStats, error: rpcError } = await supabaseScriptClient.rpc(
        'get_bulk_user_stats',
        { p_user_ids: userIds }
      );

      if (rpcError) {
        console.error(`Error fetching bulk stats for batch ${page}:`, rpcError.message);
        // Continue with empty stats rather than failing completely
      }

      // The response is JSON - both functions return the same structure
      // get_user_stats returns: { total_games, total_bet_amount, total_profit_loss }
      // get_bulk_user_stats returns: [{ user_id, total_games, total_bet_amount, total_profit_loss }]
      const statsArray = (bulkStats as Array<{
        user_id: string;
        total_games: number;
        total_bet_amount: string;
        total_profit_loss: string;
      }>) || [];
      
      // Create a map for quick lookup
      const statsMap = new Map<string, typeof statsArray[0]>();
      statsArray.forEach(stat => {
        statsMap.set(stat.user_id, stat);
      });

      for (const user of users) {
        const userStat = statsMap.get(user.id);
        
        const username = user.username ? `"${user.username.replace(/"/g, '""')}"` : '""';
        const totalBetVolume = BigInt(userStat?.total_bet_amount || '0');
        const calculatedProfitLoss = BigInt(userStat?.total_profit_loss || '0');
        const formattedProfitLoss = formatEther(calculatedProfitLoss);
        const totalBetVolumeInEth = formatEther(totalBetVolume);
        const leaderboardPoints = leaderboardPointsMap.get(user.wallet_address) || 0;
        const challengePoints = challengePointsMap.get(user.id) || 0;
        const totalPoints = parseFloat(String(user.points) || '0');
        const rawPlayPoints = totalPoints - leaderboardPoints - challengePoints;
        const playPoints = Math.max(0, rawPlayPoints);
        if (rawPlayPoints < 0) {
          console.warn(
            `Negative playPoints detected for user ${user.id}: total=${totalPoints}, ` +
              `leaderboard=${leaderboardPoints}, challenges=${challengePoints}`
          );
        }

        allCsvRows.push(
          `${user.id},${user.wallet_address},${username},${formattedProfitLoss},${totalBetVolumeInEth},${user.points},${leaderboardPoints},${challengePoints},${playPoints}`
        );
      }

      totalUsersProcessed += users.length;
      page++;
    }

    if (allCsvRows.length > 0) {
      const header =
        'id,wallet_address,username,total_profit_loss,total_bet_volume,points,leaderboard_points,challenge_points,play_points\n';
      const csvContent = header + allCsvRows.join('\n');
      const filePath = path.join(process.cwd(), 'users_export.csv');
      fs.writeFileSync(filePath, csvContent);
      console.log(`\nSuccessfully exported ${totalUsersProcessed} users to ${filePath}`);
    }
  } catch (err) {
    console.error('An unexpected error occurred during the batch process:', err);
    process.exit(1);
  }
};

main();
