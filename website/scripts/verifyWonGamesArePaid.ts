import { Database } from '@/lib/types/database';
import { Abi, createPublicClient, formatEther, Hash, http } from 'viem';
import { abstract } from 'viem/chains';
import DeathRaceGameAbiData from '@/lib/blockchain/abi/DeathRaceGame.json';
import DeathFunAbi from '@/lib/blockchain/abi/DeathFun.json';
import fs from 'fs';
import { IBlockchainProvider } from '@/lib/utils/interfaces';
import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { AbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { AbstractTransactionService } from '@/lib/utils/abstract/AbstractTransactionService';
import { CurrentGameServer } from '@/lib/types/game';

const main = async () => {
  // --- clients
  const rpcUrl = process.env.ABSTRACT_RPC_URL!;
  const publicClient = createPublicClient({
    chain: abstract,
    transport: http(rpcUrl),
  });
  const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS!;
  const oldContractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS!;
  const dryRun = true;

  const oldProvider = new AbstractTransactionService();
  const newProvider = new AbstractProvider();
  const gamesThatCanBeRetried: any[] = [];

  const results = {
    games: [] as {
      id: string;
      onChainGameId: string;
      dbStatus: string;
      blockchainStatus: number;
    }[],
  };
  let done = false;
  let page = 1;
  let moneyOwed = BigInt(0);
  const oldNewHashes = [] as { oldHash: string; newHash: string }[];
  let offset = 0;

  // Parse command line arguments
  const gameId = process.argv[2]; // Optional gameId argument

  if (gameId) {
    console.log(`Processing specific game: ${gameId}`);
  } else {
    console.log('Processing all won games');
  }

  // --- paginate
  while (!done) {
    // --- get all games that are won
    let prom = supabaseServerClient
      .from('games')
      .select('*')
      .order('created_at', { ascending: true })
      .range(offset, offset + 1000)
      .eq('status', 'won')
      .limit(1000);

    if (gameId) {
      // If gameId is provided, query for that specific game
      prom = prom.eq('id', gameId);
    }

    const { data: games, error } = await prom;
    if (error) {
      console.error('Database error fetching games:', error);
      done = true;
      break;
    }
    if (!games || games.length === 0) {
      if (gameId) {
        console.log(`Game ${gameId} not found`);
      }
      done = true;
      break;
    }

    console.log(`Processing page ${page}, ${games.length} games`);

    const gameId2Game = games.reduce(
      (acc, game) => {
        acc[game.id] = game;
        return acc;
      },
      {} as Record<string, (typeof games)[number]>
    );

    const versions = [] as string[];
    const onChainGameIds = await publicClient.multicall({
      contracts: games.map((game) => {
        versions.push(game.release_version);
        return {
          address:
            game.release_version === 'v1'
              ? (oldContractAddress as `0x${string}`)
              : (contractAddress as `0x${string}`),
          abi:
            game.release_version === 'v1'
              ? (DeathRaceGameAbiData.abi as Abi)
              : (DeathFunAbi.abi as Abi),
          functionName: 'preliminaryToOnChainId',
          args: [game.id],
        };
      }),
    });

    const blockchainGames = await publicClient.multicall({
      contracts: onChainGameIds.map((data, index) => {
        const currVersion = versions[index];
        return {
          address:
            currVersion === 'v1'
              ? (oldContractAddress as `0x${string}`)
              : (contractAddress as `0x${string}`),
          abi: currVersion === 'v1' ? (DeathRaceGameAbiData.abi as Abi) : (DeathFunAbi.abi as Abi),
          functionName: 'getGameDetails',
          args: [data.result as bigint],
        };
      }),
    });

    const onChainGameId2BlockchainGame = {} as Record<string, any>;
    for (let i = 0; i < onChainGameIds.length; i++) {
      onChainGameId2BlockchainGame[(onChainGameIds[i].result as bigint).toString()] = {
        ...(blockchainGames[i].result as any),
        actualGameId: games[i].id,
        actualGame: gameId2Game[games[i].id],
        onChainGameId: (onChainGameIds[i].result as bigint).toString(),
      };
    }

    const gamesWonNotPaid: any[] = [];
    const entries = Object.entries(onChainGameId2BlockchainGame);
    for (let i = 0; i < entries.length; i++) {
      const [onChainGameId, game] = entries[i];
      if (game.status !== 1 && typeof game.betAmount === 'bigint' && game.betAmount > BigInt(0)) {
        if (dryRun) {
          gamesWonNotPaid.push(game);
        } else {
          const txHash = await processCashout(
            oldProvider,
            newProvider,
            game.actualGame,
            BigInt(onChainGameId)
          );
          console.log(`Cashout tx hash: ${txHash}`);
          oldNewHashes.push({ oldHash: game.actualGame.payout_tx_signature, newHash: txHash! });
          gamesWonNotPaid.push(game);
        }
      } else {
        gamesThatCanBeRetried.push(onChainGameId);
      }
    }

    moneyOwed += gamesWonNotPaid.reduce((acc, game) => {
      const betAmount = game.betAmount;
      const finalMultiplier = gameId2Game[game.actualGameId].final_multiplier as number;
      if (typeof betAmount !== 'bigint') {
        console.error(`Game ${game.actualGameId} has no bet amount`);
        console.log(game);
        return acc;
      }
      acc += ((betAmount as bigint) * BigInt(Math.floor(finalMultiplier * 100))) / BigInt(100);
      return acc;
    }, BigInt(0));

    const stats = gamesWonNotPaid.map((game) => ({
      id: game.actualGameId,
      onChainGameId: game.onChainGameId,
      dbStatus: game.actualGame.status,
      blockchainStatus: game.status,
    }));

    results.games.push(...stats);

    if (gameId) {
      done = true;
    } else {
      offset += 1000;
    }
    page++;
  }

  console.log(
    `Games that can be retried: ${gamesThatCanBeRetried.length}, ${gamesThatCanBeRetried}`
  );
  console.log(`Games won not paid: ${results.games.length}`);
  console.log(`Money owed: ${formatEther(moneyOwed)} ETH`);

  // --- write results to file
  fs.writeFileSync('results.json', JSON.stringify(results));
  fs.writeFileSync('oldNewHashes.json', JSON.stringify(oldNewHashes, null, 2));
};

const processCashout = async (
  oldProvider: AbstractTransactionService,
  newProvider: AbstractProvider,
  game: CurrentGameServer,
  onChainGameId: bigint
) => {
  if (!game.onchain_game_id) {
    console.error(`Game ${game.id} has no onchain game id`);
    return;
  }
  if (!game.selected_tiles) {
    console.error(`Game ${game.id} has no selected tiles`);
    return;
  }
  if (!game.game_seed) {
    console.error(`Game ${game.id} has no game seed`);
    return;
  }
  if (!game.bet_amount) {
    console.error(`Game ${game.id} has no bet amount`);
    return;
  }
  if (!game.final_multiplier) {
    console.error(`Game ${game.id} has no final multiplier`);
    return;
  }

  console.log(
    `Processing game ${game.id}, release version: ${game.release_version}, onchain game id: ${onChainGameId}`
  );

  // --- issue cashout
  const payAmountInWei =
    (BigInt(game.bet_amount) * BigInt(Math.floor(game.final_multiplier * 100))) / BigInt(100);
  const { txHash, error: txError } =
    game.release_version === 'v1'
      ? await oldProvider.cashOutOnChain(
          onChainGameId,
          payAmountInWei,
          game.selected_tiles,
          game.game_seed
        )
      : await newProvider.cashOut({
          walletAddress: game.wallet_address as `0x${string}`,
          sessionConfig: null,
          onChainGameId,
          payoutAmountWei: payAmountInWei,
          selectedTiles: game.selected_tiles,
          gameSeed: game.game_seed,
          releaseVersion: game.release_version,
          isServer: true,
        });
  if (txError) {
    console.error(`Error cashing out game ${game.id}: ${txError.message}`);
    return;
  }

  // --- update db with tx hash
  const { error: updateTxHashError } = await supabaseServerClient
    .from('games')
    .update({
      payout_tx_signature: txHash,
    })
    .eq('id', game.id);
  if (updateTxHashError) {
    console.error(`Error updating tx hash for game ${game.id}: ${updateTxHashError.message}`);
    return;
  }

  // --- wait for tx to be confirmed
  const { receipt, error: receiptError } =
    game.release_version === 'v1'
      ? await oldProvider.waitForTransactionReceipt(txHash as Hash, 5)
      : await newProvider.waitForTransactionReceipt(txHash as Hash);
  if (receiptError) {
    console.error(`Error waiting for tx receipt for game ${game.id}: ${receiptError.message}`);
    return;
  }
  if (receipt && receipt.status !== 'success') {
    console.error(`Error waiting for tx receipt for game ${game.id}: ${receipt.status}`);
    return;
  }

  // --- update db with status
  const { error: updateStatusError } = await supabaseServerClient
    .from('games')
    .update({
      status: 'won',
      onchain_game_id: onChainGameId.toString(),
    })
    .eq('id', game.id);
  if (updateStatusError) {
    console.error(`Error updating status for game ${game.id}: ${updateStatusError.message}`);
    return;
  }

  // --- update db with status
  return txHash;
};

main();
