import { MINIMUM_CLAIM_AMOUNT } from '@/lib/constants';
import { Database } from '@/lib/types/database';
import { getAbstractProvider } from '@/lib/utils/abstract/AbstractProvider';
import { createClient } from '@supabase/supabase-js';
import { createPublicClient, formatEther, Hash, http } from 'viem';
import { abstract } from 'viem/chains';

// --- clients
const supabaseServerClient = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false,
    },
  }
);
const rpcUrl = process.env.ABSTRACT_RPC_URL!;
const publicClient = createPublicClient({
  chain: abstract,
  transport: http(rpcUrl),
});

// --- args
const TARGET_WALLET_ADDRESS = process.argv[2] || null;
if (!TARGET_WALLET_ADDRESS) {
  console.error(
    'No wallet address provided. Usage: bun run scripts/checkAndBatchPayReferrals.ts <wallet_address>'
  );
  process.exit(1);
}
const dryRun = true; // Set to false to actually execute payments
let total = BigInt(0);

// --- helpers
const verifyTransactionOnChain = async (txHash: string): Promise<boolean> => {
  try {
    const receipt = await publicClient.getTransactionReceipt({
      hash: txHash as Hash,
    });
    return receipt && receipt.status === 'success';
  } catch (error) {
    return false;
  }
};

const batchPayUnpaidReferrals = async () => {
  console.log(`\n=== Batch paying unpaid referrals ===`);

  let done = false;
  let offset = 0;
  let totalUnpaidProcessed = 0;
  const batchSize = 10000;
  const updates: { ids: string[]; tx_hash: string }[] = [];

  // --- get user
  const { data: user, error: userError } = await supabaseServerClient
    .from('users')
    .select('id')
    .eq('wallet_address', TARGET_WALLET_ADDRESS!)
    .single();
  if (userError) {
    console.error('Error fetching user:', userError);
    return;
  }

  // --- get lock
  const { error: lockError } = await supabaseServerClient.rpc('get_user_lock', {
    p_user_id: user.id,
    p_type: 'referrals_claim',
  });
  if (lockError) {
    console.error('Error getting lock:', lockError);
    return;
  }

  // --- handle unpaid referrals
  while (!done) {
    console.log(`Processing unpaid referrals: ${offset} - ${offset + batchSize - 1}`);
    // Get unpaid referral bonuses
    const { data: unpaidReferrals, error } = await supabaseServerClient
      .from('referral_bonuses')
      .select(`id, bonus_amount`)
      .in('status', ['pending', 'failed'])
      .eq('referrer_id', user.id)
      .order('created_at', { ascending: true })
      .range(offset, offset + batchSize - 1)
      .limit(batchSize);
    if (error) {
      console.error('Database error fetching unpaid referrals:', error);
      break;
    }
    if (!unpaidReferrals || unpaidReferrals.length === 0) {
      done = true;
      break;
    }

    // --- check total bonus amount
    const totalBonusAmount = unpaidReferrals.reduce(
      (sum, referral) => sum + BigInt(referral.bonus_amount ?? '0'),
      BigInt(0)
    );
    if (totalBonusAmount < MINIMUM_CLAIM_AMOUNT) {
      console.log(`Total bonus amount is less than minimum claim amount: ${totalBonusAmount}`);
      break;
    }
    total += totalBonusAmount;

    if (dryRun) {
      totalUnpaidProcessed += unpaidReferrals.length;
      offset += batchSize;
      continue;
    }

    // -- send payout
    const provider = await getAbstractProvider(TARGET_WALLET_ADDRESS!, null);
    const { txHash, error: payReferralError } = await provider.payReferral(
      TARGET_WALLET_ADDRESS! as `0x${string}`,
      totalBonusAmount
    );
    if (payReferralError) {
      console.error('Error paying referral:', payReferralError);

      const { error: updateError } = await supabaseServerClient
        .from('referral_bonuses')
        .update({ status: 'failed' })
        .in(
          'id',
          unpaidReferrals.map((b) => b.id)
        );
      if (updateError) {
        console.error('Error updating bonus status to failed', updateError);
        break;
      }
    }
    if (!txHash) {
      console.error('Failed to get transaction hash');
      break;
    }

    updates.push({
      ids: unpaidReferrals.map((b) => b.id),
      tx_hash: txHash,
    });

    totalUnpaidProcessed += unpaidReferrals.length;
    offset += batchSize;
  }

  for (const update of updates) {
    // mark bonuses as paid
    const { data: paidCount, error: markPaidError } = await supabaseServerClient.rpc(
      'adjust_referral_bonuses_status',
      {
        p_ids: update.ids,
        p_status: 'paid',
        p_tx_hash: update.tx_hash,
      }
    );
    if (markPaidError) {
      console.error('Error marking bonuses as paid:', markPaidError);
      break;
    }
    if (paidCount !== update.ids.length) {
      console.error('Some bonuses were already paid');
      break;
    }
  }

  // --- remove lock
  const { error: unlockError } = await supabaseServerClient
    .from('locks')
    .delete()
    .eq('user_id', user.id)
    .eq('type', 'referrals_claim');
  if (unlockError) {
    console.error('Error unlocking:', unlockError);
  }

  console.log(`\nTotal unpaid referrals processed: ${totalUnpaidProcessed}`);
};

const batchPayInvalidReferrals = async () => {
  const paidReferralsTxs: { transaction_signature: string }[] = []; // fill this to retry payments for invalid transactions (txs reverting due to nonce collision)
  const invalidTransactions: string[] = [];

  // Check each transaction signature on-chain
  for (const { transaction_signature: tx } of paidReferralsTxs) {
    if (!tx) continue;

    const isValid = await verifyTransactionOnChain(tx);

    if (!isValid) {
      console.log(`❌ INVALID: Transaction ${tx} not found on chain`);
      invalidTransactions.push(tx);
    }
  }

  if (invalidTransactions.length > 0) {
    let done = false;
    let offset = 0;
    let totalUnpaidProcessed = 0;
    const batchSize = 10000;
    const updates: { ids: string[]; tx_hash: string }[] = [];

    // --- get user
    const { data: user, error: userError } = await supabaseServerClient
      .from('users')
      .select('id')
      .eq('wallet_address', TARGET_WALLET_ADDRESS!)
      .single();
    if (userError) {
      console.error('Error fetching user:', userError);
      return;
    }

    // --- get lock
    const { error: lockError } = await supabaseServerClient.rpc('get_user_lock', {
      p_user_id: user.id,
      p_type: 'referrals_claim',
    });
    if (lockError) {
      console.error('Error getting lock:', lockError);
      return;
    }

    // --- handle unpaid referrals
    while (!done) {
      console.log(`Processing invalid referrals: ${offset} - ${offset + batchSize - 1}`);

      // Get unpaid referral bonuses
      const { data: invalidReferrals, error } = await supabaseServerClient
        .from('referral_bonuses')
        .select(`id, bonus_amount`)
        .eq('status', 'paid')
        .in('transaction_signature', invalidTransactions)
        .eq('referrer_id', user.id)
        .order('created_at', { ascending: true })
        .range(offset, offset + batchSize - 1)
        .limit(batchSize);
      if (error) {
        console.error('Database error fetching unpaid referrals:', error);
        break;
      }
      if (!invalidReferrals || invalidReferrals.length === 0) {
        done = true;
        break;
      }

      // --- check total bonus amount
      const totalBonusAmount = invalidReferrals.reduce(
        (sum, referral) => sum + BigInt(referral.bonus_amount ?? '0'),
        BigInt(0)
      );
      if (totalBonusAmount < MINIMUM_CLAIM_AMOUNT) {
        console.log(`Total bonus amount is less than minimum claim amount: ${totalBonusAmount}`);
        break;
      }
      total += totalBonusAmount;

      if (dryRun) {
        totalUnpaidProcessed += invalidReferrals.length;
        offset += batchSize;
        continue;
      }

      // -- send payout
      const provider = await getAbstractProvider(TARGET_WALLET_ADDRESS!, null);
      const { txHash, error: payReferralError } = await provider.payReferral(
        TARGET_WALLET_ADDRESS! as `0x${string}`,
        totalBonusAmount
      );
      if (payReferralError) {
        console.error('Error paying referral:', payReferralError);

        const { error: updateError } = await supabaseServerClient
          .from('referral_bonuses')
          .update({ status: 'failed' })
          .in(
            'id',
            invalidReferrals.map((b) => b.id)
          );
        if (updateError) {
          console.error('Error updating bonus status to failed', updateError);
          break;
        }
      }
      if (!txHash) {
        console.error('Failed to get transaction hash');
        break;
      }

      updates.push({
        ids: invalidReferrals.map((b) => b.id),
        tx_hash: txHash,
      });

      totalUnpaidProcessed += invalidReferrals.length;
      offset += batchSize;
    }

    for (const update of updates) {
      // mark bonuses as paid
      const { data: paidCount, error: markPaidError } = await supabaseServerClient.rpc(
        'adjust_referral_bonuses_status',
        {
          p_ids: update.ids,
          p_status: 'paid',
          p_tx_hash: update.tx_hash,
        }
      );
      if (markPaidError) {
        console.error('Error marking bonuses as paid:', markPaidError);
        break;
      }
      if (paidCount !== update.ids.length) {
        console.error('Some bonuses were already paid');
        break;
      }
    }

    // --- remove lock
    const { error: unlockError } = await supabaseServerClient
      .from('locks')
      .delete()
      .eq('user_id', user.id)
      .eq('type', 'referrals_claim');
    if (unlockError) {
      console.error('Error unlocking:', unlockError);
    }

    console.log(`\nTotal unpaid referrals processed: ${totalUnpaidProcessed}`);
  }

  return invalidTransactions;
};

// --- main
const main = async () => {
  console.log('🚀 Starting referral verification and batch payment script...');

  try {
    // --- batch pay invalid referrals
    const invalidReferrals = await batchPayInvalidReferrals();
    console.log(`\nInvalid referrals: ${invalidReferrals?.length}`);

    // --- batch pay unpaid referrals
    await batchPayUnpaidReferrals();

    console.log('\n✅ Script completed successfully.');
    console.log(`Total paid: ${formatEther(total)} ETH`);
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
};

main();
