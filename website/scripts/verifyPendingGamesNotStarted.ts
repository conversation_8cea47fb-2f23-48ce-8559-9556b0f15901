import { Database } from '@/lib/types/database';
import { createClient } from '@supabase/supabase-js';
import { Abi, createPublicClient, formatEther, Hash, http } from 'viem';
import { abstract } from 'viem/chains';
import DeathRaceGameAbiData from '@/lib/blockchain/abi/DeathRaceGame.json';
import DeathFunAbi from '@/lib/blockchain/abi/DeathFun.json';
import fs from 'fs';
import { AbstractProviderDeprecated } from '@/lib/utils/abstract';
import { AbstractProvider } from '@/lib/utils/abstract/AbstractProvider';

// ---  types
type DbAndBlockchainGame = {
  [onChainGameId: string]: {
    [key: string]: any;
    actualGameId: string;
    actualGame: Database['public']['Tables']['games']['Row'];
    onChainGameId: string;
  };
};

// --- clients
const supabaseServerClient = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false,
    },
  }
);
const rpcUrl = process.env.ABSTRACT_RPC_URL!;
const publicClient = createPublicClient({
  chain: abstract,
  transport: http(rpcUrl),
});
const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS!;
const oldContractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS!;
const oldProvider = new AbstractProviderDeprecated(rpcUrl);
const newProvider = new AbstractProvider();

const processRefund = async (game: DbAndBlockchainGame) => {
  if (!game.actualGame.game_seed) {
    console.error(`Game ${game.actualGame.id} has no game seed`);
    return;
  }
  if (game.actualGame.selected_tiles && game.actualGame.selected_tiles.length > 0) {
    console.error(`Game ${game.actualGame.id} has selected tiles`);
    return;
  }
  // @ts-ignore
  if (BigInt(game.actualGame.bet_amount) !== (game['2'] as bigint)) {
    console.error(
      `Game ${game.actualGame.id} bet amount ${game.actualGame.bet_amount} is not equal to blockchain bet amount ${game['2']}`
    );
    return;
  }
  if (!game.actualGame.wallet_address) {
    console.error(`Game ${game.actualGame.id} has no wallet address`);
    return;
  }

  const version = game.actualGame.release_version;
  const localContractAddress = version === 'v1' ? oldContractAddress : contractAddress;
  const localAbi = version === 'v1' ? DeathRaceGameAbiData.abi : DeathFunAbi.abi;
  // --- get game onChainId
  const onChainGameId = await publicClient.readContract({
    address: localContractAddress as `0x${string}`,
    abi: localAbi as Abi,
    functionName: 'preliminaryToOnChainId',
    args: [game.actualGame.id],
  });

  console.log(
    `onChainGameId for game ${game.actualGame.id}: ${onChainGameId}, version: ${version}`
  );

  // --- mark the game as lost on chain
  const { txHash, error: txError } = await newProvider.markGameAsLost({
    walletAddress: game.actualGame.wallet_address as `0x${string}`,
    sessionConfig: null,
    onChainGameId: onChainGameId as unknown as bigint,
    selectedTiles: [],
    gameSeed: game.actualGame.game_seed as `0x${string}`,
    releaseVersion: version,
    isServer: true,
  });
  if (txError) {
    console.error(`Error marking game ${game.actualGame.id} as lost: ${txError.message}`);
    return;
  }
  console.log(`txhash for marking game ${game.actualGame.id} as lost: ${txHash}`);

  // --- wait for tx to be confirmed
  const { receipt, error: receiptError } = await newProvider.waitForTransactionReceipt(
    txHash as Hash
  );
  if (receiptError) {
    console.error(
      `Error waiting for tx receipt for game ${game.actualGame.id}: ${receiptError.message}`
    );
    return;
  }
  if (receipt && receipt.status !== 'success') {
    console.error(`Error waiting for tx receipt for game ${game.actualGame.id}: ${receipt.status}`);
    return;
  }

  // --- update db with status
  const { error: updateRefundStatusError } = await supabaseServerClient
    .from('games')
    .update({ status: 'refund_pending' })
    .eq('id', game.actualGame.id);
  if (updateRefundStatusError) {
    console.error(
      `Error updating db with status for game ${game.actualGame.id}: ${updateRefundStatusError.message}`
    );
    return;
  }

  // --- refund the game
  const refundAmount = game['2'];
  const { txHash: refundTxHash, error: refundTxError } =
    version === 'v1'
      ? await oldProvider.withdrawFunds(
          game.actualGame.wallet_address as `0x${string}`,
          refundAmount as unknown as bigint
        )
      : await newProvider.withdrawFunds(
          game.actualGame.wallet_address as `0x${string}`,
          refundAmount as unknown as bigint
        );
  if (refundTxError) {
    console.error(`Error refunding game ${game.actualGame.id}: ${refundTxError.message}`);
  }
  console.log(`txhash for refunding game ${game.actualGame.id}: ${refundTxHash}`);

  // --- update db with hash
  const { error: updateRefundHashError } = await supabaseServerClient
    .from('games')
    .update({ payout_tx_signature: refundTxHash as `0x${string}` })
    .eq('id', game.actualGame.id);
  if (updateRefundHashError) {
    console.error(
      `Error updating db with refund hash for game ${game.actualGame.id}: ${updateRefundHashError.message}`
    );
    return;
  }
  if (!refundTxHash) {
    console.error(`No refund tx hash for game ${game.actualGame.id}`);
    return;
  }

  const { receipt: refundReceipt, error: refundReceiptError } =
    await newProvider.waitForTransactionReceipt(refundTxHash as Hash);
  if (refundReceiptError) {
    console.error(
      `Error waiting for tx receipt for game ${game.actualGame.id}: ${refundReceiptError.message}`
    );
    return;
  }
  if (refundReceipt && refundReceipt.status !== 'success') {
    console.error(
      `Error waiting for tx receipt for game ${game.actualGame.id}: ${refundReceipt.status}`
    );
    return;
  }

  // --- update db with status
  const { error: updateRefundedStatusError } = await supabaseServerClient
    .from('games')
    .update({ status: 'refunded' })
    .eq('id', game.actualGame.id);
  if (updateRefundedStatusError) {
    console.error(
      `Error updating db with status for game ${game.actualGame.id}: ${updateRefundedStatusError.message}`
    );
    return;
  }
};

const twoMinutesAgo = new Date();
twoMinutesAgo.setMinutes(twoMinutesAgo.getMinutes() - 2);
const twoMinutesAgoISO = twoMinutesAgo.toISOString();

const main = async () => {
  const dryRun = false;
  
  // Parse command line arguments
  const gameId = process.argv[2]; // Optional gameId argument
  
  if (gameId) {
    console.log(`Processing specific game: ${gameId}`);
  } else {
    console.log('Processing all creation_failed games');
  }

  const results = {
    games: [] as {
      id: string;
      onChainGameId: string;
      dbStatus: string;
      blockchainStatus: number;
    }[],
  };
  let done = false;
  let page = 1;
  let moneyOwed = BigInt(0);
  let offset = 0;

  // --- paginate
  while (!done) {
    // --- get all games that are won or specific game if gameId provided
    let query = supabaseServerClient
      .from('games')
      .select('*');
    
    if (gameId) {
      // If gameId is provided, query for that specific game
      query = query.eq('id', gameId);
    } else {
      // Otherwise, query for creation_failed games as before
      query = query
        .eq('status', 'creation_failed')
        .lt('created_at', twoMinutesAgoISO)
        .order('created_at', { ascending: true })
        .range(offset, offset + 1000)
        .limit(1000);
    }
    
    const { data: games, error } = await query;
    
    if (error) {
      console.error('Database error fetching games:', error);
      done = true;
      break;
    }
    if (!games || games.length === 0) {
      if (gameId) {
        console.log(`Game ${gameId} not found`);
      }
      done = true;
      break;
    }
    console.log(`Processing page ${page} games count: ${games.length}`);

    const gameId2Game = games.reduce(
      (acc, game) => {
        acc[game.id] = game;
        return acc;
      },
      {} as Record<string, (typeof games)[number]>
    );

    const versions: string[] = [];
    const onChainGameIds = await publicClient.multicall({
      contracts: games.map((game) => {
        versions.push(game.release_version);
        return {
          address:
            game.release_version === 'v1'
              ? (oldContractAddress as `0x${string}`)
              : (contractAddress as `0x${string}`),
          abi:
            game.release_version === 'v1'
              ? (DeathRaceGameAbiData.abi as Abi)
              : (DeathFunAbi.abi as Abi),
          functionName: 'preliminaryToOnChainId',
          args: [game.id],
        };
      }),
    });

    const blockchainGames = await publicClient.multicall({
      contracts: onChainGameIds.map((data, i) => {
        const version = versions[i];
        return {
          address:
            version === 'v1'
              ? (oldContractAddress as `0x${string}`)
              : (contractAddress as `0x${string}`),
          abi: version === 'v1' ? (DeathRaceGameAbiData.abi as Abi) : (DeathFunAbi.abi as Abi),
          functionName: 'games',
          args: [(data.result as bigint) || BigInt(0)],
        };
      }),
    });

    const onChainGameId2BlockchainGame = {} as {
      [onChainGameId: string]: {
        [key: string]: any;
        actualGameId: string;
        actualGame: Database['public']['Tables']['games']['Row'];
        onChainGameId: string;
      };
    };
    for (let i = 0; i < onChainGameIds.length; i++) {
      onChainGameId2BlockchainGame[(onChainGameIds[i].result as bigint).toString()] = {
        ...(blockchainGames[i].result as any),
        actualGameId: games[i].id,
        actualGame: gameId2Game[games[i].id],
        onChainGameId: (onChainGameIds[i].result as bigint).toString(),
      };
    }

    const gamesPendingButStarted: DbAndBlockchainGame[] = [];
    const values = Object.values(onChainGameId2BlockchainGame);
    for (let i = 0; i < values.length; i++) {
      const game = values[i];
      if (game['3'] === 0 && game['7'] === 'v1') {
        if (dryRun) {
          gamesPendingButStarted.push(game);
        } else {
          gamesPendingButStarted.push(game);
          await processRefund(game);
        }
      }
    }

    moneyOwed += gamesPendingButStarted.reduce((acc, game) => {
      const betAmount = game['2'];
      acc += betAmount as unknown as bigint;
      return acc;
    }, BigInt(0));

    const stats = gamesPendingButStarted.map((game) => ({
      id: game.actualGameId as unknown as string,
      onChainGameId: game.onChainGameId.toString(),
      dbStatus: game.actualGame.status as string,
      blockchainStatus: game['3'] as unknown as number,
    }));

    results.games.push(...stats);
    
    // If processing a specific game, we're done after one iteration
    if (gameId) {
      done = true;
    } else {
      offset += games.length;
    }
    page++;
  }

  console.log(`Games pending but started: ${results.games.length}`);
  console.log(`Money owed: ${formatEther(moneyOwed)} ETH`);

  // --- write results to file
  fs.writeFileSync('results_pending.json', JSON.stringify(results));
};

main();
