import { PrivyClient } from '@privy-io/server-auth';

const main = async () => {
  if (!process.env.NEXT_PUBLIC_PRIVY_APP_ID || !process.env.PRIVY_APP_SECRET) {
    throw new Error('NEXT_PUBLIC_PRIVY_APP_ID and PRIVY_APP_SECRET must be set');
  }

  // Initialize Privy client using environment variables
  const privy = new PrivyClient(process.env.NEXT_PUBLIC_PRIVY_APP_ID, process.env.PRIVY_APP_SECRET);

  // Create a server wallet using Privy's API
  const { id: walletId, address } = await privy.walletApi.createWallet({
    chainType: 'ethereum',
  });

  console.log(`Server wallet created: ${address} (${walletId})`);
};

main().catch(console.error);
