import { supabaseServerClient } from '@/lib/server/supabase/server-client';
import { Abi, createPublicClient, formatEther, Hash, http } from 'viem';
import { abstract } from 'viem/chains';
import DeathRaceGameAbiData from '@/lib/blockchain/abi/DeathRaceGame.json';
import DeathFunAbi from '@/lib/blockchain/abi/DeathFun.json';
import { AbstractProviderDeprecated } from '@/lib/utils/abstract';
import { AbstractProvider } from '@/lib/utils/abstract/AbstractProvider';

// --- clients
const rpcUrl = process.env.ABSTRACT_RPC_URL!;
const publicClient = createPublicClient({
  chain: abstract,
  transport: http(rpcUrl),
});

const contractAddress = process.env.NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS!;
const oldContractAddress = process.env.NEXT_PUBLIC_ABSTRACT_OLD_CONTRACT_ADDRESS!;
const oldProvider = new AbstractProviderDeprecated(rpcUrl);
const newProvider = new AbstractProvider();
const dryRun = false;

const processGamePayout = async (gameId: string) => {
  console.log(`Processing game payout for: ${gameId}`);

  // --- Get game from database
  const { data: game, error: gameError } = await supabaseServerClient
    .from('games')
    .select('*')
    .eq('id', gameId)
    .single();

  if (gameError || !game) {
    console.error(`Game ${gameId} not found in database:`, gameError);
    return;
  }

  console.log(`Found game in DB: ${game.id}, status: ${game.status}`);

  // --- Validate game requirements
  if (!game.wallet_address) {
    console.error(`Game ${gameId} has no wallet address`);
    return;
  }

  if (!game.bet_amount) {
    console.error(`Game ${gameId} has no bet amount`);
    return;
  }

  if (!game.final_multiplier) {
    console.error(`Game ${gameId} has no final multiplier`);
    return;
  }

  // --- Check if game exists on chain
  const version = game.release_version || 'v2';
  const localContractAddress = version === 'v1' ? oldContractAddress : contractAddress;
  const localAbi = version === 'v1' ? DeathRaceGameAbiData.abi : DeathFunAbi.abi;

  try {
    const onChainGameId = await publicClient.readContract({
      address: localContractAddress as `0x${string}`,
      abi: localAbi as Abi,
      functionName: 'preliminaryToOnChainId',
      args: [gameId],
    });

    console.log(`OnChain game ID: ${onChainGameId}`);

    if (onChainGameId !== BigInt(0)) {
      console.log(`Game ${gameId} exists on chain with ID ${onChainGameId}, skipping...`);
      return;
    }
  } catch (error) {
    console.log(`Game ${gameId} does not exist on chain or error checking:`, error);
  }

  // --- Calculate payout
  const betAmount = BigInt(game.bet_amount);
  const finalMultiplier = game.final_multiplier;
  const payoutAmount = (betAmount * BigInt(Math.floor(finalMultiplier * 10000))) / BigInt(10000);

  console.log(`Bet Amount: ${formatEther(betAmount)} ETH`);
  console.log(`Final Multiplier: ${finalMultiplier}x`);
  console.log(`Payout Amount: ${formatEther(payoutAmount)} ETH`);

  if (dryRun) {
    console.log('Dry run, skipping payout...');
    return;
  }

  if (payoutAmount <= BigInt(0)) {
    console.error(`Invalid payout amount: ${payoutAmount}`);
    return;
  }

  // --- Execute payout
  const { txHash, error: payoutError } =
    version === 'v1'
      ? await oldProvider.withdrawFunds(game.wallet_address as `0x${string}`, payoutAmount)
      : await newProvider.withdrawFunds(game.wallet_address as `0x${string}`, payoutAmount);

  if (payoutError) {
    console.error(`Error executing payout for game ${gameId}:`, payoutError);
    return;
  }

  console.log(`Payout transaction hash: ${txHash}`);

  // --- Update database with transaction hash
  const { error: updateHashError } = await supabaseServerClient
    .from('games')
    .update({ status: 'payout_pending', payout_tx_signature: txHash as `0x${string}` })
    .eq('id', gameId);
  if (updateHashError) {
    console.error(`Error updating payout tx hash:`, updateHashError);
    return;
  }
  if (!txHash) {
    console.error(`No payout tx hash for game ${gameId}`);
    return;
  }

  // --- Wait for transaction confirmation
  const { receipt, error: receiptError } = await newProvider.waitForTransactionReceipt(
    txHash as Hash
  );
  if (receiptError) {
    console.error(`Error waiting for tx receipt:`, receiptError);
    return;
  }
  if (receipt && receipt.status !== 'success') {
    console.error(`Transaction failed with status: ${receipt.status}`);
    return;
  }

  // --- Update final status
  const { error: finalUpdateError } = await supabaseServerClient
    .from('games')
    .update({ status: 'won' })
    .eq('id', gameId);
  if (finalUpdateError) {
    console.error(`Error updating final game status:`, finalUpdateError);
    return;
  }

  console.log(`Successfully paid out game ${gameId}: ${formatEther(payoutAmount)} ETH`);
};

const main = async () => {
  const gameId = process.argv[2];

  if (!gameId) {
    console.error('Please provide a game ID as the first argument');
    console.log('Usage: bun run scripts/honorGameDoesNotExist.ts <gameId>');
    process.exit(1);
  }

  try {
    await processGamePayout(gameId);
  } catch (error) {
    console.error('Script execution failed:', error);
    process.exit(1);
  }
};

main();
