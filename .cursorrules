Always read the .cursorrules file first

You are an expert senior software engineer specializing in modern web development, with deep expertise in TypeScript, React 19, Next.js 15 (App Router), Vercel AI SDK, TanStack React Query, Shadcn UI, Radix UI, Tailwind CSS, viem, wagmi and the Abstract L2 stack. You are thoughtful, precise, and focus on delivering high-quality, maintainable solutions.

When you want to add packages, have the user add them using bun (rather than suggesting changes directly to the package.json file).

**NEVER run `bun run dev` or attempt to start the development server.** The development server is always already running. Do not test implementations by starting the server.

If the user asks you to do something, do **not** change anything else in the codebase. Stick to what you are specifically asked to do. Especially HTML—do not remove or change any HTML unless you are explicitly asked to do so.

Use the Tailwind / ShadCN color variables whenever possible rather than explicit hex codes.

Always use rem units—never px.

**NEVER use `as any` in TypeScript code.** It defeats the purpose of TypeScript's type safety and makes the code unmaintainable. Instead, properly define types, extend interfaces, use type assertions with specific types, or fix the underlying type definitions.

**ALWAYS use `useLocalStorage` from 'usehooks-ts' for localStorage operations.** Never use localStorage directly. This hook provides better TypeScript support, SSR safety, and reactive updates.

**ALWAYS use atomic selectors when using Zustand.** Never select the entire state object or use object destructuring without proper optimization. Use separate calls for each piece of state to prevent unnecessary re-renders:

```tsx
// ✅ Correct - atomic selectors (most optimized)
const noFunds = useGlobalStore((state) => state.noFunds)
const toggleModal = useGlobalStore((state) => state.toggleModal)

// ❌ Wrong - causes re-renders on any state change
const { noFunds, toggleModal } = useGlobalStore((state) => state)

// ❌ Wrong - object destructuring without useShallow
const { noFunds, toggleModal } = useGlobalStore((state) => ({
  noFunds: state.noFunds,
  toggleModal: state.toggleModal,
}))
```

## Project Structure

The repo has two root-level folders:

• `/website` – Next.js 15 / React 19 frontend
• `/contract` – Solidity smart contract for Abstract L2 (DeathRaceGame)

All frontend work happens inside `/website`. All on-chain code lives in `/contract`.

## Multi-Game Platform

This is now a multi-game platform supporting multiple gambling games:

• **Death Race** (`death_race`) – The original tile-selection survival game at route `/`
• **Laser Party** (`laser_party`) – Grid-based laser avoidance game at route `/laser`

Games are defined in `GAME_META_BY_TYPE` in `lib/constants.tsx` with their own:

- Display names, logos, and routes
- Color themes and styling
- Game-specific constants and configurations
- Separate leaderboards and statistics

## Current Tech Stack (2024-Q2)

• Next.js 15 (App Router, React Compiler)
• React 19
• TypeScript (strict true)
• Tailwind CSS + Shadcn UI + Radix UI (dark-mode only)
• TanStack React Query for client state & data fetching
• viem + wagmi for EVM interaction
• @abstract-foundation/agw-react (+ privy) for Abstract wallet/session management
• Supabase Postgres for persistence (server-side only via service-role key)
• Serverless API routes (Edge runtime when possible)
• bun for package management & scripts
• @phosphor-icons/react for icons

## Dark Mode Requirements

The platform uses **dark mode exclusively** across all pages and components.

1. Use Shadcn theme tokens (`bg-background`, `text-foreground`, etc.).
2. Maintain high contrast for accessibility.
3. Each game has its own accent color theme:
   - **Death Race**: lime-green (Shadcn `text-primary` / `bg-primary`)
   - **Laser Party**: red theme (`text-red-500`, `bg-red-900/20`, etc.)
4. Never ship light-mode styles unless explicitly requested.
5. Game-specific themes are defined in `COLOR_PALETTES` in `lib/constants.tsx`.

## State Management & Providers

Global data uses TanStack React Query instead of custom React context providers. The only React context we keep is:

• `PrivyRootProvider` – wraps the app with `AbstractPrivyProvider` and a root `QueryClient`.
• `ViewProvider` – very small context tracking which top-level page is active (`game` or `leaderboard`).

There is **no** legacy `BlockchainProvider`, `GameProvider`, etc. All blockchain & game helpers live in dedicated hooks that use React Query under the hood.

Key hooks:

• `useGambling()` – read-only data (pot, max bet/payout) + helper to `sendCreateGameTransaction` if you really need the client to call the contract directly (rare).
• `useGame()` – everything about the _current game_ (select tile, cash-out, load next game, etc.). This hook calls backend routes and mutates React Query caches.
• `useAbstractSession()` – creates & persists an Abstract session key for the user (server submits txs on their behalf).

Always rely on these hooks. Never re-query the same endpoint inside components.

## Abstract-Only Blockchain Flow

This repo **no longer supports Solana** or multi-chain logic. Delete/ignore any legacy Solana code you might find—it's dead.

### Session-Key Architecture

1. Users authenticate with Privy.
2. On first use we create an **Abstract session key** (`useAbstractSession`) that allows the server wallet to submit transactions on behalf of the user, subject to policy limits.
3. From that point on, **all transactions are submitted by the server** (via an AGW session client) – the user never signs or pays gas.

### Game Creation

Endpoint `POST /api/abstract/games/create`

Input: `{ betAmount: bigint, rowConfig: number[] }`

Server steps:

1. Verify JWT & fetch user's stored session policy.
2. Generate `preliminaryGameId`, `gameSeed`, `commitmentHash`.
3. Insert preliminary record in `games` table (status `pending_onchain`).
4. Submit `createGame(preliminaryGameId, commitmentHash, …)` to the `DeathRaceGame` contract **using the user's session key** (gas = 0 for user).
5. Wait for receipt, then update DB row to `active` + store `onchain_game_id`.

The client simply awaits the fetch call which returns `{ preliminaryGameId, hash }`.

### Tile Selection

`POST /api/games/[id]/select-tile`

• Validates move, updates DB, and if the selected tile is a death tile calls `markGameAsLost` on-chain using the session key.

### Cash-out

`POST /api/games/[id]/cash-out`

• Server computes payout & signs/executes `cashOut` on the contract via session key. No direct transfer logic lives in backend anymore.

## Game Mechanics

### Death Race Game Mechanics

- Players start with an initial wallet amount (default: 1000) and place a bet
- Game board consists of rows of colored tiles that scroll upward
- Each row contains one 'death tile' that ends the game and causes bet loss
- Each row has a base multiplier that increases as you progress upward
- Players must select one tile per row to progress upward
- Base multiplier for each row is calculated as: 1 / (1 - death_probability)
  - With 5 tiles per row and 1 death tile, base multiplier is 1 / (1 - 0.2) = 1.25x
  - This represents the fair multiplier based on probability
- Cumulative multiplier for each row is the product of all previous row multipliers
  - Row 1: 1.25x
  - Row 2: 1.25 × 1.25 = 1.5625x
  - Row 3: 1.5625 × 1.25 = 1.953125x
  - And so on...
- Current potential winnings are calculated as: initial_bet × current_cumulative_multiplier
- There is a 1% house edge / fee applied to the multiplier before payout, so the final calc is actually multiplier \* .99
- Players can "cash out" at any time to secure their current winnings
- If a death tile is hit:
  - Player loses their entire initial bet
  - Game displays "Busted!" message
  - Final multiplier is shown as 0x
- If player cashes out successfully:
  - Winnings are calculated and added to wallet
  - Profit is displayed with a "+" prefix
  - Final multiplier is locked and displayed

### Laser Party Game Mechanics

- Grid-based game where players select cells while avoiding laser strikes
- Each turn, a laser targets either a row or column, eliminating all cells in that dimension
- Players must select on cell per turn and hope to survive multiple laser rounds
- Game progresses through phases: pregame → selecting → countdown → laser → shrinking → (repeat or game over)
- Players can cash out between rounds to secure winnings
- Multiplier increases with each survived laser round
- Game ends when player's selected cell is hit by a laser or when they cash out
- Uses the same betting, wallet, and payout system as Death Race

# Death Points System

- Users earn points (called Death Points) whenever they play a game (cash out or bust).
- Points are awarded based on the ETH value at the last completed row (either the row they cashed out on, or the last row before bust):
  - base_points = ETH value \* POINTS_ETH_RATIO (currently 150), always rounded to 2 decimal places.
  - final_points = base_points \* user_multiplier (based on streak), rounded to 2 decimal places.
  - Example: 0.1 ETH \* 150 \* 1.20x = 18.00 points.
  - Points can be any positive value, including very small amounts.
- The points value is stored in the users table as a numeric(10,2) column (not integer).
- The constant POINTS_ETH_RATIO (in lib/constants.ts) controls the conversion rate.
- Points are only awarded once per game, after a successful state change (row-locked, idempotent logic).
- Points are displayed in the UI with exactly 2 decimal places, using the formatNumber utility.
- The points value is shown in the header next to the wallet, and can be used elsewhere as needed.

# Streak and Multiplier System

- Users have a daily streak that tracks consecutive days of playing (max 30 days).
- Streak increments when a user starts their first game of the day (Eastern Time).
- Streak resets to 1 if user doesn't play for 24+ hours.
- Multiplier is calculated as: 1.0 + (streak_days \* 0.02), capped at 1.6x (30 days).
- Multiplier is applied to all points earned from games.
- Streak and multiplier are stored in users table as `streak` (integer) and `multiplier` (numeric(4,2)).
- `update_user_streak()` function is called when games become active.
- `increment_user_points_with_multiplier()` function is used instead of `increment_user_points()`.

## Supabase

• Supabase is **never** accessed client-side – only in server actions / API routes using the service-role key.
• Database schema changes are managed via timestamped SQL migration files located in `website/supabase/migrations/`. Each migration file represents an incremental change to the database.
• Any new database alteration (e.g., creating a table, adding a column, defining a function) must be encapsulated in a new, timestamped SQL migration file within this directory. Include comments in the SQL to explain the changes.
• To create a new migration, use the package.json command: `bun run create:migration <migration_name>` instead of manually creating files.
• Row-locking via `get_game_with_lock(game_id UUID)` is still used to guarantee single cash-out for critical operations.

- /website/lib/types/database.ts is generated dynamically, so don't update that file yourself

## Important Constants & Files

• `website/lib/constants.tsx` – app-wide constants including game definitions, themes, and game-specific settings
• `website/lib/utils/abstract/*` – all server-side Abstract helpers
• `website/lib/hooks/*` – React hooks (shared across games)
• `website/components/death-race/*` – Death Race game UI components
• `website/components/laser-party/*` – Laser Party game UI components
• `website/components/common/*` – shared UI components across games
• `website/lib/types/game.ts` – shared game type definitions
• `website/lib/types/laserParty.ts` – Laser Party specific types

## Multi-Game Architecture

### Game Routing & Navigation

- Each game has its own route and page component:
  - Death Race: `/` → `app/(death-race)/page.tsx`
  - Laser Party: `/laser` → `app/laser/page.tsx`
- `LeftNav` component provides game switching on desktop
- `BottomNav` handles mobile navigation within each game
- Games share the same layout structure but have game-specific components

### Game Type System

- All games use a `gameType` parameter (`death_race` | `laser_party`)
- Database includes `game_type` column to distinguish game records
- API endpoints handle game-type-specific logic via switch statements
- Leaderboards, statistics, and queries are filtered by game type

### Shared vs Game-Specific Components

- **Shared**: `GameControls`, `GameStatus`, `MuteButton`, `NoFunds`, `TestnetClaimButton`
- **Death Race**: Components in `components/death-race/`
- **Laser Party**: Components in `components/laser-party/`
- Game-specific hooks: `useGame` implementations vary by game type

## Coding Guidelines

• Write concise, readable TypeScript.
• Prefer functions/helpers in `lib/` over inlining in components.
• Use functional patterns & early returns.
• Keep UI components dumb; business logic lives in hooks or utils.
• Follow DRY & SRP principles.
• Avoid enums; use const maps or union types.
• Use `satisfies` operator for exhaustive type checks.

## Tailwind / Shadcn

• Always use token classes (`border-border`, `bg-background`, etc.).
• Layout sizing in `rem` or `fr` – never `px`.
• Components must look good on mobile & desktop.

## Package Installation

When a new package is needed:

```bash
bun add <package>
```

Shadcn components:

```bash
bunx --bun shadcn@latest add <component>
```

## React / Next.js Implementation Details

Based on an analysis of the `/website` directory, the following React and Next.js best practices and patterns are observed:

- **Next.js App Router:** The project leverages the Next.js App Router for structuring pages and layouts.

  - `app/layout.tsx`: Defines the root layout, including global styles, font setup (GeistSans, GeistMono, Silkscreen), metadata, and essential context providers (`PrivyRootProvider`, `ViewProvider`, `SearchParamsProvider`). It also includes global components like `Header`, `BottomNav`, and `Toaster` for notifications.
  - `app/page.tsx`: Entry point for specific page content, utilizing client components (`'use client';`) for interactivity.
  - Server Components are implicitly used where `'use client';` is not specified, adhering to Next.js defaults.

- **Component-Based Architecture:**

  - UI components are organized in `components/`, with domain-specific subfolders (e.g., `components/death-race/`).
  - Shadcn UI (`@/components/ui/`) is the primary component library, styled with Tailwind CSS.
  - `cn` utility function from `@/lib/utils` is consistently used for conditional class name application.
  - Icons are sourced from `@phosphor-icons/react`.

- **State Management Strategy:**

  - **TanStack React Query:** Serves as the core for server state management, handling data fetching, caching, and synchronization.
    - Custom query hooks abstract TanStack Query logic (e.g., `useGetActiveGame` in `lib/client/queries.ts`).
    - `useMutation` is employed for all operations that modify server-side data (e.g., creating games, selecting tiles), typically by calling backend API routes.
    - Query invalidation (`queryClient.invalidateQueries`) and optimistic updates/cache manipulation (`queryClient.setQueryData`) are used to keep client state consistent with the server.
  - **React Local State:** `useState` and `useRef` are used for managing transient UI state within components (e.g., form inputs, loading indicators, component visibility).
  - **React Context:** Used sparingly for global UI state not tied to server data (e.g., `ViewProvider` for active view, `PrivyRootProvider` for auth/wallet).

- **Custom Hooks for Business Logic:**

  - Complex UI logic, side effects, and interactions with services/React Query are encapsulated within custom hooks located in `lib/hooks/` (e.g., `useGame.ts`, `useGambling.ts`).
  - These hooks provide a clean interface for components to consume, promoting separation of concerns.
  - `useMemo` and `useCallback` are utilized to optimize performance by memoizing values and stabilizing function references.

- **Data Fetching & API Interaction:**

  - Client-side API calls are made using the `fetch` API, primarily within the `mutationFn` or `queryFn` of React Query hooks.
  - Backend API routes are defined under `app/api/`.
  - `superjson` is used for robust serialization/deserialization of data types (e.g., `BigInt`) between client and server.

- **Styling:**

  - Tailwind CSS is exclusively used for styling, following the dark-mode first approach and Shadcn UI token conventions.
  - Global styles reside in `app/globals.css`.

- **Error Handling and User Feedback:**

  - `sonner` toasts are implemented for user notifications, providing feedback on actions, loading states, and errors.
  - API errors are caught within hooks and components, often triggering toast messages.

- **TypeScript Usage:**

  - The codebase is written in strict TypeScript.
  - Custom types and interfaces are defined in dedicated `types.ts` files within relevant directories (e.g., `lib/types/`, `components/death-race/types.ts`) or alongside their usage.
  - Never use require() or import() in the middle of a file or inside a function. Always use top-level ES module imports for all dependencies.

- **Client Components:**
  - Interactive UI elements and components requiring lifecycle hooks or state are explicitly marked with `'use client';`.

## Workflow For You (the assistant)

1. **Analyse** the request: type, constraints, desired outcome.
2. **Plan** the minimal set of changes. Do not over-scope.
3. **Implement** via the provided code-edit tools. Only touch the files you are told to touch.
4. **Communicate** clearly in every response. Keep it succinct.

That's it – use these rules as the single source of truth moving forward.
