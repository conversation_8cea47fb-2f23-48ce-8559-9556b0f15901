# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Start development server:**
```bash
bun dev  # Runs on port 3033 with turbo mode
```

**Package management:**
```bash
bun install
bun add <package>
bun remove <package>
```

**Build and deploy:**
```bash
bun run build
bun start
```

**Linting and testing:**
```bash
bun run lint
bun run test  # Uses Jest
```

**Database operations:**
```bash
bun gen                  # Generate TypeScript types from Supabase schema
bun run create:migration # Create new Supabase migration
bun run migrate         # Run migrations and regenerate types
```

**Development tools:**
```bash
bun run ngrok           # Expose local server via ngrok
```

## Project Architecture

### Tech Stack
- **Runtime:** Bun (package manager and runtime)
- **Framework:** Next.js 15 (App Router) with React 19
- **Styling:** Tailwind CSS with custom design system
- **Database:** Supabase with PostgreSQL
- **Authentication:** Privy for wallet authentication
- **Blockchain:** Abstract (L2), zkSync, with wagmi/viem
- **State Management:** TanStack Query for server state
- **Analytics:** Mixpanel, LaunchDarkly feature flags
- **UI Components:** Radix UI primitives with custom styling

### Core Game Types
The platform supports two main games:
- **Death Race:** Grid-based gambling game with progressive multipliers
- **Laser Party:** Multi-tile selection variant with complex grid logic

### Key Directories

**`/app`** - Next.js App Router structure
- `/(death-race)/` - Main game pages and admin tools
- `/api/` - API routes for games, leaderboards, and blockchain operations
- `/laser/` - Laser Party game pages

**`/components`** - React components organized by feature
- `/death-race/` - Death Race game components
- `/laser-party/` - Laser Party game components  
- `/admin/` - Admin dashboard components
- `/common/` - Shared UI components
- `/ui/` - Base design system components

**`/lib`** - Core business logic and utilities
- `/blockchain/` - Smart contract ABIs and blockchain interfaces
- `/client/` - Client-side utilities (analytics, queries, wallet)
- `/server/` - Server-side utilities (auth, database, analytics)
- `/types/` - TypeScript type definitions
- `/utils/` - Game logic, provably fair algorithms, Abstract blockchain integration
- `/hooks/` - Custom React hooks including feature flags

**`/supabase/migrations`** - Database schema migrations with comprehensive game state management

### Database Schema
The database uses Supabase with complex triggers and functions for:
- Game state management across multiple game types
- Real-time leaderboards with daily/weekly cycles
- Referral system with custom bonus percentages
- Points/rewards system integration
- Comprehensive user statistics tracking

### Game State Architecture
Games flow through states: `pending_onchain` → `creation_failed`/`active` → `lost`/`cashout_pending` → `won`
- Provably fair verification using commitment hashes and game seeds
- Blockchain integration for on-chain game creation and payouts
- Server-side game logic with client-side state synchronization
- Error handling for failed transactions and manual intervention

### Authentication & Security
- Wallet-based authentication via Privy
- Server-side API key validation
- Content Security Policy headers
- Environment variable validation on startup

### Feature Flag System
Uses LaunchDarkly for:
- Game availability toggling (`useAvailableGames`)
- A/B testing capabilities
- Gradual feature rollouts

## Environment Requirements
- Node.js >= 20.0.0
- Bun runtime and package manager
- Required environment variables (validated in `/lib/server/constants.ts`):
  - `APP_ID`
  - `ABSTRACT_RPC_URL` 
  - `ABSCAN_API_KEY`
  - `NEXT_PUBLIC_ABSTRACT_CONTRACT_ADDRESS`
  - `SERVER_SIGNER_PRIVATE_KEY`

## Testing Strategy
- Jest configuration for unit testing
- Focus on game logic verification and provably fair algorithms
- Database function testing for complex statistical calculations

## Claude rules to follow before starting work and implementing changes:
1. First think through the problem or request (prompt) given, read the codebase for relevant files, and write a plan to tasks/todo.md.
2. The plan should have a list of todo items that you can check off as you complete them.
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. If the item requires external knowledge or certain packages, do your research to get the latest knowledge (use Task tool for research).
5. At every step of the way please just give me a high level explanation of what changes you made.
6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity. Add appropropriate documentation or inline comments where you see fit.
7. Finally, add a review section to the [todo.md](http://todo.md/) file with a summary of the changes you made and any other relevant information.
8. Spin up subagents to parallelize, only if applicable and necessary for the task. We need the code to be accurate and efficient, even if generating the code takes additional time.

## Security prompt:
Please go through and check all the code you just wrote and make sure it follows security best practices. Ensure there are no sensitive information in the frontend and backend code, and that there are no vulnerabilities that can be exploited. Be sure to check your own code that you generated and review it as though you are a principal level engineer, before suggesting me to review it.

## Learning from Claude prompt:
Please explain the functionality and code you just built out in detail. Walk me through what you changed and how it works. Act like you’re a principal level engineer teaching me code and explaining the functionalities and interactions of your changes.

## Be productive while Claude is working:
When I am coding with Claude there are long breaks in between me giving me commands to Claude. Typically I spend that time doom scrolling which distracts me and puts me in a bad mental state. I'd like to use that time now to chat with you and generate new ideas, and also reflect on my other ideas and content. I'm not sure how I'd like to use this chat or what role I'd like you to play, but I think it could be much more useful than me doom scrolling. What do you think? What could be the best way for us to use this chat?