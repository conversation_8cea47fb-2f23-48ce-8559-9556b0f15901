import * as hre from 'hardhat';
import { expect } from 'chai';
import { Deployer } from '@matterlabs/hardhat-zksync';
import { Wallet, Provider, Contract } from 'zksync-ethers';
import { vars } from 'hardhat/config';
import { ethers } from 'ethers';
import { LOCAL_RICH_WALLETS } from '../deploy/utils';

describe('DeathFun', function () {
  // Increase timeout for zkSync tests
  this.timeout(60000);

  let deathRaceGame: Contract;
  let deployer: Wallet;
  let player: Wallet;
  let provider: Provider;

  const betAmount = ethers.parseEther('0.01');
  const minBetAmount = ethers.parseEther('0.001');
  const preliminaryGameId = 'test-game-123';
  const commitmentHash = '0x123456789abcdef'; // Placeholder, will be set by backend listener
  const gameState = JSON.stringify({
    moves: [1, 2, 3],
    outcome: 'win',
    payoutMultiplier: 2,
  });
  const gameSeedHash = ethers.keccak256(ethers.toUtf8Bytes('test-seed-value')); // Example seed hash
  const gameSeed = ethers.keccak256(ethers.toUtf8Bytes('actual-test-seed-value')); // Example revealed seed - passed to backend, not contract

  const algoVersion = 'v1';
  const gameConfig = JSON.stringify({
    gameType: 'deathRace',
    gridSize: [5, 5, 5, 5, 5],
    payoutMultipliers: [1, 2, 3, 5, 10],
    maxMoves: 10,
  });

  // Helper to sign createGame parameters with domain separator and abi.encode
  const signCreateGameParams = async (
    preliminaryGameId: string,
    gameSeedHash: string,
    algoVersion: string,
    gameConfig: string,
    player: string,
    betAmount: bigint,
    deadline: bigint
  ) => {
    const abiCoder = ethers.AbiCoder.defaultAbiCoder();
    const encoded = abiCoder.encode(
      ['string', 'string', 'bytes32', 'string', 'string', 'address', 'uint256', 'uint256'],
      [
        'DeathFun:createGame',
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        player,
        betAmount,
        deadline,
      ]
    );
    const hash = ethers.keccak256(encoded);
    const signature = await deployer.signMessage(ethers.getBytes(hash));
    return signature;
  };

  before(async () => {
    provider = new Provider(hre.network.config.url);
    const deployerWallet = LOCAL_RICH_WALLETS[0];
    const playerWallet = LOCAL_RICH_WALLETS[1];
    deployer = new Wallet(deployerWallet.privateKey, provider);
    player = new Wallet(playerWallet.privateKey, provider);

    console.log(`Using rich wallets for testing: 
      Deployer: ${deployerWallet.address}
      Player: ${playerWallet.address}`);
  });

  beforeEach(async function () {
    // Deploy the contract before each test
    const hardhatDeployer = new Deployer(hre, deployer);
    const artifact = await hardhatDeployer.loadArtifact('DeathFun');
    deathRaceGame = await hardhatDeployer.deploy(artifact, ['DeathFun']);
    await deathRaceGame.waitForDeployment();
  });

  describe('Game Creation', function () {
    it('Should allow a player to create a new game with a valid server signature', async function () {
      const contractAsAny = deathRaceGame as any;
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
      const serverSignature = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );

      const tx = await contractAsAny
        .connect(player)
        .createGame(
          preliminaryGameId,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          {
            value: betAmount,
          }
        );
      await tx.wait();
      const onChainGameId = await contractAsAny.getOnChainGameId(preliminaryGameId);

      expect(Number(onChainGameId)).to.not.equal(0);
      const gameDetails = await contractAsAny.getGameDetails(onChainGameId);
      expect(gameDetails.player).to.equal(await player.getAddress());
      expect(gameDetails.betAmount.toString()).to.equal(betAmount.toString());
      expect(gameDetails.gameSeedHash).to.equal(gameSeedHash);
      expect(gameDetails.algoVersion).to.equal(algoVersion);
      expect(gameDetails.gameConfig).to.equal(gameConfig);
    });

    it('Should fail to create a game with an invalid server signature', async function () {
      const contractAsAny = deathRaceGame as any;
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
      const invalidSignature = await player.signMessage(
        ethers.getBytes(
          ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
              ['string', 'string', 'bytes32', 'string', 'string', 'address', 'uint256', 'uint256'],
              [
                'DeathFun:createGame',
                preliminaryGameId,
                gameSeedHash,
                algoVersion,
                gameConfig,
                await player.getAddress(),
                betAmount,
                deadline,
              ]
            )
          )
        )
      );

      try {
        await contractAsAny
          .connect(player)
          .createGame(
            preliminaryGameId,
            gameSeedHash,
            algoVersion,
            gameConfig,
            deadline,
            invalidSignature,
            {
              value: betAmount,
            }
          );
        expect.fail('Transaction should have failed with InvalidServerSignature');
      } catch (error: any) {
        // We expect a revert, check for the specific error if possible,
        // but Hardhat/ethers might wrap it. Check for revert existence.
        expect(error.message).to.include('reverted');
        // Ideally check: expect(error.message).to.include('InvalidServerSignature');
        // but custom errors might not propagate well through the stack yet.
      }
    });

    // Other createGame failure tests (e.g., existing ID) should also include valid signatures now
    it('Should fail to create a game with an existing preliminary ID', async function () {
      const contractAsAny = deathRaceGame as any;
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
      const serverSignature1 = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const tx = await contractAsAny
        .connect(player)
        .createGame(
          preliminaryGameId,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature1,
          {
            value: betAmount,
          }
        );
      await tx.wait();

      const serverSignature2 = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      try {
        await contractAsAny
          .connect(player)
          .createGame(
            preliminaryGameId,
            gameSeedHash,
            algoVersion,
            gameConfig,
            deadline,
            serverSignature2,
            {
              value: betAmount,
            }
          );
        expect.fail('Transaction should have failed with GameAlreadyExists');
      } catch (error: any) {
        expect(error.message).to.include('reverted');
        // Ideally: expect(error.message).to.include('GameAlreadyExists');
      }
    });
  });

  describe('Game Cash Out', function () {
    let onChainGameId: ethers.BigNumberish;
    let contractAsAny: any;
    let newAdmin: Wallet;
    const payoutAmount = betAmount * BigInt(2);
    const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

    beforeEach(async function () {
      contractAsAny = deathRaceGame as any;
      // Fund the contract (needed for payouts)
      const fundingAmount = ethers.parseEther('0.05');
      const fundingGameId = 'funding-game-' + Date.now();
      const fundingGameSeedHash = ethers.ZeroHash;
      const fundingSignature = await signCreateGameParams(
        fundingGameId,
        fundingGameSeedHash,
        algoVersion,
        gameConfig,
        await deployer.getAddress(),
        fundingAmount,
        deadline
      );
      const fundTx = await contractAsAny
        .connect(deployer)
        .createGame(
          fundingGameId,
          fundingGameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          fundingSignature,
          {
            value: fundingAmount,
          }
        );
      await fundTx.wait();
      // Create the actual game for the test using the player wallet
      const serverSignature = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx = await contractAsAny
        .connect(player)
        .createGame(
          preliminaryGameId,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          {
            value: betAmount,
          }
        );
      await createTx.wait();
      onChainGameId = await contractAsAny.getOnChainGameId(preliminaryGameId);
      // Create a new admin wallet for signature tests
      newAdmin = Wallet.createRandom().connect(provider) as unknown as Wallet;
      await deployer.sendTransaction({ to: newAdmin.address, value: ethers.parseEther('1') });
      await contractAsAny.connect(deployer).addAdmin(newAdmin.address);
    });

    it('Player can cash out with a valid admin signature', async function () {
      // Player gets a valid signature from newAdmin
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:cashOut', onChainGameId, payoutAmount, gameState, gameSeed, deadline]
      );
      const hash = ethers.keccak256(encoded);
      const signature = await newAdmin.signMessage(ethers.getBytes(hash));
      await contractAsAny
        .connect(player)
        .cashOut(onChainGameId, payoutAmount, gameState, gameSeed, deadline, signature);
      const gameDetails = await contractAsAny.getGameDetails(onChainGameId);
      expect(Number(gameDetails.status)).to.equal(1); // Won
    });

    it('Player fails to cash out with an invalid signature', async function () {
      // Player uses their own signature (not admin)
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:cashOut', onChainGameId, payoutAmount, gameState, gameSeed, deadline]
      );
      const hash = ethers.keccak256(encoded);
      const badSignature = await player.signMessage(ethers.getBytes(hash));
      await expect(
        contractAsAny
          .connect(player)
          .cashOut(onChainGameId, payoutAmount, gameState, gameSeed, deadline, badSignature)
      ).to.be.rejectedWith(/revert/);
    });

    it('Player fails to cash out with expired deadline', async function () {
      // Use a valid admin signature but with an expired deadline
      const expiredDeadline = BigInt(Math.floor(Date.now() / 1000) - 100000);
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:cashOut', onChainGameId, payoutAmount, gameState, gameSeed, expiredDeadline]
      );
      const hash = ethers.keccak256(encoded);
      const signature = await newAdmin.signMessage(ethers.getBytes(hash));
      // Set the next block timestamp to after the expired deadline
      const block = await provider.getBlock('latest');
      const nextTimestamp = Math.max(Number(expiredDeadline) + 1, Number(block.timestamp) + 1);
      await provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
      await provider.send('evm_mine', []);
      await expect(
        contractAsAny
          .connect(player)
          .cashOut(onChainGameId, payoutAmount, gameState, gameSeed, expiredDeadline, signature)
          .then((tx: any) => tx.wait())
      ).to.be.rejectedWith(/revert/);
    });

    it('Non-player, non-admin cannot cash out', async function () {
      const rando = Wallet.createRandom().connect(provider);
      await deployer.sendTransaction({ to: rando.address, value: ethers.parseEther('1') });
      await expect(
        contractAsAny
          .connect(rando)
          .cashOut(onChainGameId, payoutAmount, gameState, gameSeed, deadline, '0x')
      ).to.be.rejectedWith(/revert/);
    });

    it('New admin can cash out, but fails after removal', async function () {
      // New admin cashes out
      await contractAsAny
        .connect(newAdmin)
        .cashOut(onChainGameId, payoutAmount, gameState, gameSeed, 0, '0x');
      let gameDetails = await contractAsAny.getGameDetails(onChainGameId);
      expect(Number(gameDetails.status)).to.equal(1); // Won
      // Remove admin
      await contractAsAny.connect(deployer).removeAdmin(newAdmin.address);
      expect(await contractAsAny.isAdmin(newAdmin.address)).to.be.false;
      // Create new game for fail test
      const newPrelimId = 'admin-cashout-fail-' + Date.now();
      const serverSignature = await signCreateGameParams(
        newPrelimId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx = await contractAsAny
        .connect(player)
        .createGame(newPrelimId, gameSeedHash, algoVersion, gameConfig, deadline, serverSignature, {
          value: betAmount,
        });
      await createTx.wait();
      const failGameId = await contractAsAny.getOnChainGameId(newPrelimId);
      try {
        await contractAsAny
          .connect(newAdmin)
          .cashOut(failGameId, payoutAmount, gameState, gameSeed, 0, '0x');
        expect.fail('Expected revert for removed admin cashOut');
      } catch (error: any) {
        expect(error.message).to.include('revert');
      }
    });
  });

  describe('Mark Game as Lost', function () {
    let onChainGameId: ethers.BigNumberish;
    let contractAsAny: any;
    let newAdmin: Wallet;
    const actualGameSeed = ethers.id('some-random-seed-for-loss');
    const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

    beforeEach(async function () {
      contractAsAny = deathRaceGame as any;
      // Create game first
      const serverSignature = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx = await contractAsAny
        .connect(player)
        .createGame(
          preliminaryGameId,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          {
            value: betAmount,
          }
        );
      await createTx.wait();
      onChainGameId = await contractAsAny.getOnChainGameId(preliminaryGameId);
      // Create a new admin wallet for signature tests
      newAdmin = Wallet.createRandom().connect(provider) as unknown as Wallet;
      await deployer.sendTransaction({ to: newAdmin.address, value: ethers.parseEther('1') });
      await contractAsAny.connect(deployer).addAdmin(newAdmin.address);
    });

    it('Player can mark game as lost with a valid admin signature', async function () {
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:markGameAsLost', onChainGameId, gameState, actualGameSeed, deadline]
      );
      const hash = ethers.keccak256(encoded);
      const signature = await newAdmin.signMessage(ethers.getBytes(hash));
      await contractAsAny
        .connect(player)
        .markGameAsLost(onChainGameId, gameState, actualGameSeed, deadline, signature);
      const gameDetails = await contractAsAny.getGameDetails(onChainGameId);
      expect(Number(gameDetails.status)).to.equal(2); // Lost
    });

    it('Player fails to mark game as lost with an invalid signature', async function () {
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:markGameAsLost', onChainGameId, gameState, actualGameSeed, deadline]
      );
      const hash = ethers.keccak256(encoded);
      const badSignature = await player.signMessage(ethers.getBytes(hash));
      await expect(
        contractAsAny
          .connect(player)
          .markGameAsLost(onChainGameId, gameState, actualGameSeed, deadline, badSignature)
      ).to.be.rejectedWith(/revert/);
    });

    it('Player fails to mark game as lost with expired deadline', async function () {
      const expiredDeadline = BigInt(Math.floor(Date.now() / 1000) - 100000);
      const abiCoder = ethers.AbiCoder.defaultAbiCoder();
      const encoded = abiCoder.encode(
        ['string', 'uint256', 'string', 'string', 'uint256'],
        ['DeathFun:markGameAsLost', onChainGameId, gameState, actualGameSeed, expiredDeadline]
      );
      const hash = ethers.keccak256(encoded);
      const signature = await newAdmin.signMessage(ethers.getBytes(hash));
      // Set the next block timestamp to after the expired deadline
      const block = await provider.getBlock('latest');
      const nextTimestamp = Math.max(Number(expiredDeadline) + 1, Number(block.timestamp) + 1);
      await provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
      await provider.send('evm_mine', []);
      await expect(
        contractAsAny
          .connect(player)
          .markGameAsLost(onChainGameId, gameState, actualGameSeed, expiredDeadline, signature)
          .then((tx: any) => tx.wait())
      ).to.be.rejectedWith(/revert/);
    });

    it('Non-player, non-admin cannot mark game as lost', async function () {
      const rando = Wallet.createRandom().connect(provider);
      await deployer.sendTransaction({ to: rando.address, value: ethers.parseEther('1') });
      await expect(
        contractAsAny
          .connect(rando)
          .markGameAsLost(onChainGameId, gameState, actualGameSeed, deadline, '0x')
      ).to.be.rejectedWith(/revert/);
    });

    it('New admin can mark game as lost, but fails after removal', async function () {
      // Create new game for lost test
      const newPrelimId = 'admin-lost-test-' + Date.now();
      const serverSignature = await signCreateGameParams(
        newPrelimId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx = await contractAsAny
        .connect(player)
        .createGame(newPrelimId, gameSeedHash, algoVersion, gameConfig, deadline, serverSignature, {
          value: betAmount,
        });
      await createTx.wait();
      const lostGameId = await contractAsAny.getOnChainGameId(newPrelimId);
      // New admin marks as lost
      await contractAsAny
        .connect(newAdmin)
        .markGameAsLost(lostGameId, gameState, actualGameSeed, 0, '0x');
      let gameDetails = await contractAsAny.getGameDetails(lostGameId);
      expect(Number(gameDetails.status)).to.equal(2); // Lost
      // Remove admin
      await contractAsAny.connect(deployer).removeAdmin(newAdmin.address);
      expect(await contractAsAny.isAdmin(newAdmin.address)).to.be.false;
      // Create new game for fail test
      const newPrelimId2 = 'admin-lost-fail-' + Date.now();
      const serverSignature2 = await signCreateGameParams(
        newPrelimId2,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx2 = await contractAsAny
        .connect(player)
        .createGame(
          newPrelimId2,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature2,
          { value: betAmount }
        );
      await createTx2.wait();
      const failGameId = await contractAsAny.getOnChainGameId(newPrelimId2);
      try {
        await contractAsAny
          .connect(newAdmin)
          .markGameAsLost(failGameId, gameState, actualGameSeed, 0, '0x');
        expect.fail('Expected revert for removed admin markGameAsLost');
      } catch (error: any) {
        expect(error.message).to.include('revert');
      }
    });
  });

  describe('Admin Functions', function () {
    // Admin functions (updateHouseFee, withdrawFunds, setServerSignerAddress)
    // are protected by Ownable and don't need server signatures themselves.
    // Tests for these can remain largely unchanged, but we add a test for setServerSignerAddress.
    let contractAsAny: any;

    beforeEach(function () {
      contractAsAny = deathRaceGame as any;
    });

    // Keep existing admin tests (updateHouseFee, withdrawFunds)
    it('Should allow the owner to withdraw funds', async function () {
      // Need to fund the contract first using createGame with a valid server sig
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
      const serverSignature = await signCreateGameParams(
        'funding-game-1',
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const tx = await contractAsAny
        .connect(player) // Player creates the game
        .createGame(
          'funding-game-1',
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          {
            value: betAmount,
          }
        );
      await tx.wait();

      const ownerBalanceBefore = await provider.getBalance(await deployer.getAddress());
      const recipientAddress = await player.getAddress();
      const recipientBalanceBefore = await provider.getBalance(recipientAddress);
      const contractBalanceBefore = await provider.getBalance(await deathRaceGame.getAddress());

      expect(Number(contractBalanceBefore)).to.be.greaterThan(0);

      const withdrawTx = await contractAsAny
        .connect(deployer)
        .withdrawFunds(betAmount, recipientAddress);
      const receipt = await withdrawTx.wait();
      const gasUsed = receipt?.gasUsed ?? BigInt(0);
      const gasPrice = receipt?.gasPrice ?? BigInt(0);
      const gasCost = BigInt(gasUsed) * BigInt(gasPrice);

      const ownerBalanceAfter = await provider.getBalance(await deployer.getAddress());
      const recipientBalanceAfter = await provider.getBalance(recipientAddress);
      const contractBalanceAfter = await provider.getBalance(await deathRaceGame.getAddress());

      // Contract balance might not be exactly 0 if funding game is still there
      // Check that the owner received the withdrawn amount (approximately)
      expect(Number(ethers.formatEther(contractBalanceAfter))).to.be.lessThan(
        Number(ethers.formatEther(contractBalanceBefore))
      );

      // Check recipient balance increased
      expect(recipientBalanceAfter).to.equal(recipientBalanceBefore + betAmount);

      // Check owner balance decreased only by gas
      const expectedOwnerBalance = ownerBalanceBefore - gasCost;
      const ownerBalanceAfterNum = Number(ethers.formatEther(ownerBalanceAfter));
      const expectedOwnerBalanceNum = Number(ethers.formatEther(expectedOwnerBalance));
      const tolerance = 0.001; // Tolerance for gas calculation variations
      expect(ownerBalanceAfterNum).to.be.closeTo(
        expectedOwnerBalanceNum,
        tolerance,
        'Owner balance incorrect after paying gas for withdrawal'
      );
    });

    // ... other existing admin failure tests remain the same ...
    it('Should fail when a non-owner tries to withdraw funds', async function () {
      // Fund first
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
      const serverSignature = await signCreateGameParams(
        'funding-game-2',
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const tx = await contractAsAny
        .connect(player)
        .createGame(
          'funding-game-2',
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          {
            value: betAmount,
          }
        );
      await tx.wait();

      const recipientAddress = await deployer.getAddress(); // Choose any recipient for the test

      try {
        await contractAsAny.connect(player).withdrawFunds(betAmount, recipientAddress);
        expect.fail('Transaction should have failed');
      } catch (error: any) {
        expect(error.message).to.include('reverted');
      }
    });
  });

  describe('Treasury Deposits', function () {
    it('Should accept direct Ether deposits via receive()', async function () {
      const depositAmount = ethers.parseEther('0.1');
      const contractAddress = await deathRaceGame.getAddress();
      const balanceBefore = await provider.getBalance(contractAddress);

      // Send Ether directly to the contract
      const tx = await deployer.sendTransaction({
        to: contractAddress,
        value: depositAmount,
      });
      await tx.wait();

      const balanceAfter = await provider.getBalance(contractAddress);
      const expectedBalance = balanceBefore + depositAmount;

      expect(balanceAfter.toString()).to.equal(
        expectedBalance.toString(),
        'Contract balance did not increase correctly after deposit'
      );
    });
  });

  describe('Admin Wallet Management & Dual Authorization', function () {
    let contractAsAny: any;
    let newAdmin: Wallet;
    let onChainGameId: ethers.BigNumberish;
    const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

    beforeEach(async function () {
      contractAsAny = deathRaceGame as any;
      // Create a new admin wallet
      newAdmin = Wallet.createRandom().connect(provider) as unknown as Wallet;
      // Fund newAdmin for gas
      await deployer.sendTransaction({ to: newAdmin.address, value: ethers.parseEther('1') });
      // Owner adds newAdmin as admin
      await contractAsAny.connect(deployer).addAdmin(newAdmin.address);
      // Create a game for player
      const serverSignature = await signCreateGameParams(
        preliminaryGameId,
        gameSeedHash,
        algoVersion,
        gameConfig,
        await player.getAddress(),
        betAmount,
        deadline
      );
      const createTx = await contractAsAny
        .connect(player)
        .createGame(
          preliminaryGameId,
          gameSeedHash,
          algoVersion,
          gameConfig,
          deadline,
          serverSignature,
          { value: betAmount }
        );
      await createTx.wait();
      onChainGameId = await contractAsAny.getOnChainGameId(preliminaryGameId);
    });

    it('Owner can add and remove an admin wallet', async function () {
      // Already added in beforeEach, check isAdmin
      expect(await contractAsAny.isAdmin(newAdmin.address)).to.be.true;
      // Remove admin
      await contractAsAny.connect(deployer).removeAdmin(newAdmin.address);
      expect(await contractAsAny.isAdmin(newAdmin.address)).to.be.false;
    });
  });
});
