import { HardhatRuntimeEnvironment } from 'hardhat/types';
import { Wallet } from 'zksync-ethers';
// import { LOCAL_RICH_WALLETS } from './utils';
import * as dotenv from 'dotenv';
import { ethers, upgrades } from 'hardhat';
// import "@openzeppelin/hardhat-upgrades";

dotenv.config(); // Load environment variables from .env file

const messagePrefix = 'DeathFun';

// Deploy script for DeathRaceGame contract
export default async function (hre: HardhatRuntimeEnvironment) {
  console.log(`Running deploy script for DeathRaceGame...`);

  // Get the deployer wallet from environment variable
  const PRIVATE_KEY = process.env.WALLET_PRIVATE_KEY;
  if (!PRIVATE_KEY) {
    throw new Error(
      'WALLET_PRIVATE_KEY not set in environment variables (.env file). Please set it.'
    );
  }
  const deployerWallet = new Wallet(PRIVATE_KEY); // Assumes zksync-ethers Wallet

  // // === Add Funding Logic Here ===
  // // Check if the network is one of the local development networks
  // const localNetworkNames = ['localhost', 'hardhat', 'inMemoryNode'];
  // if (localNetworkNames.includes(hre.network.name)) {
  //   console.log(`\nNetwork is local (${hre.network.name}). Checking deployer balance...`);
  //   const deployerAddress = deployerWallet.address;
  //   const provider = hre.ethers.provider; // Get default provider

  //   // Get a rich wallet signer
  //   const richWalletSigner = await provider.getSigner(LOCAL_RICH_WALLETS[0].address);

  //   const balanceWei = await provider.getBalance(deployerAddress);
  //   const balanceEther = ethers.formatEther(balanceWei);
  //   const requiredEther = 1.0; // Minimum balance needed
  //   const fundingAmountEther = 10.0; // Amount to send if needed

  //   console.log(`Deployer (${deployerAddress}) balance: ${balanceEther} ETH`);

  //   if (parseFloat(balanceEther) < requiredEther) {
  //     console.log(
  //       `Balance low. Funding deployer account from rich wallet ${LOCAL_RICH_WALLETS[0].address}...`
  //     );
  //     const fundingAmountWei = ethers.parseEther(fundingAmountEther.toString());
  //     const tx = await richWalletSigner.sendTransaction({
  //       to: deployerAddress,
  //       value: fundingAmountWei,
  //     });
  //     console.log(`Funding transaction sent: ${tx.hash}`);
  //     await tx.wait(); // Wait for transaction confirmation
  //     const newBalanceWei = await provider.getBalance(deployerAddress);
  //     console.log(`Deployer account funded. New balance: ${ethers.formatEther(newBalanceWei)} ETH`);
  //   } else {
  //     console.log('Deployer balance sufficient.');
  //   }
  //   console.log('---'); // Separator
  // }
  // // === End Funding Logic ===

  // Deploy the upgradeable proxy using OpenZeppelin Upgrades
  let contractAddress: string;
  try {
    const DeathFun = await ethers.getContractFactory('contracts/DeathFun.sol:DeathFun');
    const proxy = await upgrades.deployProxy(DeathFun, [messagePrefix]);
    await proxy.waitForDeployment();
    contractAddress = await proxy.getAddress();
  } catch (deployError) {
    console.error(`\n❌ Deployment failed:`, deployError);
    if (deployError instanceof Error) {
      console.error('Error Message:', deployError.message);
      console.error('Stack Trace:', deployError.stack);
    }
    throw deployError;
  }

  // Instructions for manual verification
  if (contractAddress) {
    console.log('\n---');
    console.log('✅ Deployment successful!');
    const network = hre.network.name;
    console.log('\nVerify the implementation contract:');
    console.log(`npx hardhat verify --network ${network} <implementation-address>`);
    console.log('\nVerify the proxy contract:');
    console.log(`npx hardhat verify --network ${network} <proxy-address> '${messagePrefix}'`);
    console.log('---');
  } else {
    console.warn(
      '\n⚠️ Deployment seems to have failed, contract address not obtained. Skipping verification instructions.'
    );
  }
}
