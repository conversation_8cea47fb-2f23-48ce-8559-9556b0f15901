import { ethers, upgrades } from 'hardhat';
import hre from 'hardhat';

// TODO: Set your proxy address here before running the script
const proxyAddress = process.env.PROXY_ADDRESS;

async function main() {
  if (!proxyAddress) {
    throw new Error(
      'Please provide a proxy address via PROXY_ADDRESS environment variable or as a command line argument'
    );
  }

  console.log('Upgrading DeathFun proxy at:', proxyAddress);
  const DeathFun = await ethers.getContractFactory('contracts/DeathFun.sol:DeathFun');
  const upgraded = await upgrades.upgradeProxy(proxyAddress, DeathFun);
  await upgraded.waitForDeployment();
  console.log('✅ Upgrade successful!');
  console.log('---');
  const network = hre.network.name;
  console.log(
    'To verify the new implementation contract, copy the implementation address from the upgrade logs above and run:'
  );
  console.log(`npx hardhat verify --network ${network} ${await upgraded.getAddress()}`);
  console.log('---');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
