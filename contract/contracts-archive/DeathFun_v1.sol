// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

// Revert to standard OpenZeppelin imports
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Strings.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol"; // Import ECDSA

/**
 * @title DeathRaceGame
 * @dev Smart contract to manage Death Race game on Abstract L2 (zkSync Era)
 * 
 * The contract handles game creation, payout management, and provable fairness
 * for the Death Race game, where players navigate through a grid to maximize 
 * their potential winnings. Requires server-signed parameters for critical actions.
 */
contract DeathRaceGame is ReentrancyGuard, Ownable {
    using Strings for uint256;

    // ===================
    // State Variables
    // ===================

    /// @notice Counter for unique on-chain game IDs
    uint256 public gameCounter;

    /// @notice Prefix used in messageHash for domain separation
    string public messagePrefix;

    /// @notice Address authorized to sign parameters for critical actions (server backend)
    address public serverSignerAddress;

    /// @notice Mapping from preliminary game ID to on-chain game ID
    mapping(string => uint256) public preliminaryToOnChainId;

    /// @notice Game status enum
    enum GameStatus {
        Active,
        Won,
        Lost
    }

    /// @notice Game data structure
    struct Game {
        uint256 createdAt;       // Block timestamp when game was created
        address player;          // Player's wallet address
        uint256 betAmount;       // Original bet amount in wei
        GameStatus status;       // Current game status
        uint256 payoutAmount;    // Final payout amount (0 if lost)
        bytes32 gameSeedHash;    // Hash of the seed + gameConfig + algoVersion
        string gameSeed;         // Seed used to generate the game state
        string algoVersion;      // Algorithm version for going from seed to game state
        string gameConfig;       // JSON string containing game configuration
        string gameState;        // JSON string containing final game state/player moves
    }

    /// @notice Mapping from on-chain game ID to Game data
    mapping(uint256 => Game) public games;

    // ===================
    // Events
    // ===================

    /// @notice Emitted when a new game is created
    event GameCreated(
        string preliminaryGameId,
        uint256 indexed onChainGameId,
        address indexed player,
        uint256 betAmount,
        bytes32 gameSeedHash // Keep seed hash in event for listener correlation
    );

    /// @notice Emitted when a payout is sent to a player
    event PayoutSent(
        uint256 indexed onChainGameId,
        uint256 amount,
        address indexed recipient
    );

    /// @notice Emitted when a game status is updated
    event GameStatusUpdated(
        uint256 indexed onChainGameId,
        GameStatus status
    );

    /// @notice Emitted when the server signer address is updated
    event ServerSignerAddressUpdated(address newAddress);

    /// @notice Emitted when funds are deposited directly into the contract
    event DepositReceived(address indexed sender, uint256 amount);

    // ===================
    // Errors
    // ===================

    /// @notice Error when game already exists
    error GameAlreadyExists(string preliminaryGameId);

    /// @notice Error when game doesn't exist
    error GameDoesNotExist(uint256 onChainGameId);

    /// @notice Error when caller is not the player
    error NotGamePlayer(uint256 onChainGameId, address caller);

    /// @notice Error when game is not in active status
    error GameNotActive(uint256 onChainGameId);

    /// @notice Error when payout fails
    error PayoutFailed(uint256 onChainGameId, uint256 amount);

    /// @notice Error when server signature is invalid or doesn't match expected signer
    error InvalidServerSignature();

    // ===================
    // Constructor
    // ===================

    /**
     * @dev Constructor sets the contract owner and initial server signer address
     * @param _messagePrefix The prefix used in messageHash for domain separation
     */
    constructor(string memory _messagePrefix) Ownable() { // Initialize Ownable (owner defaults to deployer)
        gameCounter = 0; // Start from 0, increment before assignment
        serverSignerAddress = msg.sender; // Default to deployer, should be updated
        messagePrefix = _messagePrefix;
        emit ServerSignerAddressUpdated(msg.sender);
    }

    // ===================
    // External Functions
    // ===================

    /**
     * @notice Creates a new game placeholder on-chain. Requires server signature.
     * Only gameSeedHash is needed for provable fairness.
     * @param preliminaryGameId The preliminary game ID generated by the backend
     * @param gameSeedHash Hash of the actual game seed (used for listener correlation)
     * @param algoVersion The algorithm version for provable fairness
     * @param gameConfig JSON string containing game configuration
     * @param deadline The latest timestamp this signature is valid for
     * @param serverSignature Signature from the server authorizing this game creation
     */
    function createGame(
        string calldata preliminaryGameId,
        bytes32 gameSeedHash,
        string calldata algoVersion,
        string calldata gameConfig,
        uint256 deadline,
        bytes calldata serverSignature
    ) external payable {
        require(block.timestamp <= deadline, "Signature expired");
        // Use domain separation and abi.encode for signature
        bytes32 messageHash = keccak256(
            abi.encode(
                messagePrefix,
                preliminaryGameId,
                gameSeedHash,
                algoVersion,
                gameConfig,
                msg.sender,
                msg.value,
                deadline
            )
        );
        _verifyServerSignature(messageHash, serverSignature);

        if (preliminaryToOnChainId[preliminaryGameId] != 0) {
            revert GameAlreadyExists(preliminaryGameId);
        }

        gameCounter += 1;
        uint256 onChainGameId = gameCounter;
        preliminaryToOnChainId[preliminaryGameId] = onChainGameId;

        // Create game struct with all metadata
        games[onChainGameId] = Game({
            player: msg.sender,
            betAmount: msg.value,
            gameSeedHash: gameSeedHash,
            status: GameStatus.Active,
            payoutAmount: 0,
            gameSeed: "",
            algoVersion: algoVersion,
            gameConfig: gameConfig,
            gameState: "",
            createdAt: block.timestamp
        });

        emit GameCreated(preliminaryGameId, onChainGameId, msg.sender, msg.value, gameSeedHash);
    }

    /**
     * @notice Processes a cash out. Only callable by the server signer.
     * @param onChainGameId The on-chain game ID.
     * @param payoutAmount The NET amount to pay out.
     * @param gameState JSON string containing final game state/player moves
     * @param gameSeed The final game seed to store for provable fairness
     */
    function cashOut(
        uint256 onChainGameId,
        uint256 payoutAmount,
        string calldata gameState,
        string calldata gameSeed
    ) external nonReentrant {
        // Ensure only the designated server can call this function
        require(msg.sender == serverSignerAddress, "Only server can cash out");

        Game storage game = games[onChainGameId];
        if (game.player == address(0)) {
             revert GameDoesNotExist(onChainGameId);
        }

        // Check status - This prevents cashing out already Won/Lost games
        if (game.status != GameStatus.Active) {
            revert GameNotActive(onChainGameId);
        }

        require(payoutAmount > 0, "PayoutZero");
        // Note: We trust the server signature implicitly guarantees the payoutAmount
        // and gameState correctness based on the game state known by the server.

        address playerAddress = game.player; // Store player address

        // --- EFFECTS (set state before external call) ---
        game.status = GameStatus.Won;
        game.payoutAmount = payoutAmount;
        game.gameState = gameState;
        game.gameSeed = gameSeed; // Store the game seed

        // --- INTERACTION ---
        (bool success, ) = payable(playerAddress).call{value: payoutAmount}("");
        if (!success) {
            revert PayoutFailed(onChainGameId, payoutAmount);
        }

        emit GameStatusUpdated(onChainGameId, GameStatus.Won);
        emit PayoutSent(onChainGameId, payoutAmount, playerAddress);
    }

    /**
     * @notice Mark a game as lost. Only callable by the server signer.
     * Stores the game seed for verification.
     * @param onChainGameId The on-chain game ID
     * @param gameState JSON string containing final game state/player moves
     * @param gameSeed The final game seed to store for provable fairness
     */
    function markGameAsLost(
        uint256 onChainGameId,
        string calldata gameState,
        string calldata gameSeed
    ) external {
        // Ensure only the designated server can call this function
        require(msg.sender == serverSignerAddress, "Only server can mark game lost");

        Game storage game = games[onChainGameId];
         if (game.player == address(0)) {
             revert GameDoesNotExist(onChainGameId);
         }

        if (game.status != GameStatus.Active) {
            revert GameNotActive(onChainGameId);
        }

        game.status = GameStatus.Lost;
        game.gameState = gameState;
        game.gameSeed = gameSeed; // Store the game seed

        emit GameStatusUpdated(onChainGameId, GameStatus.Lost);
    }

    /**
     * @notice Get details for a specific game
     * @param onChainGameId The on-chain game ID
     * @return Game struct with all game details
     */
    function getGameDetails(uint256 onChainGameId) external view returns (Game memory) {
         if (games[onChainGameId].player == address(0)) { // Check if game actually exists
             revert GameDoesNotExist(onChainGameId);
         }
        return games[onChainGameId];
    }

    /**
     * @notice Get on-chain ID from preliminary ID
     * @param preliminaryGameId The preliminary game ID
     * @return onChainGameId The corresponding on-chain game ID (0 if not found)
     */
    function getOnChainGameId(string calldata preliminaryGameId) external view returns (uint256) {
        return preliminaryToOnChainId[preliminaryGameId];
    }

    // ===================
    // Admin Functions
    // ===================

    /**
     * @notice Sets the address authorized to sign critical parameters.
     * @param _newAddress The new server signer address.
     */
    function setServerSignerAddress(address _newAddress) external onlyOwner {
        require(_newAddress != address(0), "Invalid server address");
        serverSignerAddress = _newAddress;
        emit ServerSignerAddressUpdated(_newAddress);
    }

    /**
     * @notice Withdraw contract funds to a specified recipient. Only callable by the owner.
     * Funds represent implicitly collected fees (Total Bets - Total Net Payouts).
     * @param amount The amount to withdraw.
     * @param recipient The address to send the withdrawn funds to.
     */
    function withdrawFunds(uint256 amount, address payable recipient) external onlyOwner nonReentrant {
        require(amount > 0, "Withdraw amount must be positive");
        require(recipient != address(0), "Invalid recipient address");
        require(amount <= address(this).balance, "Insufficient contract balance");
        (bool success, ) = recipient.call{value: amount}("");
        require(success, "Withdrawal failed");
    }

    /**
     * @notice Receive function to accept direct Ether deposits into the treasury.
     */
    receive() external payable {
        emit DepositReceived(msg.sender, msg.value);
    }

    // ===============================
    // Internal Helper Functions
    // ===============================

    /**
     * @dev Verifies that the provided signature for the given hash was generated by the serverSignerAddress.
     * Reverts with InvalidServerSignature if verification fails.
     * @param _hash The hash that was signed.
     * @param _signature The signature bytes (expected length 65).
     */
    function _verifyServerSignature(bytes32 _hash, bytes calldata _signature) internal view {
        if (serverSignerAddress == address(0)) {
             revert InvalidServerSignature(); // Server address not set
        }

        // Use OpenZeppelin's ECDSA library for robust verification
        // 1. Create the hash that is actually signed by signMessage (prefixed)
        bytes32 prefixedHash = ECDSA.toEthSignedMessageHash(_hash);

        // 2. Recover the signer address
        address recoveredSigner = ECDSA.recover(prefixedHash, _signature);

        // 3. Check if recovery was successful and matches the expected signer
        if (recoveredSigner == address(0) || recoveredSigner != serverSignerAddress) {
            revert InvalidServerSignature();
        }
    }
} 