{"compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": ".", "sourceMap": true}, "include": ["./hardhat.config.ts", "./scripts/**/*.ts", "./test/**/*.ts", "./deploy/**/*.ts", "./contracts/**/*.sol"], "exclude": ["node_modules", "cache", "artifacts", "artifacts-zk"]}