{"name": "smart-contract-accounts-viem", "description": "Work with Abstract smart contract accounts using Viem", "license": "MIT", "repository": "https://github.com/Abstract-Foundation/smarat-contract-accounts-viem", "scripts": {"deploy": "hardhat deploy-zksync --script deploy.ts", "deploy:testnet": "hardhat deploy-zksync --script deploy.ts --network abstractTestnet", "deploy:mainnet": "hardhat deploy-zksync --script deploy.ts --network abstractMainnet", "interact": "hardhat deploy-zksync --script interact.ts", "compile": "hardhat compile --network abstractTestnet && mkdir -p ../website/lib/blockchain/abi && cp ./artifacts-zk/contracts/DeathFun.sol/DeathFun.json ../website/lib/blockchain/abi/", "clean": "hardhat clean", "test": "npx hardhat test", "verify:testnet": "npx hardhat verify --network abstractTestnet", "verify:mainnet": "npx hardhat verify --network abstractMainnet", "upgrade": "npx hardhat run deploy/upgrade.ts --network hardhat", "upgrade:testnet": "npx hardhat run deploy/upgrade.ts --network abstractTestnet", "upgrade:mainnet": "npx hardhat run deploy/upgrade.ts --network abstractMainnet"}, "devDependencies": {"@matterlabs/hardhat-zksync": "^1.6.0", "@matterlabs/hardhat-zksync-deploy": "^1.7.0", "@matterlabs/zksync-contracts": "^0.6.1", "@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-ignition-viem": "^0.15.11", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox-viem": "^3.0.0", "@nomicfoundation/hardhat-viem": "^2.0.3", "@nomicfoundation/ignition-core": "^0.15.11", "@nomiclabs/hardhat-etherscan": "^3.1.7", "@openzeppelin/contracts": "^4.6.0", "@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.8", "@types/mocha": "^10.0.1", "dotenv": "^16.5.0", "hardhat": "^2.12.4", "hardhat-gas-reporter": "^1.0.10", "mocha": "^10.2.0", "solidity-coverage": "^0.8.15", "ts-node": "^10.9.1", "typescript": "^4.9.5", "viem": "^2.20.0"}, "dependencies": {"@openzeppelin/contracts-upgradeable": "^5.3.0", "@openzeppelin/hardhat-upgrades": "^3.9.0", "chai": "^4.3.0"}}