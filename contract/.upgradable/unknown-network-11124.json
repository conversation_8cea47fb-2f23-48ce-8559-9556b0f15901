{"manifestVersion": "3.2", "proxies": [{"address": "0x240675690d116888Db0803BD96ee96Fe61A7AFf3", "txHash": "0xafeecb4a6e5182ecd54240d603afc0e32cbdd10d94de34b5925bf3966d184bfa", "kind": "transparent"}, {"address": "0x1b9b952a9b83187dfd89305e9c22630D03Ccae60", "txHash": "0x1bfd13e501424e2f4bf7742aeb65b5f56fc19dabc83728ac7cf401868ee10824", "kind": "transparent"}, {"address": "0x4B7e1469958d88275DC04eD2098E2FCF0Aa0166b", "txHash": "0x993e8fdabe92ffc91d49a37f77f2bbd987cb60fd02314dd55516c64e01dd80d3", "kind": "transparent"}], "impls": {"b09d59772ab7deabbb2abf1a096146b9fb7a74c55b0929250308c25eee89de03": {"address": "0x078880e03B9f7bDCBAeE6DbA740E61CD49DC346D", "txHash": "0xbf6cf2e4a03007a2e46dc81da00a78651548d0a4c9f478a07006ad1d8bcc9ead", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "gameCounter", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "DeathFun", "src": "contracts/DeathFun.sol:27"}, {"label": "messagePrefix", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "DeathFun", "src": "contracts/DeathFun.sol:30"}, {"label": "isAdmin", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_bool)", "contract": "DeathFun", "src": "contracts/DeathFun.sol:33"}, {"label": "preliminaryToOnChainId", "offset": 0, "slot": "3", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "contract": "DeathFun", "src": "contracts/DeathFun.sol:36"}, {"label": "games", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_struct(Game)2264_storage)", "contract": "DeathFun", "src": "contracts/DeathFun.sol:60"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_enum(GameStatus)2241": {"label": "enum DeathFun.GameStatus", "members": ["Active", "Won", "Lost"], "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Game)2264_storage)": {"label": "mapping(uint256 => struct DeathFun.Game)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Game)2264_storage": {"label": "struct DeathFun.Game", "members": [{"label": "createdAt", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "player", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "betAmount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "status", "type": "t_enum(GameStatus)2241", "offset": 0, "slot": "3"}, {"label": "payoutAmount", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "gameSeedHash", "type": "t_bytes32", "offset": 0, "slot": "5"}, {"label": "gameSeed", "type": "t_string_storage", "offset": 0, "slot": "6"}, {"label": "algoVersion", "type": "t_string_storage", "offset": 0, "slot": "7"}, {"label": "gameConfig", "type": "t_string_storage", "offset": 0, "slot": "8"}, {"label": "gameState", "type": "t_string_storage", "offset": 0, "slot": "9"}], "numberOfBytes": "320"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}, "51a9ce99d161aebca09d147f5ff28186db184ca927b6f919a878be48ee45989e": {"address": "0xE0b60669346bF450c78165c287991DC219B51516", "txHash": "0x193e987a1b6358b602e5450c33094bd3947301e95b49dfc622dd306e183a6633", "layout": {"solcVersion": "0.8.24", "storage": [{"contract": "DeathFunV2", "label": "gameCounter", "type": "t_uint256", "src": "contracts/DeathFunV2.sol:27"}, {"contract": "DeathFunV2", "label": "messagePrefix", "type": "t_string_storage", "src": "contracts/DeathFunV2.sol:30"}, {"contract": "DeathFunV2", "label": "isAdmin", "type": "t_mapping(t_address,t_bool)", "src": "contracts/DeathFunV2.sol:33"}, {"contract": "DeathFunV2", "label": "preliminaryToOnChainId", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "src": "contracts/DeathFunV2.sol:36"}, {"contract": "DeathFunV2", "label": "games", "type": "t_mapping(t_uint256,t_struct(Game)3101_storage)", "src": "contracts/DeathFunV2.sol:39"}, {"contract": "DeathFunV2", "label": "__gap", "type": "t_array(t_uint256)50_storage", "src": "contracts/DeathFunV2.sol:42"}], "types": {"t_uint256": {"label": "uint256"}, "t_string_storage": {"label": "string"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)"}, "t_address": {"label": "address"}, "t_bool": {"label": "bool"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)"}, "t_mapping(t_uint256,t_struct(Game)3101_storage)": {"label": "mapping(uint256 => struct DeathFunV2.Game)"}, "t_struct(Game)3101_storage": {"label": "struct DeathFunV2.Game", "members": [{"label": "createdAt", "type": "t_uint256"}, {"label": "player", "type": "t_address"}, {"label": "betAmount", "type": "t_uint256"}, {"label": "status", "type": "t_enum(GameStatus)3106"}, {"label": "payoutAmount", "type": "t_uint256"}, {"label": "gameSeedHash", "type": "t_bytes32"}, {"label": "gameSeed", "type": "t_string_storage"}, {"label": "algoVersion", "type": "t_string_storage"}, {"label": "gameConfig", "type": "t_string_storage"}, {"label": "gameState", "type": "t_string_storage"}]}, "t_enum(GameStatus)3106": {"label": "enum DeathFunV2.GameStatus", "members": ["Active", "Won", "Lost"]}, "t_bytes32": {"label": "bytes32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}, "1bd10c9846f356cbaa6ddad7f9f9c477594c4c1449153c7e3c2457ff24d7984e": {"address": "0x5CfBD706c34DC29FBd9938c4D56ccF64e81D9991", "txHash": "0xe412637d50b2e22ed23591d37cc73465f259041ed0a15bb827a6aee51bc67e9e", "layout": {"solcVersion": "0.8.24", "storage": [{"contract": "DeathFun", "label": "gameCounter", "type": "t_uint256", "src": "contracts/DeathFun.sol:27"}, {"contract": "DeathFun", "label": "messagePrefix", "type": "t_string_storage", "src": "contracts/DeathFun.sol:30"}, {"contract": "DeathFun", "label": "isAdmin", "type": "t_mapping(t_address,t_bool)", "src": "contracts/DeathFun.sol:33"}, {"contract": "DeathFun", "label": "preliminaryToOnChainId", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "src": "contracts/DeathFun.sol:36"}, {"contract": "DeathFun", "label": "games", "type": "t_mapping(t_uint256,t_struct(Game)2264_storage)", "src": "contracts/DeathFun.sol:60"}, {"contract": "DeathFun", "label": "__gap", "type": "t_array(t_uint256)50_storage", "src": "contracts/DeathFun.sol:63"}], "types": {"t_uint256": {"label": "uint256"}, "t_string_storage": {"label": "string"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)"}, "t_address": {"label": "address"}, "t_bool": {"label": "bool"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)"}, "t_mapping(t_uint256,t_struct(Game)2264_storage)": {"label": "mapping(uint256 => struct DeathFun.Game)"}, "t_struct(Game)2264_storage": {"label": "struct DeathFun.Game", "members": [{"label": "createdAt", "type": "t_uint256"}, {"label": "player", "type": "t_address"}, {"label": "betAmount", "type": "t_uint256"}, {"label": "status", "type": "t_enum(GameStatus)2241"}, {"label": "payoutAmount", "type": "t_uint256"}, {"label": "gameSeedHash", "type": "t_bytes32"}, {"label": "gameSeed", "type": "t_string_storage"}, {"label": "algoVersion", "type": "t_string_storage"}, {"label": "gameConfig", "type": "t_string_storage"}, {"label": "gameState", "type": "t_string_storage"}]}, "t_enum(GameStatus)2241": {"label": "enum DeathFun.GameStatus", "members": ["Active", "Won", "Lost"]}, "t_bytes32": {"label": "bytes32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}, "d06305fdf04ccdaf15d17792079ba3d9f8b7911c19410384ae2c25c7cf65e296": {"address": "0xeE9Ffd1832D6BAc52B9007d52044822350579bE8", "txHash": "0x37bfb0eaff2e8556f8dfefca6a7c042e68dee803445ec49ceb9ca95843ef98c6", "layout": {"solcVersion": "0.8.24", "storage": [{"contract": "DeathFun", "label": "gameCounter", "type": "t_uint256", "src": "contracts/DeathFun.sol:27"}, {"contract": "DeathFun", "label": "messagePrefix", "type": "t_string_storage", "src": "contracts/DeathFun.sol:30"}, {"contract": "DeathFun", "label": "isAdmin", "type": "t_mapping(t_address,t_bool)", "src": "contracts/DeathFun.sol:33"}, {"contract": "DeathFun", "label": "preliminaryToOnChainId", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "src": "contracts/DeathFun.sol:36"}, {"contract": "DeathFun", "label": "games", "type": "t_mapping(t_uint256,t_struct(Game)2264_storage)", "src": "contracts/DeathFun.sol:60"}, {"contract": "DeathFun", "label": "__gap", "type": "t_array(t_uint256)50_storage", "src": "contracts/DeathFun.sol:63"}], "types": {"t_uint256": {"label": "uint256"}, "t_string_storage": {"label": "string"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)"}, "t_address": {"label": "address"}, "t_bool": {"label": "bool"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)"}, "t_mapping(t_uint256,t_struct(Game)2264_storage)": {"label": "mapping(uint256 => struct DeathFun.Game)"}, "t_struct(Game)2264_storage": {"label": "struct DeathFun.Game", "members": [{"label": "createdAt", "type": "t_uint256"}, {"label": "player", "type": "t_address"}, {"label": "betAmount", "type": "t_uint256"}, {"label": "status", "type": "t_enum(GameStatus)2241"}, {"label": "payoutAmount", "type": "t_uint256"}, {"label": "gameSeedHash", "type": "t_bytes32"}, {"label": "gameSeed", "type": "t_string_storage"}, {"label": "algoVersion", "type": "t_string_storage"}, {"label": "gameConfig", "type": "t_string_storage"}, {"label": "gameState", "type": "t_string_storage"}]}, "t_enum(GameStatus)2241": {"label": "enum DeathFun.GameStatus", "members": ["Active", "Won", "Lost"]}, "t_bytes32": {"label": "bytes32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}}}