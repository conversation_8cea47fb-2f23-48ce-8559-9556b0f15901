{"manifestVersion": "3.2", "proxies": [{"address": "0x27EDd16eE56958fddCBA08947f12C43DDeC2B20C", "txHash": "0xf78fae88c2a24db9bb4da670e1c0de0514078994f033af8cdb7f4b7e922afc68", "kind": "transparent"}], "impls": {"1bd10c9846f356cbaa6ddad7f9f9c477594c4c1449153c7e3c2457ff24d7984e": {"address": "0xA0CecFbf51b80e73Cd2A27494dE6949Ac0B67927", "txHash": "0xfa7aca4b5e3098847a6ab27839c592cdaf6ae8cc321078489bf51739c805be3c", "layout": {"solcVersion": "0.8.24", "storage": [{"contract": "DeathFun", "label": "gameCounter", "type": "t_uint256", "src": "contracts/DeathFun.sol:27"}, {"contract": "DeathFun", "label": "messagePrefix", "type": "t_string_storage", "src": "contracts/DeathFun.sol:30"}, {"contract": "DeathFun", "label": "isAdmin", "type": "t_mapping(t_address,t_bool)", "src": "contracts/DeathFun.sol:33"}, {"contract": "DeathFun", "label": "preliminaryToOnChainId", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "src": "contracts/DeathFun.sol:36"}, {"contract": "DeathFun", "label": "games", "type": "t_mapping(t_uint256,t_struct(Game)2264_storage)", "src": "contracts/DeathFun.sol:60"}, {"contract": "DeathFun", "label": "__gap", "type": "t_array(t_uint256)50_storage", "src": "contracts/DeathFun.sol:63"}], "types": {"t_uint256": {"label": "uint256"}, "t_string_storage": {"label": "string"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)"}, "t_address": {"label": "address"}, "t_bool": {"label": "bool"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)"}, "t_mapping(t_uint256,t_struct(Game)2264_storage)": {"label": "mapping(uint256 => struct DeathFun.Game)"}, "t_struct(Game)2264_storage": {"label": "struct DeathFun.Game", "members": [{"label": "createdAt", "type": "t_uint256"}, {"label": "player", "type": "t_address"}, {"label": "betAmount", "type": "t_uint256"}, {"label": "status", "type": "t_enum(GameStatus)2241"}, {"label": "payoutAmount", "type": "t_uint256"}, {"label": "gameSeedHash", "type": "t_bytes32"}, {"label": "gameSeed", "type": "t_string_storage"}, {"label": "algoVersion", "type": "t_string_storage"}, {"label": "gameConfig", "type": "t_string_storage"}, {"label": "gameState", "type": "t_string_storage"}]}, "t_enum(GameStatus)2241": {"label": "enum DeathFun.GameStatus", "members": ["Active", "Won", "Lost"]}, "t_bytes32": {"label": "bytes32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}, "d06305fdf04ccdaf15d17792079ba3d9f8b7911c19410384ae2c25c7cf65e296": {"address": "0xaA2F22f5481B432FBC11F7a0448375fB2D96d20a", "txHash": "0xf2f1783fc2e2ebcc825ee55ceef5b7a20cfe862e4f56135b13b38aadbbbcf803", "layout": {"solcVersion": "0.8.24", "storage": [{"contract": "DeathFun", "label": "gameCounter", "type": "t_uint256", "src": "contracts/DeathFun.sol:27"}, {"contract": "DeathFun", "label": "messagePrefix", "type": "t_string_storage", "src": "contracts/DeathFun.sol:30"}, {"contract": "DeathFun", "label": "isAdmin", "type": "t_mapping(t_address,t_bool)", "src": "contracts/DeathFun.sol:33"}, {"contract": "DeathFun", "label": "preliminaryToOnChainId", "type": "t_mapping(t_string_memory_ptr,t_uint256)", "src": "contracts/DeathFun.sol:36"}, {"contract": "DeathFun", "label": "games", "type": "t_mapping(t_uint256,t_struct(Game)2264_storage)", "src": "contracts/DeathFun.sol:60"}, {"contract": "DeathFun", "label": "__gap", "type": "t_array(t_uint256)50_storage", "src": "contracts/DeathFun.sol:63"}], "types": {"t_uint256": {"label": "uint256"}, "t_string_storage": {"label": "string"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)"}, "t_address": {"label": "address"}, "t_bool": {"label": "bool"}, "t_mapping(t_string_memory_ptr,t_uint256)": {"label": "mapping(string => uint256)"}, "t_mapping(t_uint256,t_struct(Game)2264_storage)": {"label": "mapping(uint256 => struct DeathFun.Game)"}, "t_struct(Game)2264_storage": {"label": "struct DeathFun.Game", "members": [{"label": "createdAt", "type": "t_uint256"}, {"label": "player", "type": "t_address"}, {"label": "betAmount", "type": "t_uint256"}, {"label": "status", "type": "t_enum(GameStatus)2241"}, {"label": "payoutAmount", "type": "t_uint256"}, {"label": "gameSeedHash", "type": "t_bytes32"}, {"label": "gameSeed", "type": "t_string_storage"}, {"label": "algoVersion", "type": "t_string_storage"}, {"label": "gameConfig", "type": "t_string_storage"}, {"label": "gameState", "type": "t_string_storage"}]}, "t_enum(GameStatus)2241": {"label": "enum DeathFun.GameStatus", "members": ["Active", "Won", "Lost"]}, "t_bytes32": {"label": "bytes32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]"}, "t_uint64": {"label": "uint64"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73"}]}}}}}